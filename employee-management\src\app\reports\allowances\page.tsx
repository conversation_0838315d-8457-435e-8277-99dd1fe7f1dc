'use client';

import { useState } from 'react';
import { 
  <PERSON>, 
  CardHeader, 
  CardBody, 
  Button, 
  Table, 
  TableHeader, 
  TableColumn, 
  TableBody, 
  TableRow, 
  TableCell,
  Input,
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem
} from '@nextui-org/react';
import { FaSearch, FaEllipsisV, FaEye, FaPrint, FaFileExport } from 'react-icons/fa';

// نموذج بيانات العلاوة المستحقة
type AllowanceReport = {
  id: string;
  employeeName: string;
  employeeNumber: string;
  jobTitle: string;
  workLocation: string;
  lastAllowanceDate: string;
  nextAllowanceDate: string;
  daysRemaining: number;
};

export default function AllowanceReportPage() {
  const [searchQuery, setSearchQuery] = useState('');
  
  // بيانات تجريبية للتقرير
  const allowances: AllowanceReport[] = [
    {
      id: '1',
      employeeName: 'أحمد محمد علي',
      employeeNumber: 'EMP001',
      jobTitle: 'مهندس برمجيات',
      workLocation: 'المقر الرئيسي',
      lastAllowanceDate: '2024-05-15',
      nextAllowanceDate: '2025-05-15',
      daysRemaining: 10,
    },
    {
      id: '2',
      employeeName: 'سارة أحمد محمود',
      employeeNumber: 'EMP002',
      jobTitle: 'محاسب',
      workLocation: 'الفرع الأول',
      lastAllowanceDate: '2024-05-20',
      nextAllowanceDate: '2025-05-20',
      daysRemaining: 15,
    },
    {
      id: '3',
      employeeName: 'محمد خالد عبدالله',
      employeeNumber: 'EMP003',
      jobTitle: 'مدير مبيعات',
      workLocation: 'الفرع الثاني',
      lastAllowanceDate: '2024-06-05',
      nextAllowanceDate: '2025-06-05',
      daysRemaining: 30,
    },
    {
      id: '4',
      employeeName: 'فاطمة علي حسن',
      employeeNumber: 'EMP004',
      jobTitle: 'مسؤول موارد بشرية',
      workLocation: 'المقر الرئيسي',
      lastAllowanceDate: '2024-06-20',
      nextAllowanceDate: '2025-06-20',
      daysRemaining: 45,
    },
    {
      id: '5',
      employeeName: 'خالد عبدالرحمن',
      employeeNumber: 'EMP005',
      jobTitle: 'مهندس شبكات',
      workLocation: 'الفرع الثالث',
      lastAllowanceDate: '2024-06-25',
      nextAllowanceDate: '2025-06-25',
      daysRemaining: 50,
    },
  ];

  // تصفية البيانات بناءً على البحث
  const filteredAllowances = allowances.filter(
    (allowance) =>
      allowance.employeeName.includes(searchQuery) ||
      allowance.employeeNumber.includes(searchQuery) ||
      allowance.jobTitle.includes(searchQuery) ||
      allowance.workLocation.includes(searchQuery)
  );

  // دالة لتنسيق التاريخ
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA');
  };

  // دالة لتحديد لون الخلية بناءً على عدد الأيام المتبقية
  const getDaysRemainingColor = (days: number) => {
    if (days <= 15) return 'text-red-500 font-bold';
    if (days <= 30) return 'text-amber-500 font-bold';
    return 'text-green-500';
  };

  return (
    <div>
      <Card className="shadow-md">
        <CardHeader className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">تقرير العلاوات المستحقة</h1>
          <div className="flex gap-2">
            <Button color="primary" startContent={<FaPrint />}>
              طباعة
            </Button>
            <Button color="success" startContent={<FaFileExport />}>
              تصدير Excel
            </Button>
          </div>
        </CardHeader>
        <CardBody>
          <div className="mb-4">
            <Input
              placeholder="البحث..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              startContent={<FaSearch className="text-gray-400" />}
              className="max-w-md"
            />
          </div>

          <Table aria-label="جدول العلاوات المستحقة">
            <TableHeader>
              <TableColumn>الاسم</TableColumn>
              <TableColumn>الرقم الوظيفي</TableColumn>
              <TableColumn>العنوان الوظيفي</TableColumn>
              <TableColumn>موقع العمل</TableColumn>
              <TableColumn>تاريخ آخر علاوة</TableColumn>
              <TableColumn>تاريخ العلاوة القادمة</TableColumn>
              <TableColumn>الأيام المتبقية</TableColumn>
              <TableColumn>الإجراءات</TableColumn>
            </TableHeader>
            <TableBody>
              {filteredAllowances.map((allowance) => (
                <TableRow key={allowance.id}>
                  <TableCell>{allowance.employeeName}</TableCell>
                  <TableCell>{allowance.employeeNumber}</TableCell>
                  <TableCell>{allowance.jobTitle}</TableCell>
                  <TableCell>{allowance.workLocation}</TableCell>
                  <TableCell>{formatDate(allowance.lastAllowanceDate)}</TableCell>
                  <TableCell>{formatDate(allowance.nextAllowanceDate)}</TableCell>
                  <TableCell className={getDaysRemainingColor(allowance.daysRemaining)}>
                    {allowance.daysRemaining}
                  </TableCell>
                  <TableCell>
                    <Dropdown>
                      <DropdownTrigger>
                        <Button isIconOnly variant="light">
                          <FaEllipsisV />
                        </Button>
                      </DropdownTrigger>
                      <DropdownMenu aria-label="إجراءات">
                        <DropdownItem
                          key="view"
                          startContent={<FaEye className="text-blue-500" />}
                        >
                          عرض تفاصيل الموظف
                        </DropdownItem>
                        <DropdownItem
                          key="print"
                          startContent={<FaPrint className="text-green-500" />}
                        >
                          طباعة
                        </DropdownItem>
                      </DropdownMenu>
                    </Dropdown>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardBody>
      </Card>
    </div>
  );
}
