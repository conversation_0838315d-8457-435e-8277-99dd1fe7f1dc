<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فتح ملف موظف - برنامج إدارة العلاوات والترفيعات</title>
    <link rel="stylesheet" href="styles.css">
    <!-- إضافة خط Cairo من Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <!-- إضافة Font Awesome للأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- إضا<PERSON>ة مكتبة Select2 للقوائم المنسدلة مع خاصية البحث -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <!-- إضافة أنماط خاصة بصفحة الموظف -->
    <link rel="stylesheet" href="employee-form.css">
    <!-- إضافة أنماط النوافذ المنبثقة -->
    <link rel="stylesheet" href="modals.css">
    <!-- إضافة أنماط الإشعارات -->
    <link rel="stylesheet" href="notifications.css">
    <style>
        /* أنماط الأقسام */
        .form-section {
            margin-bottom: 30px;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            background-color: #f9f9f9;
        }

        .section-title {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            margin: -20px -20px 20px -20px;
            border-radius: 5px 5px 0 0;
            font-weight: bold;
            font-size: 1.2em;
            display: flex;
            align-items: center;
        }

        .section-title i {
            margin-left: 10px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-actions {
            margin-top: 20px;
            text-align: center;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s;
            margin: 0 5px;
        }

        .btn-primary {
            background-color: #4CAF50;
            color: white;
        }

        .btn-primary:hover {
            background-color: #45a049;
        }

        .btn-secondary {
            background-color: #f1f1f1;
            color: #333;
        }

        .btn-secondary:hover {
            background-color: #ddd;
        }

        /* أنماط Select2 */
        .select2-container--default .select2-selection--single {
            height: 38px;
            border: 1px solid #ced4da;
            border-radius: 4px;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 38px;
            padding-right: 12px;
            padding-left: 20px;
            text-align: right;
        }

        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: 36px;
            left: 1px;
            right: auto;
        }

        .select2-container--default .select2-results__option {
            text-align: right;
        }

        .select2-container--default .select2-search--dropdown .select2-search__field {
            text-align: right;
            direction: rtl;
        }

        .select2-dropdown {
            border: 1px solid #ced4da;
        }

        .select2-container--default .select2-results__option--highlighted[aria-selected] {
            background-color: #4CAF50;
        }

        .text-success {
            color: #4CAF50;
        }

        .text-danger {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <header>
        <nav>
            <div class="logo">
                <i class="fas fa-chart-line logo-icon"></i>
                برنامج إدارة العلاوات والترفيعات
            </div>
            <div class="nav-links">
                <div class="dropdown">
                    <a href="index.html"><i class="fas fa-home"></i> الرئيسية</a>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-id-card"></i> الملفات التعريفية <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="work-locations.html"><i class="fas fa-building"></i> مواقع العمل</a>
                        <a href="job-titles.html"><i class="fas fa-user-tie"></i> العناوين الوظيفية</a>
                        <a href="education-levels.html"><i class="fas fa-graduation-cap"></i> التحصيل الدراسي</a>
                        <a href="leaves.html"><i class="fas fa-calendar-alt"></i> الاجازات</a>
                        <a href="alert-periods.html"><i class="fas fa-bell"></i> فترة التنبيه</a>
                    </div>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle active"><i class="fas fa-users"></i> الموظفين <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="employee-form.html" class="active"><i class="fas fa-user-plus"></i> فتح ملف موظف</a>
                        <a href="employees-list.html"><i class="fas fa-list"></i> عرض الموظفين</a>
                    </div>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-medal"></i> التشكرات والعقوبات <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="thanks.html"><i class="fas fa-award"></i> التشكرات</a>
                        <a href="penalties.html"><i class="fas fa-exclamation-triangle"></i> العقوبات</a>
                    </div>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-calendar-alt"></i> الاجازات والغيابات <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="employee-leaves.html"><i class="fas fa-calendar-check"></i> الاجازات</a>
                        <a href="absences.html"><i class="fas fa-calendar-times"></i> الغيابات</a>
                    </div>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-chart-bar"></i> التقارير <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="#"><i class="fas fa-file-alt"></i> تقرير العلاوات المستحقة</a>
                        <a href="#"><i class="fas fa-file-alt"></i> تقرير الترفيعات المستحقة</a>
                        <a href="#"><i class="fas fa-user-clock"></i> قرب الاحالة الى التقاعد</a>
                        <a href="salary-scale.html"><i class="fas fa-money-bill-wave"></i> سلم الرواتب</a>
                    </div>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-bell"></i> التنبيهات <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="allowance-alerts.html"><i class="fas fa-bell"></i> تنبيه العلاوات المستحقة</a>
                        <a href="promotion-alerts.html"><i class="fas fa-bell"></i> تنبيه الترفيعات المستحقة</a>
                        <a href="retirement-alerts.html"><i class="fas fa-bell"></i> تنبيه الاحالة الى التقاعد</a>
                    </div>
                </div>
            </div>
            <div class="menu-toggle" id="menuToggle">
                <i class="fas fa-bars"></i>
            </div>
        </nav>
    </header>

    <main>
        <div class="employee-form-container">
            <h1 class="form-title">ملف الموظف</h1>

            <form id="employeeForm" class="employee-form">
                <!-- قسم المعلومات الأساسية -->
                <div class="form-section">
                    <div class="section-title">
                        <i class="fas fa-user"></i> المعلومات الأساسية
                    </div>
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="employeeId">الرقم الوظيفي: <span class="required">*</span></label>
                            <input type="text" id="employeeId" name="employeeId" required>
                        </div>

                        <div class="form-group">
                            <label for="fullName">الاسم الرباعي: <span class="required">*</span></label>
                            <input type="text" id="fullName" name="fullName" required>
                        </div>

                        <div class="form-group">
                            <label for="jobDescription">الوصف الوظيفي: <span class="required">*</span></label>
                            <select id="jobDescription" name="jobDescription" required>
                                <option value="">اختر الوصف الوظيفي</option>
                                <option value="teaching">تدريسي</option>
                                <option value="technical">فني</option>
                                <option value="administrative">اداري</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="jobTitle">العنوان الوظيفي: <span class="required">*</span></label>
                            <select id="jobTitle" name="jobTitle" required>
                                <option value="">اختر العنوان الوظيفي</option>
                                <!-- سيتم تحميل الخيارات ديناميكياً -->
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="education">التحصيل الدراسي: <span class="required">*</span></label>
                            <select id="education" name="education" required>
                                <option value="">اختر التحصيل الدراسي</option>
                                <!-- سيتم تحميل الخيارات ديناميكياً -->
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="workLocation">موقع العمل: <span class="required">*</span></label>
                            <select id="workLocation" name="workLocation" required>
                                <option value="">اختر موقع العمل</option>
                                <!-- سيتم تحميل الخيارات ديناميكياً -->
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="birthDate">تاريخ التولد: <span class="required">*</span></label>
                            <input type="date" id="birthDate" name="birthDate" required>
                        </div>

                        <div class="form-group">
                            <label for="hireDate">تاريخ التعيين: <span class="required">*</span></label>
                            <input type="date" id="hireDate" name="hireDate" required>
                        </div>



                        <div class="form-group">
                            <label for="retirementDate">تاريخ الإحالة على التقاعد:</label>
                            <input type="date" id="retirementDate" name="retirementDate" readonly>
                        </div>
                    </div>
                </div>

                <!-- قسم العلاوات -->
                <div class="form-section">
                    <div class="section-title">
                        <i class="fas fa-money-bill-wave"></i> العلاوات
                    </div>
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="currentDegree">الدرجة الحالية: <span class="required">*</span></label>
                            <input type="number" id="currentDegree" name="currentDegree" min="1" max="10" required>
                        </div>

                        <div class="form-group">
                            <label for="currentStage">المرحلة الحالية: <span class="required">*</span></label>
                            <input type="number" id="currentStage" name="currentStage" min="1" max="11" required>
                        </div>

                        <div class="form-group">
                            <label for="currentSalary">الراتب الحالي:</label>
                            <input type="text" id="currentSalary" name="currentSalary" readonly>
                        </div>

                        <div class="form-group">
                            <label for="currentDueDate">تاريخ الاستحقاق الحالي: <span class="required">*</span></label>
                            <input type="date" id="currentDueDate" name="currentDueDate" required>
                        </div>

                        <div class="form-group">
                            <label for="allowanceSeniority">قدم العلاوة (بالأشهر): <span class="required">*</span></label>
                            <input type="number" id="allowanceSeniority" name="allowanceSeniority" min="0" max="120" required>
                            <small class="form-text text-muted">أدخل عدد أشهر قدم العلاوة (من 0 إلى 120 شهر)</small>
                        </div>

                        <div class="form-group">
                            <label for="newDegree">الدرجة الجديدة:</label>
                            <input type="number" id="newDegree" name="newDegree" readonly>
                        </div>

                        <div class="form-group">
                            <label for="newStage">المرحلة الجديدة:</label>
                            <input type="number" id="newStage" name="newStage" readonly>
                        </div>

                        <div class="form-group">
                            <label for="newSalary">الراتب الجديد:</label>
                            <input type="text" id="newSalary" name="newSalary" readonly>
                        </div>

                        <div class="form-group">
                            <label for="newDueDate">تاريخ الاستحقاق الجديد:</label>
                            <input type="date" id="newDueDate" name="newDueDate" readonly>
                        </div>
                    </div>
                </div>

                <!-- قسم الترفيعات -->
                <div class="form-section">
                    <div class="section-title">
                        <i class="fas fa-arrow-up"></i> الترفيعات
                    </div>
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="lastPromotionDate">تاريخ آخر ترفيع: <span class="required">*</span></label>
                            <input type="date" id="lastPromotionDate" name="lastPromotionDate" required>
                        </div>

                        <div class="form-group">
                            <label for="promotionSeniority">قدم الترفيع (بالأشهر): <span class="required">*</span></label>
                            <input type="number" id="promotionSeniority" name="promotionSeniority" min="0" max="120" required>
                            <small class="form-text text-muted">أدخل عدد أشهر قدم الترفيع (من 0 إلى 120 شهر)</small>
                        </div>

                        <div class="form-group">
                            <label for="newJobTitle">العنوان الوظيفي الجديد:</label>
                            <input type="text" id="newJobTitle" name="newJobTitle" readonly>
                            <small class="form-text text-muted">يتم تحديده تلقائياً بناءً على العنوان الوظيفي الحالي</small>
                        </div>

                        <div class="form-group">
                            <label for="nextPromotionDate">تاريخ الترفيع القادم:</label>
                            <input type="date" id="nextPromotionDate" name="nextPromotionDate" readonly>
                        </div>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">حفظ</button>
                    <button type="reset" class="btn btn-secondary">إعادة تعيين</button>
                </div>
            </form>
        </div>
    </main>

    <footer>
        <div class="footer-content">
            <div class="footer-logo">
                <i class="fas fa-chart-line"></i> برنامج إدارة العلاوات والترفيعات
            </div>
            <p>جميع الحقوق محفوظة &copy; 2025 - تم إنشاء النظام بواسطة الفني حسن علي موسى</p>
        </div>
    </footer>

    <div class="dark-mode-toggle" id="darkModeToggle">
        <i class="fas fa-moon"></i>
    </div>

    <!-- إضافة jQuery (مطلوب لـ Select2) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- إضافة مكتبة Select2 -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <!-- إضافة ملف الترجمة العربية لـ Select2 -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/i18n/ar.js"></script>

    <script src="script.js"></script>
    <script src="employee-form.js"></script>
    <script src="promotion-rules.js"></script>

    <!-- تهيئة Select2 للقوائم المنسدلة -->
    <script>
        $(document).ready(function() {
            // تطبيق Select2 على جميع القوائم المنسدلة
            $('#jobDescription, #jobTitle, #education, #workLocation').select2({
                language: "ar",
                dir: "rtl",
                width: '100%',
                placeholder: "اختر...",
                allowClear: true
            });

            // إعادة تهيئة Select2 عند تغيير الوصف الوظيفي
            $('#jobDescription').on('change', function() {
                setTimeout(function() {
                    $('#jobTitle').select2({
                        language: "ar",
                        dir: "rtl",
                        width: '100%',
                        placeholder: "اختر العنوان الوظيفي",
                        allowClear: true
                    });
                }, 100);
            });
        });
    </script>
</body>
</html>
