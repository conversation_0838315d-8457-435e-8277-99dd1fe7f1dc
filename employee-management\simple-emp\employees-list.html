<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض الموظفين - برنامج إدارة العلاوات والترفيعات</title>
    <link rel="stylesheet" href="styles.css">
    <!-- إضافة خط Cairo من Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <!-- إضافة Font Awesome للأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- إضافة أنماط خاصة بصفحة عرض الموظفين -->
    <link rel="stylesheet" href="employees-list.css">
    <!-- إضافة أنماط النوافذ المنبثقة -->
    <link rel="stylesheet" href="modals.css">
    <!-- إضافة أنماط الإشعارات -->
    <link rel="stylesheet" href="notifications.css">
    <!-- إضافة أنماط النماذج الحديثة -->
    <link rel="stylesheet" href="modern-form.css">
</head>
<body>
    <header>
        <nav>
            <div class="logo">
                <i class="fas fa-chart-line logo-icon"></i>
                برنامج إدارة العلاوات والترفيعات
            </div>
            <div class="nav-links">
                <div class="dropdown">
                    <a href="index.html"><i class="fas fa-home"></i> الرئيسية</a>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-id-card"></i> الملفات التعريفية <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="work-locations.html"><i class="fas fa-building"></i> مواقع العمل</a>
                        <a href="job-titles.html"><i class="fas fa-user-tie"></i> العناوين الوظيفية</a>
                        <a href="education-levels.html"><i class="fas fa-graduation-cap"></i> التحصيل الدراسي</a>
                        <a href="leaves.html"><i class="fas fa-calendar-alt"></i> الاجازات</a>
                        <a href="alert-periods.html"><i class="fas fa-bell"></i> فترة التنبيه</a>
                    </div>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle active"><i class="fas fa-users"></i> الموظفين <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="employee-form.html"><i class="fas fa-user-plus"></i> فتح ملف موظف</a>
                        <a href="employees-list.html" class="active"><i class="fas fa-list"></i> عرض الموظفين</a>
                    </div>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-medal"></i> التشكرات والعقوبات <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="add-thanks.html"><i class="fas fa-plus"></i> إضافة شكر وتقدير</a>
                        <a href="penalties.html"><i class="fas fa-exclamation-triangle"></i> العقوبات</a>
                    </div>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-calendar-alt"></i> الاجازات والغيابات <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="employee-leaves.html"><i class="fas fa-calendar-check"></i> الاجازات</a>
                        <a href="absences.html"><i class="fas fa-calendar-times"></i> الغيابات</a>
                    </div>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-chart-bar"></i> التقارير <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="allowance-reports.html"><i class="fas fa-file-alt"></i> تقرير العلاوات المستحقة</a>
                        <a href="promotion-reports.html"><i class="fas fa-file-alt"></i> تقرير الترفيعات المستحقة</a>
                        <a href="retirement-reports.html"><i class="fas fa-user-clock"></i> قرب الاحالة الى التقاعد</a>
                    </div>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-bell"></i> التنبيهات <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="allowance-alerts.html"><i class="fas fa-bell"></i> تنبيه العلاوات المستحقة</a>
                        <a href="promotion-alerts.html"><i class="fas fa-bell"></i> تنبيه الترفيعات المستحقة</a>
                        <a href="retirement-alerts.html"><i class="fas fa-bell"></i> تنبيه الاحالة الى التقاعد</a>
                    </div>
                </div>
            </div>
            <div class="menu-toggle" id="menuToggle">
                <i class="fas fa-bars"></i>
            </div>
        </nav>
    </header>

    <main>
        <div class="employees-container">
            <h1 class="page-title">قائمة الموظفين</h1>

            <div class="filters-section">
                <div class="search-container">
                    <input type="text" id="searchEmployee" placeholder="بحث عن موظف..." class="search-input">
                    <button class="search-btn"><i class="fas fa-search"></i></button>
                </div>

                <div class="filters">
                    <div class="filter-group">
                        <label for="filterJobDescription">الوصف الوظيفي:</label>
                        <select id="filterJobDescription" class="filter-select">
                            <option value="">الكل</option>
                            <option value="teaching">تدريسي</option>
                            <option value="technical">فني</option>
                            <option value="administrative">اداري</option>
                        </select>
                    </div>

                    <div class="filter-group">
                        <label for="filterWorkLocation">موقع العمل:</label>
                        <select id="filterWorkLocation" class="filter-select">
                            <option value="">الكل</option>
                            <!-- سيتم ملء هذا الجزء ديناميكياً -->
                        </select>
                    </div>

                    <div class="filter-group">
                        <label for="filterStatus">الحالة:</label>
                        <select id="filterStatus" class="filter-select">
                            <option value="">الكل</option>
                            <option value="active">فعال</option>
                            <option value="retired">متقاعد</option>
                            <option value="suspended">موقوف</option>
                        </select>
                    </div>
                </div>

                <div class="actions">
                    <button id="exportBtn" class="btn btn-secondary"><i class="fas fa-file-export"></i> تصدير</button>
                    <button id="printBtn" class="btn btn-secondary"><i class="fas fa-print"></i> طباعة</button>
                    <a href="employee-form.html" class="btn btn-primary"><i class="fas fa-user-plus"></i> إضافة موظف</a>
                </div>
            </div>

            <div class="employees-table-container">
                <table id="employeesTable" class="employees-table">
                    <thead>
                        <tr>
                            <th>رقم الموظف</th>
                            <th>الاسم الرباعي</th>
                            <th>الوصف الوظيفي</th>
                            <th>العنوان الوظيفي</th>
                            <th>موقع العمل</th>
                            <th>الدرجة</th>
                            <th>المرحلة</th>
                            <th>القدم</th>
                            <th>تاريخ الترفيع القادم</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- سيتم ملء هذا الجزء ديناميكياً -->
                    </tbody>
                </table>
            </div>

            <div class="pagination">
                <button class="pagination-btn" id="prevPage" disabled><i class="fas fa-chevron-right"></i></button>
                <span class="pagination-info">صفحة <span id="currentPage">1</span> من <span id="totalPages">1</span></span>
                <button class="pagination-btn" id="nextPage" disabled><i class="fas fa-chevron-left"></i></button>
            </div>
        </div>
    </main>

    <footer>
        <div class="footer-content">
            <div class="footer-logo">
                <i class="fas fa-chart-line"></i> برنامج إدارة العلاوات والترفيعات
            </div>
            <p>جميع الحقوق محفوظة &copy; 2025 - تم إنشاء النظام بواسطة الفني حسن علي موسى</p>
        </div>
    </footer>

    <div class="dark-mode-toggle" id="darkModeToggle">
        <i class="fas fa-moon"></i>
    </div>

    <!-- نافذة عرض تفاصيل الموظف -->
    <div class="modal" id="viewEmployeeModal">
        <div class="modal-content modal-lg">
            <div class="modal-header">
                <h2>تفاصيل الموظف</h2>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <div class="employee-details">
                    <div class="employee-header">
                        <div class="employee-avatar">
                            <i class="fas fa-user-circle"></i>
                        </div>
                        <div class="employee-basic-info">
                            <h3 id="viewEmployeeName">اسم الموظف</h3>
                            <p id="viewEmployeeTitle">العنوان الوظيفي</p>
                            <p id="viewEmployeeId">رقم الموظف: </p>
                        </div>
                    </div>

                    <div class="employee-info-grid">
                        <div class="info-group">
                            <label>الوصف الوظيفي:</label>
                            <span id="viewJobDescription"></span>
                        </div>
                        <div class="info-group">
                            <label>موقع العمل:</label>
                            <span id="viewWorkLocation"></span>
                        </div>
                        <div class="info-group">
                            <label>التحصيل الدراسي:</label>
                            <span id="viewEducation"></span>
                        </div>
                        <div class="info-group">
                            <label>تاريخ التولد:</label>
                            <span id="viewBirthDate"></span>
                        </div>
                        <div class="info-group">
                            <label>تاريخ التعيين:</label>
                            <span id="viewHireDate"></span>
                        </div>
                        <div class="info-group">
                            <label>الدرجة الحالية:</label>
                            <span id="viewCurrentDegree"></span>
                        </div>
                        <div class="info-group">
                            <label>المرحلة الحالية:</label>
                            <span id="viewCurrentStage"></span>
                        </div>
                        <div class="info-group">
                            <label>القدم (بالأشهر):</label>
                            <span id="viewSeniority"></span>
                        </div>
                        <div class="info-group">
                            <label>الراتب الحالي:</label>
                            <span id="viewCurrentSalary"></span>
                        </div>
                        <div class="info-group">
                            <label>تاريخ آخر ترفيع:</label>
                            <span id="viewLastPromotionDate"></span>
                        </div>
                        <div class="info-group">
                            <label>تاريخ الترفيع القادم:</label>
                            <span id="viewNextPromotionDate"></span>
                        </div>
                        <div class="info-group">
                            <label>تاريخ الإحالة على التقاعد:</label>
                            <span id="viewRetirementDate"></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <a id="editEmployeeBtn" href="#" class="btn btn-primary"><i class="fas fa-edit"></i> تعديل</a>
                <button id="closeViewBtn" class="btn btn-secondary"><i class="fas fa-times"></i> إغلاق</button>
            </div>
        </div>
    </div>

    <!-- نافذة تأكيد الحذف -->
    <div class="modal" id="deleteModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>تأكيد الحذف</h2>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من رغبتك في حذف الموظف "<span id="deleteEmployeeName"></span>"؟</p>
                <p class="warning-text">هذا الإجراء لا يمكن التراجع عنه.</p>
            </div>
            <div class="modal-footer">
                <button id="confirmDeleteBtn" class="btn btn-danger"><i class="fas fa-trash"></i> تأكيد الحذف</button>
                <button id="cancelDeleteBtn" class="btn btn-secondary"><i class="fas fa-times"></i> إلغاء</button>
            </div>
        </div>
    </div>

    <!-- إضافة jQuery (مطلوب لـ Select2) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- إضافة مكتبة Select2 -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <!-- إضافة ملف الترجمة العربية لـ Select2 -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/i18n/ar.js"></script>

    <!-- ملفات النظام الأساسية -->
    <script src="script.js"></script>
    <script src="debug-storage.js"></script>
    <script src="simple-messages.js"></script>

    <!-- ملفات التوافق والتكامل -->
    <script src="jquery-compatibility.js"></script>
    <script src="select2-handler.js"></script>

    <!-- ملفات البيانات والمنطق -->
    <script src="simple-job-titles.js"></script>

    <!-- ملف الصفحة الرئيسي -->
    <script src="employees-list.js"></script>
</body>
</html>
