/* أنماط محسنة للتطبيق */
@import url('variables.css');

/* عنوان الصفحة */
.page-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 2rem;
    color: var(--text-color);
    position: relative;
    padding-right: 1.5rem;
    display: inline-block;
}

.page-title::before {
    content: '';
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 5px;
    height: 70%;
    background: linear-gradient(to bottom, var(--primary-color), var(--primary-light));
    border-radius: 5px;
}

.page-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    right: 0;
    width: 100px;
    height: 4px;
    background: linear-gradient(to right, var(--primary-color), transparent);
    border-radius: 2px;
}

/* أزرار محسنة */
.btn-enhanced {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-weight: 600;
    font-size: 1rem;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    gap: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.btn-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.btn-enhanced:hover::before {
    transform: translateX(100%);
}

.btn-enhanced:active {
    transform: translateY(2px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-primary-enhanced {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary-enhanced:hover {
    background-color: var(--primary-dark);
}

.btn-secondary-enhanced {
    background-color: var(--secondary-color);
    color: white;
}

.btn-secondary-enhanced:hover {
    background-color: var(--secondary-light);
}

.btn-warning-enhanced {
    background-color: var(--warning-color);
    color: white;
}

.btn-warning-enhanced:hover {
    background-color: var(--warning-light);
}

.btn-danger-enhanced {
    background-color: var(--danger-color);
    color: white;
}

.btn-danger-enhanced:hover {
    background-color: var(--danger-light);
}

.btn-outline-enhanced {
    background-color: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-enhanced:hover {
    background-color: var(--primary-color);
    color: white;
}

/* بطاقات محسنة */
.card-enhanced {
    background-color: var(--card-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.card-enhanced:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.card-header-enhanced {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.card-title-enhanced {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 0.7rem;
}

.card-title-enhanced i {
    font-size: 1.3rem;
    color: var(--primary-color);
}

.card-body-enhanced {
    padding: 0.5rem 0;
}

.card-footer-enhanced {
    display: flex;
    justify-content: flex-end;
    padding-top: 1rem;
    margin-top: 1rem;
    border-top: 1px solid #eee;
}

/* جدول محسن */
.table-enhanced {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-bottom: 1.5rem;
}

.table-enhanced th,
.table-enhanced td {
    padding: 1rem;
    text-align: right;
}

.table-enhanced thead th {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--primary-color);
    font-weight: 600;
    border-bottom: 2px solid rgba(59, 130, 246, 0.2);
    position: relative;
}

.table-enhanced thead th:first-child {
    border-radius: 10px 0 0 0;
}

.table-enhanced thead th:last-child {
    border-radius: 0 10px 0 0;
}

.table-enhanced tbody tr {
    transition: var(--transition);
}

.table-enhanced tbody tr:hover {
    background-color: rgba(59, 130, 246, 0.05);
}

.table-enhanced tbody tr:last-child td:first-child {
    border-radius: 0 0 0 10px;
}

.table-enhanced tbody tr:last-child td:last-child {
    border-radius: 0 0 10px 0;
}

.table-enhanced tbody td {
    border-bottom: 1px solid #eee;
}

/* شارات محسنة */
.badge-enhanced {
    display: inline-flex;
    align-items: center;
    padding: 0.35rem 0.75rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
    gap: 0.3rem;
}

.badge-primary {
    background-color: rgba(59, 130, 246, 0.15);
    color: var(--primary-color);
}

.badge-success {
    background-color: rgba(16, 185, 129, 0.15);
    color: var(--secondary-color);
}

.badge-warning {
    background-color: rgba(245, 158, 11, 0.15);
    color: var(--warning-color);
}

.badge-danger {
    background-color: rgba(239, 68, 68, 0.15);
    color: var(--danger-color);
}

.badge-info {
    background-color: rgba(99, 102, 241, 0.15);
    color: var(--info-color);
}

.badge-purple {
    background-color: rgba(139, 92, 246, 0.15);
    color: var(--purple-color);
}

/* نموذج محسن */
.form-enhanced {
    margin-bottom: 2rem;
}

.form-group-enhanced {
    margin-bottom: 1.5rem;
}

.form-label-enhanced {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
}

.form-control-enhanced {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #e5e7eb;
    border-radius: 10px;
    font-family: 'Cairo', sans-serif;
    font-size: 1rem;
    transition: var(--transition);
}

.form-control-enhanced:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.form-select-enhanced {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #e5e7eb;
    border-radius: 10px;
    font-family: 'Cairo', sans-serif;
    font-size: 1rem;
    transition: var(--transition);
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234b5563'%3E%3Cpath d='M7 10l5 5 5-5H7z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: left 1rem center;
    background-size: 1.5rem;
}

.form-select-enhanced:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

/* الوضع المظلم */
.dark-mode .page-title {
    color: #e0e0e0;
}

.dark-mode .card-enhanced {
    background-color: #1e1e1e;
}

.dark-mode .card-header-enhanced,
.dark-mode .card-footer-enhanced {
    border-color: #333;
}

.dark-mode .card-title-enhanced {
    color: #e0e0e0;
}

.dark-mode .table-enhanced thead th {
    background-color: rgba(59, 130, 246, 0.2);
}

.dark-mode .table-enhanced tbody tr:hover {
    background-color: rgba(59, 130, 246, 0.1);
}

.dark-mode .table-enhanced tbody td {
    border-color: #333;
}

.dark-mode .form-label-enhanced {
    color: #e0e0e0;
}

.dark-mode .form-control-enhanced,
.dark-mode .form-select-enhanced {
    background-color: #2a2a2a;
    border-color: #444;
    color: #e0e0e0;
}

.dark-mode .form-select-enhanced {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23e0e0e0'%3E%3Cpath d='M7 10l5 5 5-5H7z'/%3E%3C/svg%3E");
}
