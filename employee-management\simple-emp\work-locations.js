// انتظر حتى يتم تحميل المستند بالكامل
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة صفحة مواقع العمل
    initWorkLocationsPage();
});

// تهيئة صفحة مواقع العمل
function initWorkLocationsPage() {
    // تحميل مواقع العمل
    loadWorkLocations();

    // تهيئة نموذج إضافة موقع عمل
    initAddWorkLocationForm();

    // تهيئة البحث
    initSearch();

    // تهيئة النوافذ المنبثقة
    initModals();
}

// تحميل مواقع العمل
function loadWorkLocations() {
    // في الإصدار النهائي، سيتم استبدال هذا بطلب API لجلب البيانات من الخادم
    // هنا نستخدم بيانات تجريبية للعرض

    // التحقق من وجود بيانات في التخزين المحلي
    let workLocations = localStorage.getItem('workLocations');

    if (!workLocations) {
        // إذا لم تكن هناك بيانات، استخدم بيانات افتراضية
        workLocations = [
            { id: 1, name: 'كلية الهندسة' },
            { id: 2, name: 'كلية العلوم' },
            { id: 3, name: 'كلية الطب' },
            { id: 4, name: 'كلية الآداب' },
            { id: 5, name: 'رئاسة الجامعة' }
        ];

        // حفظ البيانات في التخزين المحلي
        localStorage.setItem('workLocations', JSON.stringify(workLocations));
    } else {
        // تحويل البيانات من JSON إلى كائن JavaScript
        workLocations = JSON.parse(workLocations);
    }

    // عرض البيانات في الجدول
    displayWorkLocations(workLocations);
}

// عرض مواقع العمل في الجدول
function displayWorkLocations(workLocations) {
    const tableBody = document.querySelector('#workLocationsTable tbody');
    if (!tableBody) return;

    // مسح محتوى الجدول
    tableBody.innerHTML = '';

    // إذا لم تكن هناك مواقع عمل، عرض رسالة
    if (workLocations.length === 0) {
        const emptyRow = document.createElement('tr');
        emptyRow.innerHTML = `<td colspan="3" class="text-center">لا توجد مواقع عمل. أضف موقع عمل جديد.</td>`;
        tableBody.appendChild(emptyRow);
        return;
    }

    // إضافة الصفوف إلى الجدول
    workLocations.forEach((location, index) => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${index + 1}</td>
            <td>${location.name}</td>
            <td>
                <div class="action-buttons">
                    <button class="action-btn edit-btn" data-id="${location.id}" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn delete-btn" data-id="${location.id}" data-name="${location.name}" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;
        tableBody.appendChild(row);
    });

    // إضافة مستمعي الأحداث لأزرار التعديل والحذف
    addActionButtonsEventListeners();
}

// إضافة مستمعي الأحداث لأزرار التعديل والحذف
function addActionButtonsEventListeners() {
    // أزرار التعديل
    const editButtons = document.querySelectorAll('.edit-btn');
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const locationId = this.getAttribute('data-id');
            openEditModal(locationId);
        });
    });

    // أزرار الحذف
    const deleteButtons = document.querySelectorAll('.delete-btn');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const locationId = this.getAttribute('data-id');
            const locationName = this.getAttribute('data-name');
            openDeleteModal(locationId, locationName);
        });
    });
}

// تهيئة نموذج إضافة موقع عمل
function initAddWorkLocationForm() {
    const addForm = document.getElementById('addWorkLocationForm');
    if (!addForm) return;

    addForm.addEventListener('submit', function(e) {
        e.preventDefault();

        // الحصول على قيم الحقول
        const name = document.getElementById('locationName').value.trim();

        // التحقق من صحة البيانات
        if (!name) {
            alert('يرجى إدخال اسم موقع العمل');
            return;
        }

        // إضافة موقع العمل الجديد
        addWorkLocation(name);

        // إعادة تعيين النموذج
        this.reset();
    });
}

// إضافة موقع عمل جديد
function addWorkLocation(name) {
    // الحصول على مواقع العمل الحالية
    let workLocations = JSON.parse(localStorage.getItem('workLocations') || '[]');

    // إنشاء معرف فريد جديد
    const newId = workLocations.length > 0 ? Math.max(...workLocations.map(loc => loc.id)) + 1 : 1;

    // إنشاء موقع العمل الجديد
    const newLocation = {
        id: newId,
        name: name
    };

    // إضافة موقع العمل الجديد إلى المصفوفة
    workLocations.push(newLocation);

    // حفظ البيانات المحدثة في التخزين المحلي
    localStorage.setItem('workLocations', JSON.stringify(workLocations));

    // تحديث عرض الجدول
    displayWorkLocations(workLocations);

    // عرض رسالة نجاح
    showNotification('تمت الإضافة', 'success');
}

// تهيئة البحث
function initSearch() {
    const searchInput = document.getElementById('searchLocation');
    if (!searchInput) return;

    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.trim().toLowerCase();

        // الحصول على مواقع العمل
        const workLocations = JSON.parse(localStorage.getItem('workLocations') || '[]');

        // تصفية مواقع العمل بناءً على مصطلح البحث
        const filteredLocations = workLocations.filter(location => {
            return location.name.toLowerCase().includes(searchTerm);
        });

        // عرض النتائج المصفاة
        displayWorkLocations(filteredLocations);
    });
}

// تهيئة النوافذ المنبثقة
function initModals() {
    // الحصول على عناصر النوافذ المنبثقة
    const editModal = document.getElementById('editModal');
    const deleteModal = document.getElementById('deleteModal');
    const closeButtons = document.querySelectorAll('.close-modal');

    // أزرار الإغلاق
    closeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const modal = this.closest('.modal');
            closeModal(modal);
        });
    });

    // إغلاق النافذة عند النقر خارجها
    window.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal')) {
            closeModal(e.target);
        }
    });

    // زر إلغاء التعديل
    const cancelEditBtn = document.getElementById('cancelEditBtn');
    if (cancelEditBtn) {
        cancelEditBtn.addEventListener('click', function() {
            closeModal(editModal);
        });
    }

    // زر حفظ التعديل
    const saveEditBtn = document.getElementById('saveEditBtn');
    if (saveEditBtn) {
        saveEditBtn.addEventListener('click', function() {
            saveEditedWorkLocation();
        });
    }

    // زر إلغاء الحذف
    const cancelDeleteBtn = document.getElementById('cancelDeleteBtn');
    if (cancelDeleteBtn) {
        cancelDeleteBtn.addEventListener('click', function() {
            closeModal(deleteModal);
        });
    }

    // زر تأكيد الحذف
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    if (confirmDeleteBtn) {
        confirmDeleteBtn.addEventListener('click', function() {
            const locationId = this.getAttribute('data-id');
            deleteWorkLocation(locationId);
            closeModal(deleteModal);
        });
    }
}

// فتح نافذة التعديل
function openEditModal(locationId) {
    // الحصول على موقع العمل المراد تعديله
    const workLocations = JSON.parse(localStorage.getItem('workLocations') || '[]');
    const location = workLocations.find(loc => loc.id == locationId);

    if (!location) return;

    // ملء حقول النموذج بالبيانات الحالية
    document.getElementById('editLocationId').value = location.id;
    document.getElementById('editLocationName').value = location.name;

    // فتح النافذة
    const editModal = document.getElementById('editModal');
    openModal(editModal);
}

// فتح نافذة تأكيد الحذف
function openDeleteModal(locationId, locationName) {
    // عرض اسم موقع العمل في رسالة التأكيد
    document.getElementById('deleteLocationName').textContent = locationName;

    // تعيين معرف موقع العمل لزر التأكيد
    document.getElementById('confirmDeleteBtn').setAttribute('data-id', locationId);

    // فتح النافذة
    const deleteModal = document.getElementById('deleteModal');
    openModal(deleteModal);
}

// حفظ موقع العمل المعدل
function saveEditedWorkLocation() {
    // الحصول على قيم الحقول
    const id = document.getElementById('editLocationId').value;
    const name = document.getElementById('editLocationName').value.trim();

    // التحقق من صحة البيانات
    if (!name) {
        alert('يرجى إدخال اسم موقع العمل');
        return;
    }

    // الحصول على مواقع العمل الحالية
    let workLocations = JSON.parse(localStorage.getItem('workLocations') || '[]');

    // البحث عن موقع العمل وتحديثه
    const index = workLocations.findIndex(loc => loc.id == id);
    if (index !== -1) {
        workLocations[index] = {
            id: parseInt(id),
            name: name
        };

        // حفظ البيانات المحدثة في التخزين المحلي
        localStorage.setItem('workLocations', JSON.stringify(workLocations));

        // تحديث عرض الجدول
        displayWorkLocations(workLocations);

        // إغلاق النافذة
        closeModal(document.getElementById('editModal'));

        // عرض رسالة نجاح
        showNotification('تم التعديل', 'success');
    }
}

// حذف موقع العمل
function deleteWorkLocation(locationId) {
    // الحصول على مواقع العمل الحالية
    let workLocations = JSON.parse(localStorage.getItem('workLocations') || '[]');

    // حذف موقع العمل
    workLocations = workLocations.filter(loc => loc.id != locationId);

    // حفظ البيانات المحدثة في التخزين المحلي
    localStorage.setItem('workLocations', JSON.stringify(workLocations));

    // تحديث عرض الجدول
    displayWorkLocations(workLocations);

    // عرض رسالة نجاح
    showNotification('تم الحذف', 'success');
}

// فتح نافذة منبثقة
function openModal(modal) {
    if (modal) {
        modal.style.display = 'block';
    }
}

// إغلاق نافذة منبثقة
function closeModal(modal) {
    if (modal) {
        modal.style.display = 'none';
    }
}

// عرض إشعار
function showNotification(message, type = 'info') {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-info-circle'}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close"><i class="fas fa-times"></i></button>
    `;

    // إضافة الإشعار إلى الصفحة
    document.body.appendChild(notification);

    // إضافة مستمع حدث لزر الإغلاق
    const closeButton = notification.querySelector('.notification-close');
    closeButton.addEventListener('click', function() {
        notification.remove();
    });

    // إزالة الإشعار تلقائيًا بعد 5 ثوانٍ
    setTimeout(() => {
        notification.classList.add('fade-out');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 5000);
}

// إضافة أنماط CSS للإشعارات
(function addNotificationStyles() {
    const style = document.createElement('style');
    style.textContent = `
        .notification {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            padding: 1rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            min-width: 300px;
            z-index: 1000;
            animation: slideIn 0.3s ease-out;
        }

        .notification.success {
            border-right: 4px solid var(--secondary-color);
        }

        .notification.info {
            border-right: 4px solid var(--primary-color);
        }

        .notification.error {
            border-right: 4px solid var(--danger-color);
        }

        .notification-content {
            display: flex;
            align-items: center;
            gap: 0.8rem;
        }

        .notification-content i {
            font-size: 1.2rem;
        }

        .notification.success i {
            color: var(--secondary-color);
        }

        .notification.info i {
            color: var(--primary-color);
        }

        .notification.error i {
            color: var(--danger-color);
        }

        .notification-close {
            background: none;
            border: none;
            color: var(--text-light);
            cursor: pointer;
            font-size: 0.9rem;
            padding: 0.3rem;
            transition: var(--transition);
        }

        .notification-close:hover {
            color: var(--text-color);
        }

        .notification.fade-out {
            opacity: 0;
            transform: translateX(30px);
            transition: opacity 0.3s, transform 0.3s;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .dark-mode .notification {
            background-color: #2a2a2a;
            color: white;
        }
    `;
    document.head.appendChild(style);
})();
