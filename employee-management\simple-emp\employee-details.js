// انتظر حتى يتم تحميل المستند بالكامل
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة صفحة تفاصيل الموظف
    initEmployeeDetailsPage();
});

// تهيئة صفحة تفاصيل الموظف
function initEmployeeDetailsPage() {
    // الحصول على معرف الموظف من عنوان URL
    const urlParams = new URLSearchParams(window.location.search);
    const employeeId = urlParams.get('id');

    if (!employeeId) {
        // إذا لم يتم تحديد معرف الموظف، العودة إلى صفحة قائمة الموظفين
        window.location.href = 'employees-list.html';
        return;
    }

    // تحميل بيانات الموظف
    loadEmployeeDetails(employeeId);

    // تحميل التشكرات والعقوبات الخاصة بالموظف
    loadEmployeeThanks(employeeId);
    loadEmployeePenalties(employeeId);

    // إضافة مستمع حدث لزر العودة للقائمة
    document.getElementById('backToListBtn').addEventListener('click', function() {
        window.location.href = 'employees-list.html';
    });

    // إضافة مستمع حدث لزر تعديل الموظف
    document.getElementById('editEmployeeBtn').addEventListener('click', function() {
        window.location.href = `employee-form.html?id=${employeeId}`;
    });
}

// تحميل بيانات الموظف
function loadEmployeeDetails(employeeId) {
    // الحصول على بيانات الموظفين من التخزين المحلي
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');

    // البحث عن الموظف بواسطة المعرف
    const employee = employees.find(emp => emp.id == employeeId);

    if (!employee) {
        // إذا لم يتم العثور على الموظف، العودة إلى صفحة قائمة الموظفين
        window.location.href = 'employees-list.html';
        return;
    }

    // عرض بيانات الموظف في الصفحة
    displayEmployeeDetails(employee);
}

// عرض بيانات الموظف في الصفحة
function displayEmployeeDetails(employee) {
    // عرض اسم الموظف في عنوان الصفحة
    document.getElementById('employeeName').textContent = `تفاصيل الموظف: ${employee.name}`;

    // عرض المعلومات الأساسية
    document.getElementById('employeeId').textContent = employee.id;
    document.getElementById('employeeFullName').textContent = employee.name;
    document.getElementById('employeeJobDescription').textContent = getJobDescriptionText(employee.jobDescription);
    document.getElementById('employeeJobTitle').textContent = employee.jobTitle || '-';
    document.getElementById('employeeEducation').textContent = employee.education || '-';
    document.getElementById('employeeWorkLocation').textContent = employee.workLocation || '-';

    // عرض التواريخ
    document.getElementById('employeeBirthDate').textContent = formatDate(employee.birthDate) || '-';
    document.getElementById('employeeAppointmentDate').textContent = formatDate(employee.appointmentDate) || '-';
    document.getElementById('employeeLastAllowanceDate').textContent = formatDate(employee.lastAllowanceDate) || '-';
    document.getElementById('employeeNextAllowanceDate').textContent = formatDate(employee.nextAllowanceDate) || '-';
    document.getElementById('employeeLastPromotionDate').textContent = formatDate(employee.lastPromotionDate) || '-';
    document.getElementById('employeeNextPromotionDate').textContent = formatDate(employee.nextPromotionDate) || '-';
    document.getElementById('employeeRetirementDate').textContent = formatDate(employee.retirementDate) || '-';

    // عرض معلومات الدرجة والراتب
    document.getElementById('employeeCurrentGrade').textContent = employee.currentGrade || '-';
    document.getElementById('employeeCurrentStage').textContent = employee.currentStage || '-';
    document.getElementById('employeeSeniority').textContent = employee.seniority || '0';
    document.getElementById('employeeCurrentSalary').textContent = employee.currentSalary ? `${employee.currentSalary} د.ع` : '-';

    // عرض ملاحظات العلاوة والترفيع
    const allowanceNotes = document.getElementById('allowanceNotes');
    const promotionNotes = document.getElementById('promotionNotes');

    // التحقق من وجود تشكرات تؤثر على العلاوة
    const thanks = JSON.parse(localStorage.getItem('thanks') || '[]');
    const employeeThanks = thanks.filter(thank => thank.employeeId == employee.id);

    // البحث عن التشكرات التي تؤثر على العلاوة
    const allowanceAffectingThanks = employeeThanks.filter(thank => {
        const thankDate = new Date(thank.date);
        const lastAllowanceDate = new Date(employee.lastAllowanceDate);
        const nextAllowanceDate = new Date(employee.nextAllowanceDate);

        return thank.effect !== 'none' && thankDate > lastAllowanceDate && thankDate < nextAllowanceDate;
    });

    if (allowanceAffectingThanks.length > 0) {
        allowanceNotes.textContent = '* تم تقديم العلاوة بسبب كتب الشكر';
        allowanceNotes.style.display = 'block';
    } else {
        allowanceNotes.style.display = 'none';
    }

    // البحث عن التشكرات التي تؤثر على الترفيع
    const promotionAffectingThanks = employeeThanks.filter(thank => {
        const thankDate = new Date(thank.date);
        const lastPromotionDate = new Date(employee.lastPromotionDate);
        const nextPromotionDate = new Date(employee.nextPromotionDate);

        return thank.effect !== 'none' && thankDate > lastPromotionDate && thankDate < nextPromotionDate;
    });

    if (promotionAffectingThanks.length > 0) {
        promotionNotes.textContent = '* تم تقديم الترفيع بسبب كتب الشكر';
        promotionNotes.style.display = 'block';
    } else {
        promotionNotes.style.display = 'none';
    }
}

// تحميل التشكرات الخاصة بالموظف
function loadEmployeeThanks(employeeId) {
    // الحصول على التشكرات من التخزين المحلي
    const thanks = JSON.parse(localStorage.getItem('thanks') || '[]');

    // تصفية التشكرات الخاصة بالموظف
    const employeeThanks = thanks.filter(thank => thank.employeeId == employeeId);

    // عرض التشكرات في الجدول
    displayEmployeeThanks(employeeThanks);
}

// عرض التشكرات الخاصة بالموظف
function displayEmployeeThanks(thanks) {
    const tableBody = document.getElementById('thanksTableBody');
    const noThanksMessage = document.getElementById('noThanksMessage');

    // مسح محتوى الجدول
    tableBody.innerHTML = '';

    if (thanks.length === 0) {
        // إذا لم تكن هناك تشكرات، عرض رسالة
        noThanksMessage.style.display = 'block';
        document.getElementById('thanksTable').style.display = 'none';
        return;
    }

    // إخفاء رسالة عدم وجود تشكرات وإظهار الجدول
    noThanksMessage.style.display = 'none';
    document.getElementById('thanksTable').style.display = 'table';

    // ترتيب التشكرات حسب التاريخ (الأحدث أولاً)
    thanks.sort((a, b) => new Date(b.date) - new Date(a.date));

    // إضافة التشكرات إلى الجدول
    thanks.forEach(thank => {
        const row = document.createElement('tr');

        // تحويل نوع التشكر إلى نص عربي
        let typeText = '';
        switch (thank.type) {
            case 'ministerial':
                typeText = 'وزاري';
                break;
            case 'presidential':
                typeText = 'رئاسي';
                break;
            case 'university':
                typeText = 'جامعي';
                break;
            case 'college':
                typeText = 'كلية';
                break;
            case 'department':
                typeText = 'قسم';
                break;
            default:
                typeText = thank.type;
        }

        // تحويل تأثير التشكر إلى نص عربي
        let effectText = '';
        switch (thank.effect) {
            case 'none':
                effectText = 'لا يوجد';
                break;
            case '1month_university':
                effectText = 'رئيس الجامعة - تقديم شهر واحد';
                break;
            case '1month_minister':
                effectText = 'وزير التعليم العالي - تقديم شهر واحد';
                break;
            case '6months_pm':
                effectText = 'رئيس الوزراء - تقديم 6 أشهر';
                break;
            default:
                effectText = thank.effect;
        }

        row.innerHTML = `
            <td>${formatDate(thank.date)}</td>
            <td>${typeText}</td>
            <td>${thank.issuer}</td>
            <td>${effectText}</td>
        `;

        tableBody.appendChild(row);
    });
}

// تحميل العقوبات الخاصة بالموظف
function loadEmployeePenalties(employeeId) {
    // الحصول على العقوبات من التخزين المحلي
    const penalties = JSON.parse(localStorage.getItem('penalties') || '[]');

    // تصفية العقوبات الخاصة بالموظف
    const employeePenalties = penalties.filter(penalty => penalty.employeeId == employeeId);

    // عرض العقوبات في الجدول
    displayEmployeePenalties(employeePenalties);
}

// عرض العقوبات الخاصة بالموظف
function displayEmployeePenalties(penalties) {
    const tableBody = document.getElementById('penaltiesTableBody');
    const noPenaltiesMessage = document.getElementById('noPenaltiesMessage');

    // مسح محتوى الجدول
    tableBody.innerHTML = '';

    if (penalties.length === 0) {
        // إذا لم تكن هناك عقوبات، عرض رسالة
        noPenaltiesMessage.style.display = 'block';
        document.getElementById('penaltiesTable').style.display = 'none';
        return;
    }

    // إخفاء رسالة عدم وجود عقوبات وإظهار الجدول
    noPenaltiesMessage.style.display = 'none';
    document.getElementById('penaltiesTable').style.display = 'table';

    // ترتيب العقوبات حسب التاريخ (الأحدث أولاً)
    penalties.sort((a, b) => new Date(b.date) - new Date(a.date));

    // إضافة العقوبات إلى الجدول
    penalties.forEach(penalty => {
        const row = document.createElement('tr');

        row.innerHTML = `
            <td>${formatDate(penalty.date)}</td>
            <td>${penalty.type}</td>
            <td>${penalty.reason || '-'}</td>
            <td>${penalty.effect || '-'}</td>
        `;

        tableBody.appendChild(row);
    });
}

// تحويل الوصف الوظيفي إلى نص عربي
function getJobDescriptionText(jobDescription) {
    switch (jobDescription) {
        case 'teaching':
            return 'تدريسي';
        case 'technical':
            return 'فني';
        case 'administrative':
            return 'إداري';
        default:
            return jobDescription || '-';
    }
}

// تنسيق التاريخ (ميلادي فقط - إجباري)
function formatDate(dateString) {
    if (!dateString) return null;

    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;

    // إجبار استخدام التقويم الميلادي مع تنسيق يدوي لضمان عدم ظهور التاريخ الهجري
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();

    const monthNames = [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];

    return `${day} ${monthNames[month - 1]} ${year}`;
}
