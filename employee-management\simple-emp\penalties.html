<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>العقوبات - برنامج إدارة العلاوات والترفيعات</title>
    <link rel="stylesheet" href="styles.css">
    <!-- إضافة خط Cairo من Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <!-- إضافة Font Awesome للأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- إضافة أنماط خاصة بصفحة التشكرات والعقوبات -->
    <link rel="stylesheet" href="thanks-penalties.css">
    <!-- إضافة أنماط النوافذ المنبثقة -->
    <link rel="stylesheet" href="modals.css">
    <!-- إضافة أنماط الإشعارات -->
    <link rel="stylesheet" href="notifications.css">
</head>
<body>
    <header>
        <nav>
            <div class="logo">
                <i class="fas fa-chart-line logo-icon"></i>
                برنامج إدارة العلاوات والترفيعات
            </div>
            <div class="nav-links">
                <div class="dropdown">
                    <a href="index.html"><i class="fas fa-home"></i> الرئيسية</a>
                </div>
                
                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-id-card"></i> الملفات التعريفية <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="work-locations.html"><i class="fas fa-building"></i> مواقع العمل</a>
                        <a href="job-titles.html"><i class="fas fa-user-tie"></i> العناوين الوظيفية</a>
                        <a href="education-levels.html"><i class="fas fa-graduation-cap"></i> التحصيل الدراسي</a>
                        <a href="leaves.html"><i class="fas fa-calendar-alt"></i> الاجازات</a>
                        <a href="alert-periods.html"><i class="fas fa-bell"></i> فترة التنبيه</a>
                    </div>
                </div>
                
                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-users"></i> الموظفين <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="employee-form.html"><i class="fas fa-user-plus"></i> فتح ملف موظف</a>
                        <a href="employees-list.html"><i class="fas fa-list"></i> عرض الموظفين</a>
                    </div>
                </div>
                
                <div class="dropdown">
                    <a href="#" class="dropdown-toggle active"><i class="fas fa-medal"></i> التشكرات والعقوبات <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="thanks.html"><i class="fas fa-award"></i> التشكرات</a>
                        <a href="penalties.html" class="active"><i class="fas fa-exclamation-triangle"></i> العقوبات</a>
                    </div>
                </div>
                
                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-calendar-alt"></i> الاجازات والغيابات <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="employee-leaves.html"><i class="fas fa-calendar-check"></i> الاجازات</a>
                        <a href="absences.html"><i class="fas fa-calendar-times"></i> الغيابات</a>
                    </div>
                </div>
                
                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-chart-bar"></i> التقارير <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="allowance-reports.html"><i class="fas fa-file-alt"></i> تقرير العلاوات المستحقة</a>
                        <a href="promotion-reports.html"><i class="fas fa-file-alt"></i> تقرير الترفيعات المستحقة</a>
                        <a href="retirement-reports.html"><i class="fas fa-user-clock"></i> قرب الاحالة الى التقاعد</a>
                    </div>
                </div>
                
                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-bell"></i> التنبيهات <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="allowance-alerts.html"><i class="fas fa-bell"></i> تنبيه العلاوات المستحقة</a>
                        <a href="promotion-alerts.html"><i class="fas fa-bell"></i> تنبيه الترفيعات المستحقة</a>
                        <a href="retirement-alerts.html"><i class="fas fa-bell"></i> تنبيه الاحالة الى التقاعد</a>
                    </div>
                </div>
            </div>
            <div class="menu-toggle" id="menuToggle">
                <i class="fas fa-bars"></i>
            </div>
        </nav>
    </header>

    <main>
        <div class="penalties-container">
            <h1 class="page-title">إدارة العقوبات</h1>
            
            <div class="penalties-content">
                <div class="penalties-form-section">
                    <h2><i class="fas fa-plus-circle"></i> إضافة عقوبة جديدة</h2>
                    <form id="addPenaltyForm" class="penalties-form">
                        <div class="form-group">
                            <label for="employeeId">الموظف: <span class="required">*</span></label>
                            <select id="employeeId" name="employeeId" required>
                                <option value="">اختر الموظف</option>
                                <!-- سيتم ملء هذا الجزء ديناميكياً -->
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="penaltyType">نوع العقوبة: <span class="required">*</span></label>
                            <select id="penaltyType" name="penaltyType" required>
                                <option value="">اختر نوع العقوبة</option>
                                <option value="severe">شديدة</option>
                                <option value="medium">متوسطة</option>
                                <option value="light">خفيفة</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="penaltyDate">تاريخ العقوبة: <span class="required">*</span></label>
                            <input type="date" id="penaltyDate" name="penaltyDate" required>
                        </div>
                        <div class="form-group">
                            <label for="penaltyNumber">رقم الكتاب:</label>
                            <input type="text" id="penaltyNumber" name="penaltyNumber">
                        </div>
                        <div class="form-group">
                            <label for="penaltyIssuer">الجهة المانحة: <span class="required">*</span></label>
                            <input type="text" id="penaltyIssuer" name="penaltyIssuer" required>
                        </div>
                        <div class="form-group">
                            <label for="penaltyReason">سبب العقوبة:</label>
                            <textarea id="penaltyReason" name="penaltyReason" rows="3"></textarea>
                        </div>
                        <div class="form-group">
                            <label for="penaltyEffect">التأثير على الترفيع:</label>
                            <select id="penaltyEffect" name="penaltyEffect">
                                <option value="none">لا يوجد</option>
                                <option value="3months">تأخير 3 أشهر</option>
                                <option value="6months">تأخير 6 أشهر</option>
                                <option value="1year">تأخير سنة</option>
                            </select>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary"><i class="fas fa-save"></i> حفظ</button>
                            <button type="reset" class="btn btn-secondary"><i class="fas fa-undo"></i> إعادة تعيين</button>
                        </div>
                    </form>
                </div>
                
                <div class="penalties-list-section">
                    <div class="list-header">
                        <h2><i class="fas fa-list"></i> قائمة العقوبات</h2>
                        <div class="list-actions">
                            <div class="search-container">
                                <input type="text" id="searchPenalty" placeholder="بحث..." class="search-input">
                                <button class="search-btn"><i class="fas fa-search"></i></button>
                            </div>
                            <select id="filterPenaltyType" class="filter-select">
                                <option value="">جميع الأنواع</option>
                                <option value="severe">شديدة</option>
                                <option value="medium">متوسطة</option>
                                <option value="light">خفيفة</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="penalties-list">
                        <table id="penaltiesTable">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>الموظف</th>
                                    <th>نوع العقوبة</th>
                                    <th>تاريخ العقوبة</th>
                                    <th>الجهة المانحة</th>
                                    <th>التأثير</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- سيتم ملء هذا الجزء ديناميكياً -->
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="pagination">
                        <button class="pagination-btn" disabled><i class="fas fa-chevron-right"></i></button>
                        <span class="pagination-info">صفحة <span id="currentPage">1</span> من <span id="totalPages">1</span></span>
                        <button class="pagination-btn" disabled><i class="fas fa-chevron-left"></i></button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="footer-content">
            <div class="footer-logo">
                <i class="fas fa-chart-line"></i> برنامج إدارة العلاوات والترفيعات
            </div>
            <div class="footer-links">
                <a href="#">الرئيسية</a>
                <a href="#">عن البرنامج</a>
                <a href="#">الميزات</a>
                <a href="#">المساعدة</a>
                <a href="#">اتصل بنا</a>
            </div>
            <div class="footer-social">
                <a href="#" class="social-icon"><i class="fab fa-facebook-f"></i></a>
                <a href="#" class="social-icon"><i class="fab fa-twitter"></i></a>
                <a href="#" class="social-icon"><i class="fab fa-linkedin-in"></i></a>
                <a href="#" class="social-icon"><i class="fab fa-instagram"></i></a>
            </div>
            <p>جميع الحقوق محفوظة &copy; 2025 - برنامج إدارة العلاوات والترفيعات</p>
        </div>
    </footer>

    <div class="dark-mode-toggle" id="darkModeToggle">
        <i class="fas fa-moon"></i>
    </div>

    <!-- نافذة عرض تفاصيل العقوبة -->
    <div class="modal" id="viewPenaltyModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>تفاصيل العقوبة</h2>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <div class="penalty-details">
                    <div class="info-group">
                        <label>الموظف:</label>
                        <span id="viewEmployeeName"></span>
                    </div>
                    <div class="info-group">
                        <label>نوع العقوبة:</label>
                        <span id="viewPenaltyType"></span>
                    </div>
                    <div class="info-group">
                        <label>تاريخ العقوبة:</label>
                        <span id="viewPenaltyDate"></span>
                    </div>
                    <div class="info-group">
                        <label>رقم الكتاب:</label>
                        <span id="viewPenaltyNumber"></span>
                    </div>
                    <div class="info-group">
                        <label>الجهة المانحة:</label>
                        <span id="viewPenaltyIssuer"></span>
                    </div>
                    <div class="info-group">
                        <label>سبب العقوبة:</label>
                        <span id="viewPenaltyReason"></span>
                    </div>
                    <div class="info-group">
                        <label>التأثير على الترفيع:</label>
                        <span id="viewPenaltyEffect"></span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button id="editPenaltyBtn" class="btn btn-primary"><i class="fas fa-edit"></i> تعديل</button>
                <button id="closeViewBtn" class="btn btn-secondary"><i class="fas fa-times"></i> إغلاق</button>
            </div>
        </div>
    </div>

    <!-- نافذة تعديل العقوبة -->
    <div class="modal" id="editModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>تعديل العقوبة</h2>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <form id="editPenaltyForm" class="penalties-form">
                    <input type="hidden" id="editPenaltyId">
                    <div class="form-group">
                        <label for="editEmployeeId">الموظف: <span class="required">*</span></label>
                        <select id="editEmployeeId" name="editEmployeeId" required>
                            <option value="">اختر الموظف</option>
                            <!-- سيتم ملء هذا الجزء ديناميكياً -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="editPenaltyType">نوع العقوبة: <span class="required">*</span></label>
                        <select id="editPenaltyType" name="editPenaltyType" required>
                            <option value="">اختر نوع العقوبة</option>
                            <option value="severe">شديدة</option>
                            <option value="medium">متوسطة</option>
                            <option value="light">خفيفة</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="editPenaltyDate">تاريخ العقوبة: <span class="required">*</span></label>
                        <input type="date" id="editPenaltyDate" name="editPenaltyDate" required>
                    </div>
                    <div class="form-group">
                        <label for="editPenaltyNumber">رقم الكتاب:</label>
                        <input type="text" id="editPenaltyNumber" name="editPenaltyNumber">
                    </div>
                    <div class="form-group">
                        <label for="editPenaltyIssuer">الجهة المانحة: <span class="required">*</span></label>
                        <input type="text" id="editPenaltyIssuer" name="editPenaltyIssuer" required>
                    </div>
                    <div class="form-group">
                        <label for="editPenaltyReason">سبب العقوبة:</label>
                        <textarea id="editPenaltyReason" name="editPenaltyReason" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="editPenaltyEffect">التأثير على الترفيع:</label>
                        <select id="editPenaltyEffect" name="editPenaltyEffect">
                            <option value="none">لا يوجد</option>
                            <option value="3months">تأخير 3 أشهر</option>
                            <option value="6months">تأخير 6 أشهر</option>
                            <option value="1year">تأخير سنة</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button id="saveEditBtn" class="btn btn-primary"><i class="fas fa-save"></i> حفظ التغييرات</button>
                <button id="cancelEditBtn" class="btn btn-secondary"><i class="fas fa-times"></i> إلغاء</button>
            </div>
        </div>
    </div>

    <!-- نافذة تأكيد الحذف -->
    <div class="modal" id="deleteModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>تأكيد الحذف</h2>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من رغبتك في حذف هذه العقوبة؟</p>
                <p class="warning-text">هذا الإجراء لا يمكن التراجع عنه.</p>
            </div>
            <div class="modal-footer">
                <button id="confirmDeleteBtn" class="btn btn-danger"><i class="fas fa-trash"></i> تأكيد الحذف</button>
                <button id="cancelDeleteBtn" class="btn btn-secondary"><i class="fas fa-times"></i> إلغاء</button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
    <script src="penalties.js"></script>
</body>
</html>
