<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح شامل للتواريخ - نظام إدارة العلاوات والترفيعات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }

        .header h1 {
            color: #dc2626;
            margin-bottom: 10px;
        }

        .emergency {
            background: #fef2f2;
            border: 2px solid #ef4444;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
        }

        .emergency h3 {
            color: #dc2626;
            margin-bottom: 10px;
        }

        .emergency p {
            color: #991b1b;
            font-weight: 600;
        }

        .fix-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: #f9f9f9;
        }

        .fix-section h2 {
            color: #dc2626;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .btn {
            background: linear-gradient(135deg, #dc2626 0%, #991b1b 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
            font-size: 16px;
            font-weight: 600;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }

        .btn.success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }

        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            background: white;
            border-left: 4px solid #dc2626;
        }

        .success {
            border-left-color: #10b981;
            background: #f0fdf4;
            color: #065f46;
        }

        .error {
            border-left-color: #ef4444;
            background: #fef2f2;
            color: #991b1b;
        }

        .progress {
            background: #e5e7eb;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-bar {
            background: linear-gradient(135deg, #dc2626 0%, #991b1b 100%);
            height: 25px;
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        .step {
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .step.completed {
            border-left: 4px solid #10b981;
            background: #f0fdf4;
        }

        .step.failed {
            border-left: 4px solid #ef4444;
            background: #fef2f2;
        }

        .step-status {
            font-weight: bold;
        }

        .completed .step-status {
            color: #10b981;
        }

        .failed .step-status {
            color: #ef4444;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-exclamation-triangle"></i> إصلاح شامل للتواريخ</h1>
            <p>حل نهائي لمشكلة التواريخ الهجرية في النظام</p>
        </div>

        <div class="emergency">
            <h3><i class="fas fa-fire"></i> إصلاح طارئ مطلوب!</h3>
            <p>تم اكتشاف تواريخ هجرية في النظام - يجب إصلاحها فوراً</p>
        </div>

        <!-- الإصلاح الشامل -->
        <div class="fix-section">
            <h2><i class="fas fa-tools"></i> الإصلاح الشامل</h2>
            <button class="btn" onclick="startCompleteFix()">بدء الإصلاح الشامل</button>
            <div id="fixProgress" style="display: none;">
                <div class="progress">
                    <div class="progress-bar" id="progressBar" style="width: 0%">0%</div>
                </div>
                <div id="fixSteps"></div>
            </div>
            <div id="fixResult" class="result" style="display: none;"></div>
        </div>

        <!-- التحقق النهائي -->
        <div class="fix-section">
            <h2><i class="fas fa-check-double"></i> التحقق النهائي</h2>
            <button class="btn success" onclick="finalCheck()">التحقق من الإصلاح</button>
            <div id="checkResult" class="result" style="display: none;"></div>
        </div>

        <!-- إعادة تحميل النظام -->
        <div class="fix-section">
            <h2><i class="fas fa-sync-alt"></i> إعادة تحميل النظام</h2>
            <button class="btn success" onclick="reloadSystem()">إعادة تحميل النظام</button>
            <div id="reloadResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        let fixSteps = [
            { name: 'فحص البيانات الموجودة', completed: false },
            { name: 'تنظيف بيانات الموظفين', completed: false },
            { name: 'تنظيف بيانات الشكر', completed: false },
            { name: 'إعادة تنسيق جميع التواريخ', completed: false },
            { name: 'حفظ البيانات المحدثة', completed: false },
            { name: 'التحقق من النتائج', completed: false }
        ];

        // بدء الإصلاح الشامل
        function startCompleteFix() {
            const progressDiv = document.getElementById('fixProgress');
            const resultDiv = document.getElementById('fixResult');
            const stepsDiv = document.getElementById('fixSteps');
            
            progressDiv.style.display = 'block';
            resultDiv.style.display = 'none';
            
            // إعادة تعيين الخطوات
            fixSteps.forEach(step => step.completed = false);
            updateStepsDisplay();
            
            // تشغيل الإصلاح
            executeFixSteps();
        }

        // تنفيذ خطوات الإصلاح
        async function executeFixSteps() {
            for (let i = 0; i < fixSteps.length; i++) {
                await executeStep(i);
                updateProgress((i + 1) / fixSteps.length * 100);
                updateStepsDisplay();
                await sleep(500); // توقف قصير بين الخطوات
            }
            
            showFinalResult();
        }

        // تنفيذ خطوة واحدة
        async function executeStep(stepIndex) {
            try {
                switch (stepIndex) {
                    case 0:
                        await scanAllData();
                        break;
                    case 1:
                        await cleanEmployeeData();
                        break;
                    case 2:
                        await cleanThanksData();
                        break;
                    case 3:
                        await reformatAllDates();
                        break;
                    case 4:
                        await saveCleanData();
                        break;
                    case 5:
                        await verifyResults();
                        break;
                }
                fixSteps[stepIndex].completed = true;
            } catch (error) {
                fixSteps[stepIndex].failed = true;
                console.error(`فشل في الخطوة ${stepIndex}:`, error);
            }
        }

        // فحص جميع البيانات
        async function scanAllData() {
            const employees = JSON.parse(localStorage.getItem('employees') || '[]');
            const thanks = JSON.parse(localStorage.getItem('thanks') || '[]');
            console.log(`تم فحص ${employees.length} موظف و ${thanks.length} شكر`);
        }

        // تنظيف بيانات الموظفين
        async function cleanEmployeeData() {
            let employees = JSON.parse(localStorage.getItem('employees') || '[]');
            
            employees.forEach(emp => {
                const dateFields = ['birthDate', 'hireDate', 'lastPromotionDate', 'nextPromotionDate', 'currentDueDate', 'newDueDate', 'retirementDate'];
                dateFields.forEach(field => {
                    if (emp[field]) {
                        emp[field] = convertToGregorianISO(emp[field]);
                    }
                });
            });
            
            localStorage.setItem('employees', JSON.stringify(employees));
        }

        // تنظيف بيانات الشكر
        async function cleanThanksData() {
            let thanks = JSON.parse(localStorage.getItem('thanks') || '[]');
            
            thanks.forEach(thank => {
                if (thank.date) {
                    thank.date = convertToGregorianISO(thank.date);
                }
                if (thank.createdAt) {
                    thank.createdAt = convertToGregorianISO(thank.createdAt);
                }
            });
            
            localStorage.setItem('thanks', JSON.stringify(thanks));
        }

        // إعادة تنسيق جميع التواريخ
        async function reformatAllDates() {
            // التأكد من أن جميع التواريخ بتنسيق ISO صحيح
            let employees = JSON.parse(localStorage.getItem('employees') || '[]');
            let thanks = JSON.parse(localStorage.getItem('thanks') || '[]');
            
            // معالجة الموظفين
            employees.forEach(emp => {
                const dateFields = ['birthDate', 'hireDate', 'lastPromotionDate', 'nextPromotionDate', 'currentDueDate', 'newDueDate', 'retirementDate'];
                dateFields.forEach(field => {
                    if (emp[field] && !emp[field].match(/^\d{4}-\d{2}-\d{2}$/)) {
                        emp[field] = new Date().toISOString().split('T')[0];
                    }
                });
            });
            
            // معالجة الشكر
            thanks.forEach(thank => {
                if (thank.date && !thank.date.match(/^\d{4}-\d{2}-\d{2}$/)) {
                    thank.date = new Date().toISOString().split('T')[0];
                }
            });
            
            localStorage.setItem('employees', JSON.stringify(employees));
            localStorage.setItem('thanks', JSON.stringify(thanks));
        }

        // حفظ البيانات المنظفة
        async function saveCleanData() {
            // إنشاء نسخة احتياطية
            const employees = JSON.parse(localStorage.getItem('employees') || '[]');
            const thanks = JSON.parse(localStorage.getItem('thanks') || '[]');
            
            localStorage.setItem('employees_backup', JSON.stringify(employees));
            localStorage.setItem('thanks_backup', JSON.stringify(thanks));
            
            console.log('تم حفظ البيانات المنظفة مع نسخة احتياطية');
        }

        // التحقق من النتائج
        async function verifyResults() {
            const employees = JSON.parse(localStorage.getItem('employees') || '[]');
            const thanks = JSON.parse(localStorage.getItem('thanks') || '[]');
            
            let hasIssues = false;
            
            // فحص الموظفين
            employees.forEach(emp => {
                const dateFields = ['birthDate', 'hireDate', 'lastPromotionDate', 'nextPromotionDate', 'currentDueDate', 'newDueDate', 'retirementDate'];
                dateFields.forEach(field => {
                    if (emp[field] && !emp[field].match(/^\d{4}-\d{2}-\d{2}$/)) {
                        hasIssues = true;
                    }
                });
            });
            
            // فحص الشكر
            thanks.forEach(thank => {
                if (thank.date && !thank.date.match(/^\d{4}-\d{2}-\d{2}$/)) {
                    hasIssues = true;
                }
            });
            
            if (hasIssues) {
                throw new Error('لا تزال هناك مشاكل في التواريخ');
            }
        }

        // تحويل التاريخ إلى تنسيق ISO ميلادي
        function convertToGregorianISO(dateString) {
            if (!dateString) return null;
            
            // إذا كان التاريخ بتنسيق ISO صحيح، أرجعه كما هو
            if (dateString.match(/^\d{4}-\d{2}-\d{2}$/)) {
                return dateString;
            }
            
            // محاولة تحليل التاريخ
            const date = new Date(dateString);
            if (!isNaN(date.getTime())) {
                return date.toISOString().split('T')[0];
            }
            
            // إذا فشل، استخدم التاريخ الحالي
            return new Date().toISOString().split('T')[0];
        }

        // تحديث شريط التقدم
        function updateProgress(percentage) {
            const progressBar = document.getElementById('progressBar');
            progressBar.style.width = percentage + '%';
            progressBar.textContent = Math.round(percentage) + '%';
        }

        // تحديث عرض الخطوات
        function updateStepsDisplay() {
            const stepsDiv = document.getElementById('fixSteps');
            
            let html = '';
            fixSteps.forEach((step, index) => {
                let className = '';
                let status = '⏳ في الانتظار';
                
                if (step.completed) {
                    className = 'completed';
                    status = '✅ مكتمل';
                } else if (step.failed) {
                    className = 'failed';
                    status = '❌ فشل';
                }
                
                html += `
                    <div class="step ${className}">
                        <span>${index + 1}. ${step.name}</span>
                        <span class="step-status">${status}</span>
                    </div>
                `;
            });
            
            stepsDiv.innerHTML = html;
        }

        // عرض النتيجة النهائية
        function showFinalResult() {
            const resultDiv = document.getElementById('fixResult');
            const allCompleted = fixSteps.every(step => step.completed);
            
            resultDiv.style.display = 'block';
            
            if (allCompleted) {
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <h4>🎉 تم الإصلاح بنجاح!</h4>
                    <p><strong>✅ جميع التواريخ الآن تستخدم التقويم الميلادي</strong></p>
                    <p><strong>✅ تم حفظ نسخة احتياطية من البيانات</strong></p>
                    <p><strong>✅ النظام جاهز للاستخدام</strong></p>
                `;
            } else {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h4>❌ فشل في الإصلاح!</h4>
                    <p>حدثت مشاكل أثناء عملية الإصلاح. يرجى المحاولة مرة أخرى.</p>
                `;
            }
        }

        // التحقق النهائي
        function finalCheck() {
            const resultDiv = document.getElementById('checkResult');
            resultDiv.style.display = 'block';
            
            try {
                const employees = JSON.parse(localStorage.getItem('employees') || '[]');
                const thanks = JSON.parse(localStorage.getItem('thanks') || '[]');
                
                let issues = 0;
                
                // فحص الموظفين
                employees.forEach(emp => {
                    const dateFields = ['birthDate', 'hireDate', 'lastPromotionDate', 'nextPromotionDate', 'currentDueDate', 'newDueDate', 'retirementDate'];
                    dateFields.forEach(field => {
                        if (emp[field] && !emp[field].match(/^\d{4}-\d{2}-\d{2}$/)) {
                            issues++;
                        }
                    });
                });
                
                // فحص الشكر
                thanks.forEach(thank => {
                    if (thank.date && !thank.date.match(/^\d{4}-\d{2}-\d{2}$/)) {
                        issues++;
                    }
                });
                
                if (issues === 0) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h4>✅ النظام نظيف تماماً!</h4>
                        <p><strong>عدد الموظفين:</strong> ${employees.length}</p>
                        <p><strong>عدد كتب الشكر:</strong> ${thanks.length}</p>
                        <p><strong>مشاكل التواريخ:</strong> 0</p>
                        <p><strong>🎯 جميع التواريخ تستخدم التقويم الميلادي</strong></p>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        <h4>⚠️ لا تزال هناك مشاكل!</h4>
                        <p><strong>مشاكل التواريخ:</strong> ${issues}</p>
                        <p>يرجى تشغيل الإصلاح الشامل مرة أخرى</p>
                    `;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<h4>❌ خطأ في الفحص: ${error.message}</h4>`;
            }
        }

        // إعادة تحميل النظام
        function reloadSystem() {
            const resultDiv = document.getElementById('reloadResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result success';
            resultDiv.innerHTML = `
                <h4>🔄 جاري إعادة تحميل النظام...</h4>
                <p>سيتم إعادة تحميل الصفحة خلال 3 ثوان</p>
            `;
            
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 3000);
        }

        // وظيفة مساعدة للتوقف
        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        // تشغيل فحص أولي عند تحميل الصفحة
        window.addEventListener('load', function() {
            updateStepsDisplay();
        });
    </script>
</body>
</html>
