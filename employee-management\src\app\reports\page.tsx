'use client';

import { useState } from 'react';
import Link from 'next/link';

export default function ReportsPage() {
  const [selectedPeriod, setSelectedPeriod] = useState('thisMonth');

  const reportCards = [
    {
      title: 'تقرير العلاوات',
      description: 'تقرير شامل عن العلاوات المستحقة والممنوحة',
      icon: '💰',
      color: 'bg-green-500',
      href: '/reports/allowances',
      stats: '25 علاوة هذا الشهر'
    },
    {
      title: 'تقرير الترفيعات',
      description: 'تقرير عن الترفيعات المستحقة والممنوحة',
      icon: '📈',
      color: 'bg-blue-500',
      href: '/reports/promotions',
      stats: '12 ترفيع هذا الشهر'
    },
    {
      title: 'تقرير التقاعد',
      description: 'تقرير عن الموظفين المقاربين للتقاعد',
      icon: '🏖️',
      color: 'bg-orange-500',
      href: '/reports/retirement',
      stats: '8 موظفين خلال 6 أشهر'
    },
    {
      title: 'تقرير الموظفين',
      description: 'إحصائيات شاملة عن الموظفين',
      icon: '👥',
      color: 'bg-purple-500',
      href: '/reports/employees',
      stats: '150 موظف نشط'
    },
    {
      title: 'تقرير الرواتب',
      description: 'تحليل الرواتب والتكاليف',
      icon: '💵',
      color: 'bg-indigo-500',
      href: '/reports/salaries',
      stats: 'متوسط الراتب: 8,500 ريال'
    },
    {
      title: 'تقرير الأداء',
      description: 'تقييم أداء الموظفين والإدارات',
      icon: '📊',
      color: 'bg-pink-500',
      href: '/reports/performance',
      stats: 'معدل الأداء: 85%'
    }
  ];

  const quickStats = [
    { label: 'إجمالي الموظفين', value: '150', change: '+5', changeType: 'positive' },
    { label: 'العلاوات المستحقة', value: '32', change: '+8', changeType: 'positive' },
    { label: 'الترفيعات المستحقة', value: '18', change: '+3', changeType: 'positive' },
    { label: 'التنبيهات النشطة', value: '12', change: '-2', changeType: 'negative' },
  ];

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">التقارير والإحصائيات</h1>
            <p className="text-gray-600 mt-2">عرض شامل لجميع التقارير والإحصائيات</p>
          </div>
          <div className="flex items-center space-x-4 space-x-reverse">
            <select 
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="thisMonth">هذا الشهر</option>
              <option value="lastMonth">الشهر الماضي</option>
              <option value="thisQuarter">هذا الربع</option>
              <option value="thisYear">هذا العام</option>
            </select>
            <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
              تصدير التقارير
            </button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {quickStats.map((stat, index) => (
            <div key={index} className="bg-gray-50 p-4 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">{stat.label}</p>
                  <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                </div>
                <div className={`text-sm font-medium ${
                  stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
                }`}>
                  {stat.change}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Report Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {reportCards.map((report, index) => (
          <Link key={index} href={report.href} className="group">
            <div className="bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-300 group-hover:scale-105 overflow-hidden">
              <div className={`${report.color} h-2`}></div>
              <div className="p-6">
                <div className="flex items-center mb-4">
                  <div className="text-3xl mr-3">{report.icon}</div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors">
                      {report.title}
                    </h3>
                    <p className="text-sm text-gray-500">{report.stats}</p>
                  </div>
                </div>
                <p className="text-gray-600 mb-4">{report.description}</p>
                <div className="flex items-center text-blue-600 font-medium">
                  <span>عرض التقرير</span>
                  <svg className="w-4 h-4 mr-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>
            </div>
          </Link>
        ))}
      </div>

      {/* Recent Activity */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">النشاط الأخير</h2>
        <div className="space-y-4">
          {[
            { action: 'تم منح علاوة', employee: 'أحمد محمد علي', amount: '500 ريال', time: 'منذ ساعتين' },
            { action: 'تم ترفيع', employee: 'سارة أحمد محمود', position: 'مدير قسم', time: 'منذ 4 ساعات' },
            { action: 'تنبيه تقاعد', employee: 'محمد خالد عبدالله', note: 'يتقاعد خلال 6 أشهر', time: 'منذ يوم' },
            { action: 'تم إضافة موظف جديد', employee: 'فاطمة علي حسن', department: 'الموارد البشرية', time: 'منذ يومين' },
          ].map((activity, index) => (
            <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                  <svg className="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
                <div>
                  <p className="font-medium text-gray-900">
                    {activity.action} - {activity.employee}
                  </p>
                  <p className="text-sm text-gray-600">
                    {activity.amount || activity.position || activity.note || activity.department}
                  </p>
                </div>
              </div>
              <span className="text-sm text-gray-500">{activity.time}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-xl font-bold text-gray-900 mb-4">توزيع الموظفين حسب الإدارة</h3>
          <div className="space-y-3">
            {[
              { department: 'تقنية المعلومات', count: 45, percentage: 30 },
              { department: 'المبيعات', count: 38, percentage: 25 },
              { department: 'الموارد البشرية', count: 22, percentage: 15 },
              { department: 'المحاسبة', count: 30, percentage: 20 },
              { department: 'الإدارة', count: 15, percentage: 10 },
            ].map((dept, index) => (
              <div key={index} className="flex items-center justify-between">
                <span className="text-gray-700">{dept.department}</span>
                <div className="flex items-center">
                  <div className="w-32 bg-gray-200 rounded-full h-2 mr-3">
                    <div 
                      className="bg-blue-600 h-2 rounded-full" 
                      style={{ width: `${dept.percentage}%` }}
                    ></div>
                  </div>
                  <span className="text-sm text-gray-600 w-12">{dept.count}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-xl font-bold text-gray-900 mb-4">العلاوات والترفيعات الشهرية</h3>
          <div className="space-y-4">
            {[
              { month: 'يناير', allowances: 20, promotions: 8 },
              { month: 'فبراير', allowances: 25, promotions: 12 },
              { month: 'مارس', allowances: 18, promotions: 6 },
              { month: 'أبريل', allowances: 32, promotions: 15 },
            ].map((data, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                <span className="font-medium">{data.month}</span>
                <div className="flex space-x-4 space-x-reverse">
                  <span className="text-green-600">علاوات: {data.allowances}</span>
                  <span className="text-blue-600">ترفيعات: {data.promotions}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}