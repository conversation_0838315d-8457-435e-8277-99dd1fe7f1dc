'use client';

import { useState } from 'react';
import { 
  <PERSON>, 
  CardHeader, 
  CardBody, 
  Button, 
  Table, 
  TableHeader, 
  TableColumn, 
  TableBody, 
  TableRow, 
  TableCell,
  Input,
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem
} from '@nextui-org/react';
import { FaPlus, FaSearch, FaEllipsisV, FaEdit, FaTrash, FaEye } from 'react-icons/fa';
import Link from 'next/link';

// نموذج بيانات الموظف
type Employee = {
  id: string;
  fullName: string;
  employeeNumber: string;
  jobTitle: string;
  workLocation: string;
  hireDate: string;
  email: string | null;
  phoneNumber: string | null;
};

export default function EmployeesPage() {
  const [searchQuery, setSearchQuery] = useState('');
  
  // بيانات تجريبية للموظفين
  const employees: Employee[] = [
    {
      id: '1',
      fullName: 'أحمد محمد علي',
      employeeNumber: 'EMP001',
      jobTitle: 'مهندس برمجيات',
      workLocation: 'المقر الرئيسي',
      hireDate: '2025-01-15',
      email: '<EMAIL>',
      phoneNumber: '0123456789',
    },
    {
      id: '2',
      fullName: 'سارة أحمد محمود',
      employeeNumber: 'EMP002',
      jobTitle: 'محاسب',
      workLocation: 'الفرع الأول',
      hireDate: '2025-02-10',
      email: '<EMAIL>',
      phoneNumber: '0123456788',
    },
    {
      id: '3',
      fullName: 'محمد خالد عبدالله',
      employeeNumber: 'EMP003',
      jobTitle: 'مدير مبيعات',
      workLocation: 'الفرع الثاني',
      hireDate: '2025-03-05',
      email: '<EMAIL>',
      phoneNumber: '0123456787',
    },
    {
      id: '4',
      fullName: 'فاطمة علي حسن',
      employeeNumber: 'EMP004',
      jobTitle: 'مسؤول موارد بشرية',
      workLocation: 'المقر الرئيسي',
      hireDate: '2025-04-20',
      email: '<EMAIL>',
      phoneNumber: '0123456786',
    },
    {
      id: '5',
      fullName: 'خالد عبدالرحمن',
      employeeNumber: 'EMP005',
      jobTitle: 'مهندس شبكات',
      workLocation: 'الفرع الثالث',
      hireDate: '2025-04-25',
      email: '<EMAIL>',
      phoneNumber: '0123456785',
    },
  ];

  // تصفية الموظفين بناءً على البحث
  const filteredEmployees = employees.filter(
    (employee) =>
      employee.fullName.includes(searchQuery) ||
      employee.employeeNumber.includes(searchQuery) ||
      employee.jobTitle.includes(searchQuery) ||
      employee.workLocation.includes(searchQuery)
  );

  // دالة لتنسيق التاريخ
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA');
  };

  return (
    <div>
      <Card className="shadow-md">
        <CardHeader className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">إدارة الموظفين</h1>
          <Link href="/employees/new">
            <Button color="primary" startContent={<FaPlus />}>
              إضافة موظف جديد
            </Button>
          </Link>
        </CardHeader>
        <CardBody>
          <div className="mb-4">
            <Input
              placeholder="البحث عن موظف..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              startContent={<FaSearch className="text-gray-400" />}
              className="max-w-md"
            />
          </div>

          <Table aria-label="جدول الموظفين">
            <TableHeader>
              <TableColumn>الاسم</TableColumn>
              <TableColumn>الرقم الوظيفي</TableColumn>
              <TableColumn>العنوان الوظيفي</TableColumn>
              <TableColumn>موقع العمل</TableColumn>
              <TableColumn>تاريخ التعيين</TableColumn>
              <TableColumn>الإجراءات</TableColumn>
            </TableHeader>
            <TableBody>
              {filteredEmployees.map((employee) => (
                <TableRow key={employee.id}>
                  <TableCell>{employee.fullName}</TableCell>
                  <TableCell>{employee.employeeNumber}</TableCell>
                  <TableCell>{employee.jobTitle}</TableCell>
                  <TableCell>{employee.workLocation}</TableCell>
                  <TableCell>{formatDate(employee.hireDate)}</TableCell>
                  <TableCell>
                    <Dropdown>
                      <DropdownTrigger>
                        <Button isIconOnly variant="light">
                          <FaEllipsisV />
                        </Button>
                      </DropdownTrigger>
                      <DropdownMenu aria-label="إجراءات الموظف">
                        <DropdownItem
                          key="view"
                          startContent={<FaEye className="text-blue-500" />}
                        >
                          <Link href={`/employees/${employee.id}`}>
                            عرض التفاصيل
                          </Link>
                        </DropdownItem>
                        <DropdownItem
                          key="edit"
                          startContent={<FaEdit className="text-green-500" />}
                        >
                          <Link href={`/employees/${employee.id}/edit`}>
                            تعديل
                          </Link>
                        </DropdownItem>
                        <DropdownItem
                          key="delete"
                          className="text-danger"
                          color="danger"
                          startContent={<FaTrash className="text-red-500" />}
                        >
                          حذف
                        </DropdownItem>
                      </DropdownMenu>
                    </Dropdown>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardBody>
      </Card>
    </div>
  );
}
