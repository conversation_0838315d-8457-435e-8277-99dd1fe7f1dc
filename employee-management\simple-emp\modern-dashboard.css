/**
 * أنماط لوحة المعلومات الحديثة
 */

:root {
    --primary-color: #1e88e5;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --info-color: #17a2b8;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --body-bg: #f5f7fa;
    --card-bg: #ffffff;
    --text-color: #212529;
    --border-color: #e9ecef;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --transition-speed: 0.3s;
    --border-radius: 10px;
    --font-family: 'Cairo', sans-serif;
}

/* الوضع الداكن */
body.dark-mode {
    --body-bg: #121212;
    --card-bg: #1e1e1e;
    --text-color: #ffffff;
    --border-color: #333333;
    --shadow-color: rgba(0, 0, 0, 0.3);
}

body {
    background-color: var(--body-bg);
    color: var(--text-color);
    font-family: var(--font-family);
    transition: background-color var(--transition-speed), color var(--transition-speed);
}

/* تحسين بطاقات الإحصائيات */
.stats {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: 0 4px 15px var(--shadow-color);
    transition: transform var(--transition-speed), box-shadow var(--transition-speed);
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px var(--shadow-color);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, var(--primary-color), var(--info-color));
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: rgba(74, 108, 247, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    transition: background-color var(--transition-speed);
}

.stat-card:nth-child(2n) .stat-icon {
    background-color: rgba(40, 167, 69, 0.1);
}

.stat-card:nth-child(3n) .stat-icon {
    background-color: rgba(255, 193, 7, 0.1);
}

.stat-card:nth-child(4n) .stat-icon {
    background-color: rgba(220, 53, 69, 0.1);
}

.stat-icon i {
    font-size: 24px;
    color: var(--primary-color);
}

.stat-card:nth-child(2n) .stat-icon i {
    color: var(--success-color);
}

.stat-card:nth-child(3n) .stat-icon i {
    color: var(--warning-color);
}

.stat-card:nth-child(4n) .stat-icon i {
    color: var(--danger-color);
}

.stat-card h3 {
    font-size: 16px;
    margin: 0 0 10px;
    color: var(--text-color);
    font-weight: 600;
}

.stat-number {
    font-size: 28px;
    font-weight: 700;
    margin: 0;
    color: var(--primary-color);
    transition: color var(--transition-speed);
}

.stat-card:nth-child(2n) .stat-number {
    color: var(--success-color);
}

.stat-card:nth-child(3n) .stat-number {
    color: var(--warning-color);
}

.stat-card:nth-child(4n) .stat-number {
    color: var(--danger-color);
}

/* تحسين بطاقات الميزات */
.features {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.feature-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 25px;
    box-shadow: 0 4px 15px var(--shadow-color);
    transition: transform var(--transition-speed), box-shadow var(--transition-speed);
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 100%;
    transition: width var(--transition-speed);
}

.feature-card:hover::before {
    width: 100%;
    opacity: 0.1;
}

.feature-card.blue::before {
    background-color: var(--primary-color);
}

.feature-card.green::before {
    background-color: var(--success-color);
}

.feature-card.amber::before {
    background-color: var(--warning-color);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px var(--shadow-color);
}

.feature-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    transition: background-color var(--transition-speed);
}

.feature-card.blue .feature-icon {
    background-color: rgba(74, 108, 247, 0.1);
}

.feature-card.green .feature-icon {
    background-color: rgba(40, 167, 69, 0.1);
}

.feature-card.amber .feature-icon {
    background-color: rgba(255, 193, 7, 0.1);
}

.feature-icon i {
    font-size: 32px;
}

.feature-card.blue .feature-icon i {
    color: var(--primary-color);
}

.feature-card.green .feature-icon i {
    color: var(--success-color);
}

.feature-card.amber .feature-icon i {
    color: var(--warning-color);
}

.feature-card h2 {
    font-size: 20px;
    margin: 0 0 15px;
    font-weight: 600;
}

.feature-card p {
    margin: 0 0 20px;
    color: var(--secondary-color);
    line-height: 1.6;
}

.feature-card .btn {
    margin-top: auto;
}

/* تحسين لوحة المعلومات */
.dashboard {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.dashboard-section {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: 0 4px 15px var(--shadow-color);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
}

.section-header h2 {
    font-size: 18px;
    margin: 0;
    display: flex;
    align-items: center;
}

.section-header h2 i {
    margin-left: 10px;
    color: var(--primary-color);
}

.view-all {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 14px;
    display: flex;
    align-items: center;
}

.view-all i {
    margin-right: 5px;
    font-size: 12px;
}

/* تنسيق التنبيهات */
.alert {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 8px var(--shadow-color);
    border-right: 4px solid var(--primary-color);
    transition: transform var(--transition-speed);
}

.alert:hover {
    transform: translateX(-5px);
}

.blue-alert {
    border-right-color: var(--primary-color);
}

.green-alert {
    border-right-color: var(--success-color);
}

.amber-alert {
    border-right-color: var(--warning-color);
}

.alert-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.alert-header h3 {
    font-size: 16px;
    margin: 0;
    display: flex;
    align-items: center;
}

.blue-alert .alert-header h3 i {
    color: var(--primary-color);
}

.green-alert .alert-header h3 i {
    color: var(--success-color);
}

.amber-alert .alert-header h3 i {
    color: var(--warning-color);
}

.alert-header h3 i {
    margin-left: 8px;
}

.alert-badge {
    background-color: rgba(74, 108, 247, 0.1);
    color: var(--primary-color);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
}

.blue-alert .alert-badge {
    background-color: rgba(74, 108, 247, 0.1);
    color: var(--primary-color);
}

.green-alert .alert-badge {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.amber-alert .alert-badge {
    background-color: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
}

.alert p {
    margin: 0 0 10px;
    font-size: 14px;
}

.date {
    display: block;
    font-size: 12px;
    color: var(--secondary-color);
}

.date i {
    margin-left: 5px;
}

/* تنسيق بطاقات الموظفين */
.employee-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 8px var(--shadow-color);
    transition: transform var(--transition-speed);
}

.employee-card:hover {
    transform: translateX(-5px);
}

.employee-info {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.employee-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-left: 15px;
}

.employee-name-title h3 {
    font-size: 16px;
    margin: 0 0 5px;
}

.employee-name-title p {
    margin: 0;
    font-size: 14px;
    color: var(--secondary-color);
}

.employee-details {
    display: flex;
    justify-content: space-between;
    font-size: 13px;
    color: var(--secondary-color);
}

.employee-details p {
    margin: 0;
}

/* تحسين زر التبديل للوضع الداكن */
.dark-mode-toggle {
    position: fixed;
    bottom: 20px;
    left: 20px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--card-bg);
    box-shadow: 0 4px 15px var(--shadow-color);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 1000;
    transition: background-color var(--transition-speed), transform var(--transition-speed);
}

.dark-mode-toggle:hover {
    transform: scale(1.1);
}

.dark-mode-toggle i {
    font-size: 20px;
    color: var(--text-color);
    transition: color var(--transition-speed);
}

/* تحسينات للجوال */
@media (max-width: 768px) {
    .stats {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 15px;
    }

    .features {
        grid-template-columns: 1fr;
    }

    .dashboard {
        grid-template-columns: 1fr;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
    }

    .stat-number {
        font-size: 24px;
    }

    .feature-icon {
        width: 60px;
        height: 60px;
    }

    .feature-icon i {
        font-size: 24px;
    }
}

/* أنماط الحذف الجماعي */
.bulk-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
    padding: 0.5rem;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
}

.select-all-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.select-all-container input[type="checkbox"] {
    width: 1.2rem;
    height: 1.2rem;
    cursor: pointer;
}

.select-all-container label {
    font-weight: 500;
    cursor: pointer;
}

.employee-checkbox {
    width: 1.2rem;
    height: 1.2rem;
    cursor: pointer;
}

#deleteSelected {
    background-color: #dc3545;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

#deleteSelected:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
    opacity: 0.65;
}

#deleteSelected:not(:disabled):hover {
    background-color: #c82333;
}
