'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { 
  Navbar as NextUINavbar, 
  NavbarBrand, 
  NavbarContent, 
  NavbarItem, 
  NavbarMenu,
  NavbarMenuItem,
  NavbarMenuToggle,
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
  Button
} from '@nextui-org/react';

export default function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const pathname = usePathname();

  const isActive = (path: string) => pathname === path;

  return (
    <NextUINavbar 
      onMenuOpenChange={setIsMenuOpen}
      isMenuOpen={isMenuOpen}
      className="bg-primary text-white"
    >
      <NavbarContent>
        <NavbarMenuToggle
          aria-label={isMenuOpen ? "إغلاق القائمة" : "فتح القائمة"}
          className="sm:hidden text-white"
        />
        <NavbarBrand>
          <Link href="/" className="font-bold text-xl text-white">
            برنامج إدارة العلاوات والترفيعات
          </Link>
        </NavbarBrand>
      </NavbarContent>

      <NavbarContent className="hidden sm:flex gap-4" justify="center">
        <NavbarItem isActive={isActive('/')}>
          <Link href="/" className={`text-white ${isActive('/') ? 'font-bold' : ''}`}>
            الرئيسية
          </Link>
        </NavbarItem>
        
        <Dropdown>
          <NavbarItem>
            <DropdownTrigger>
              <Button
                variant="light"
                className="text-white p-0 bg-transparent"
              >
                الملفات التعريفية
              </Button>
            </DropdownTrigger>
          </NavbarItem>
          <DropdownMenu aria-label="الملفات التعريفية">
            <DropdownItem key="work-locations">
              <Link href="/profile/work-locations">مواقع العمل</Link>
            </DropdownItem>
            <DropdownItem key="job-titles">
              <Link href="/profile/job-titles">العناوين الوظيفية</Link>
            </DropdownItem>
            <DropdownItem key="education">
              <Link href="/profile/education">التحصيل الدراسي</Link>
            </DropdownItem>
            <DropdownItem key="leaves">
              <Link href="/profile/leaves">الإجازات</Link>
            </DropdownItem>
            <DropdownItem key="alert-period">
              <Link href="/profile/alert-period">فترة التنبيه</Link>
            </DropdownItem>
          </DropdownMenu>
        </Dropdown>

        <Dropdown>
          <NavbarItem>
            <DropdownTrigger>
              <Button
                variant="light"
                className="text-white p-0 bg-transparent"
              >
                الموظفين
              </Button>
            </DropdownTrigger>
          </NavbarItem>
          <DropdownMenu aria-label="الموظفين">
            <DropdownItem key="new-employee">
              <Link href="/employees/new">فتح ملف موظف</Link>
            </DropdownItem>
            <DropdownItem key="view-employees">
              <Link href="/employees">عرض الموظفين</Link>
            </DropdownItem>
          </DropdownMenu>
        </Dropdown>

        <Dropdown>
          <NavbarItem>
            <DropdownTrigger>
              <Button
                variant="light"
                className="text-white p-0 bg-transparent"
              >
                التشكرات والعقوبات
              </Button>
            </DropdownTrigger>
          </NavbarItem>
          <DropdownMenu aria-label="التشكرات والعقوبات">
            <DropdownItem key="appreciations">
              <Link href="/appreciations">التشكرات</Link>
            </DropdownItem>
            <DropdownItem key="penalties">
              <Link href="/penalties">العقوبات</Link>
            </DropdownItem>
          </DropdownMenu>
        </Dropdown>

        <Dropdown>
          <NavbarItem>
            <DropdownTrigger>
              <Button
                variant="light"
                className="text-white p-0 bg-transparent"
              >
                الإجازات والغيابات
              </Button>
            </DropdownTrigger>
          </NavbarItem>
          <DropdownMenu aria-label="الإجازات والغيابات">
            <DropdownItem key="leaves">
              <Link href="/leaves">الإجازات</Link>
            </DropdownItem>
            <DropdownItem key="absences">
              <Link href="/absences">الغيابات</Link>
            </DropdownItem>
          </DropdownMenu>
        </Dropdown>

        <Dropdown>
          <NavbarItem>
            <DropdownTrigger>
              <Button
                variant="light"
                className="text-white p-0 bg-transparent"
              >
                التقارير
              </Button>
            </DropdownTrigger>
          </NavbarItem>
          <DropdownMenu aria-label="التقارير">
            <DropdownItem key="allowance-report">
              <Link href="/reports/allowances">تقرير العلاوات المستحقة</Link>
            </DropdownItem>
            <DropdownItem key="promotion-report">
              <Link href="/reports/promotions">تقرير الترفيعات المستحقة</Link>
            </DropdownItem>
            <DropdownItem key="retirement-report">
              <Link href="/reports/retirement">قرب الإحالة إلى التقاعد</Link>
            </DropdownItem>
          </DropdownMenu>
        </Dropdown>

        <Dropdown>
          <NavbarItem>
            <DropdownTrigger>
              <Button
                variant="light"
                className="text-white p-0 bg-transparent"
              >
                التنبيهات
              </Button>
            </DropdownTrigger>
          </NavbarItem>
          <DropdownMenu aria-label="التنبيهات">
            <DropdownItem key="allowance-alerts">
              <Link href="/alerts/allowances">تنبيه العلاوات المستحقة</Link>
            </DropdownItem>
            <DropdownItem key="promotion-alerts">
              <Link href="/alerts/promotions">تنبيه الترفيعات المستحقة</Link>
            </DropdownItem>
            <DropdownItem key="retirement-alerts">
              <Link href="/alerts/retirement">تنبيه الإحالة إلى التقاعد</Link>
            </DropdownItem>
          </DropdownMenu>
        </Dropdown>
      </NavbarContent>

      <NavbarMenu className="bg-primary/95 pt-6">
        <NavbarMenuItem>
          <Link href="/" className="w-full text-white">
            الرئيسية
          </Link>
        </NavbarMenuItem>
        
        {/* القائمة المنسدلة للأجهزة المحمولة */}
        <NavbarMenuItem className="font-bold text-white mt-2">الملفات التعريفية:</NavbarMenuItem>
        <NavbarMenuItem>
          <Link href="/profile/work-locations" className="w-full text-white mr-4">
            مواقع العمل
          </Link>
        </NavbarMenuItem>
        <NavbarMenuItem>
          <Link href="/profile/job-titles" className="w-full text-white mr-4">
            العناوين الوظيفية
          </Link>
        </NavbarMenuItem>
        <NavbarMenuItem>
          <Link href="/profile/education" className="w-full text-white mr-4">
            التحصيل الدراسي
          </Link>
        </NavbarMenuItem>
        <NavbarMenuItem>
          <Link href="/profile/leaves" className="w-full text-white mr-4">
            الإجازات
          </Link>
        </NavbarMenuItem>
        <NavbarMenuItem>
          <Link href="/profile/alert-period" className="w-full text-white mr-4">
            فترة التنبيه
          </Link>
        </NavbarMenuItem>
        
        {/* باقي العناصر للأجهزة المحمولة */}
        {/* ... يمكن إضافة باقي العناصر بنفس الطريقة */}
      </NavbarMenu>
    </NextUINavbar>
  );
}
