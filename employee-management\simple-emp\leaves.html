<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإجازات - برنامج إدارة العلاوات والترفيعات</title>
    <link rel="stylesheet" href="styles.css">
    <!-- إضافة خط Cairo من Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- إضافة Font Awesome للأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- إضافة أنماط شريط التنقل المحسن -->
    <link rel="stylesheet" href="navbar.css">
    <!-- إضافة أنماط محسنة -->
    <link rel="stylesheet" href="enhanced-styles.css">
    <!-- إضافة أنماط خاصة بصفحات الملفات التعريفية -->
    <link rel="stylesheet" href="profile-pages.css">
    <!-- إضافة أنماط النوافذ المنبثقة -->
    <link rel="stylesheet" href="modals.css">
    <!-- إضافة أنماط الإشعارات -->
    <link rel="stylesheet" href="notifications.css">
</head>
<body>
    <header>
        <nav>
            <div class="logo">
                <i class="fas fa-chart-line logo-icon"></i>
                <span>برنامج إدارة العلاوات والترفيعات</span>
            </div>
            <div class="nav-links">
                <div class="dropdown">
                    <a href="index.html"><i class="fas fa-home"></i> الرئيسية</a>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle active"><i class="fas fa-id-card"></i> الملفات التعريفية <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="work-locations.html"><i class="fas fa-building"></i> مواقع العمل</a>
                        <a href="job-titles.html"><i class="fas fa-user-tie"></i> العناوين الوظيفية</a>
                        <a href="education-levels.html"><i class="fas fa-graduation-cap"></i> التحصيل الدراسي</a>
                        <a href="leaves.html" class="active"><i class="fas fa-calendar-alt"></i> الاجازات</a>
                        <a href="alert-periods.html"><i class="fas fa-bell"></i> فترة التنبيه</a>
                    </div>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-users"></i> الموظفين <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="employee-form.html"><i class="fas fa-user-plus"></i> فتح ملف موظف</a>
                        <a href="employees-list.html"><i class="fas fa-list"></i> عرض الموظفين</a>
                    </div>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-medal"></i> التشكرات والعقوبات <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="thanks.html"><i class="fas fa-award"></i> التشكرات</a>
                        <a href="penalties.html"><i class="fas fa-exclamation-triangle"></i> العقوبات</a>
                    </div>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-calendar-alt"></i> الاجازات والغيابات <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="employee-leaves.html"><i class="fas fa-calendar-check"></i> الاجازات</a>
                        <a href="absences.html"><i class="fas fa-calendar-times"></i> الغيابات</a>
                    </div>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-chart-bar"></i> التقارير <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="allowance-reports.html"><i class="fas fa-file-alt"></i> تقرير العلاوات المستحقة</a>
                        <a href="promotion-reports.html"><i class="fas fa-file-alt"></i> تقرير الترفيعات المستحقة</a>
                        <a href="retirement-reports.html"><i class="fas fa-user-clock"></i> قرب الاحالة الى التقاعد</a>
                    </div>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-bell"></i> التنبيهات <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="allowance-alerts.html"><i class="fas fa-bell"></i> تنبيه العلاوات المستحقة</a>
                        <a href="promotion-alerts.html"><i class="fas fa-bell"></i> تنبيه الترفيعات المستحقة</a>
                        <a href="retirement-alerts.html"><i class="fas fa-bell"></i> تنبيه الاحالة الى التقاعد</a>
                    </div>
                </div>
            </div>
            <div class="menu-toggle" id="menuToggle">
                <i class="fas fa-bars"></i>
            </div>
        </nav>
    </header>

    <main>
        <div class="profile-container">
            <h1 class="profile-title">إدارة أنواع الإجازات</h1>

            <div class="profile-content">
                <div class="profile-form-section">
                    <h2><i class="fas fa-plus-circle"></i> إضافة نوع إجازة جديد</h2>
                    <form id="addLeaveForm" class="profile-form">
                        <div class="form-group">
                            <label for="leaveName">اسم الإجازة: <span class="required">*</span></label>
                            <input type="text" id="leaveName" name="leaveName" required>
                        </div>
                        <div class="form-group">
                            <label for="leaveMaxDays">الحد الأقصى للأيام: <span class="required">*</span></label>
                            <input type="number" id="leaveMaxDays" name="leaveMaxDays" min="0" required>
                            <small class="form-text text-muted">أدخل 0 للإجازات التي تحدد مدتها حسب الطلب</small>
                        </div>
                        <div class="form-group">
                            <label for="leaveAffectsAllowancePromotion">تؤثر على العلاوة والترفيع:</label>
                            <select id="leaveAffectsAllowancePromotion" name="leaveAffectsAllowancePromotion">
                                <option value="false">لا</option>
                                <option value="true">نعم</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="leaveDescription">وصف الإجازة:</label>
                            <textarea id="leaveDescription" name="leaveDescription" rows="3"></textarea>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary"><i class="fas fa-save"></i> حفظ</button>
                            <button type="reset" class="btn btn-secondary"><i class="fas fa-undo"></i> إعادة تعيين</button>
                        </div>
                    </form>
                </div>

                <div class="profile-list-section">
                    <div class="list-header">
                        <h2><i class="fas fa-list"></i> قائمة أنواع الإجازات</h2>
                        <div class="list-actions">
                            <div class="search-container">
                                <input type="text" id="searchLeave" placeholder="بحث..." class="search-input">
                                <button class="search-btn"><i class="fas fa-search"></i></button>
                            </div>
                        </div>
                    </div>

                    <div class="profile-list">
                        <table id="leavesTable">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>اسم الإجازة</th>
                                    <th>الحد الأقصى للأيام</th>
                                    <th>تؤثر على العلاوة والترفيع</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- سيتم ملء هذا الجزء ديناميكياً -->
                            </tbody>
                        </table>
                    </div>

                    <div class="pagination">
                        <button class="pagination-btn" disabled><i class="fas fa-chevron-right"></i></button>
                        <span class="pagination-info">صفحة <span id="currentPage">1</span> من <span id="totalPages">1</span></span>
                        <button class="pagination-btn" disabled><i class="fas fa-chevron-left"></i></button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="footer-content">
            <div class="footer-logo">
                <i class="fas fa-chart-line"></i> برنامج إدارة العلاوات والترفيعات
            </div>
            <p>جميع الحقوق محفوظة &copy; 2025 - تم إنشاء النظام بواسطة الفني حسن علي موسى</p>
        </div>
    </footer>

    <div class="dark-mode-toggle" id="darkModeToggle">
        <i class="fas fa-moon"></i>
    </div>

    <!-- نافذة التعديل -->
    <div class="modal" id="editModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>تعديل نوع الإجازة</h2>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <form id="editLeaveForm" class="profile-form">
                    <input type="hidden" id="editLeaveId">
                    <div class="form-group">
                        <label for="editLeaveName">اسم الإجازة: <span class="required">*</span></label>
                        <input type="text" id="editLeaveName" name="editLeaveName" required>
                    </div>
                    <div class="form-group">
                        <label for="editLeaveMaxDays">الحد الأقصى للأيام: <span class="required">*</span></label>
                        <input type="number" id="editLeaveMaxDays" name="editLeaveMaxDays" min="0" required>
                        <small class="form-text text-muted">أدخل 0 للإجازات التي تحدد مدتها حسب الطلب</small>
                    </div>
                    <div class="form-group">
                        <label for="editLeaveAffectsAllowancePromotion">تؤثر على العلاوة والترفيع:</label>
                        <select id="editLeaveAffectsAllowancePromotion" name="editLeaveAffectsAllowancePromotion">
                            <option value="false">لا</option>
                            <option value="true">نعم</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="editLeaveDescription">وصف الإجازة:</label>
                        <textarea id="editLeaveDescription" name="editLeaveDescription" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button id="saveEditBtn" class="btn btn-primary"><i class="fas fa-save"></i> حفظ التغييرات</button>
                <button id="cancelEditBtn" class="btn btn-secondary"><i class="fas fa-times"></i> إلغاء</button>
            </div>
        </div>
    </div>

    <!-- نافذة تأكيد الحذف -->
    <div class="modal" id="deleteModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>تأكيد الحذف</h2>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من رغبتك في حذف نوع الإجازة "<span id="deleteLeaveName"></span>"؟</p>
                <p class="warning-text">هذا الإجراء لا يمكن التراجع عنه.</p>
            </div>
            <div class="modal-footer">
                <button id="confirmDeleteBtn" class="btn btn-danger"><i class="fas fa-trash"></i> تأكيد الحذف</button>
                <button id="cancelDeleteBtn" class="btn btn-secondary"><i class="fas fa-times"></i> إلغاء</button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
    <script src="leaves.js"></script>
</body>
</html>
