/**
 * ملف دمج مكتبة Select2 مع النظام
 * يحتوي على الوظائف اللازمة لتهيئة وتحديث القوائم المنسدلة مع خاصية البحث
 */

// تهيئة Select2 عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من وجود jQuery و Select2
    if (typeof jQuery !== 'undefined' && typeof jQuery.fn.select2 !== 'undefined') {
        initSelect2();
    } else {
        console.error('jQuery أو Select2 غير متوفر');
    }
});

/**
 * تهيئة مكتبة Select2 للقوائم المنسدلة
 */
function initSelect2() {
    // تطبيق Select2 على القوائم المنسدلة الرئيسية
    jQuery('#jobDescription').select2({
        language: "ar",
        dir: "rtl",
        width: '100%',
        placeholder: "اختر الوصف الوظيفي",
        allowClear: true
    });
    
    jQuery('#jobTitle').select2({
        language: "ar",
        dir: "rtl",
        width: '100%',
        placeholder: "اختر العنوان الوظيفي",
        allowClear: true
    });
    
    jQuery('#education').select2({
        language: "ar",
        dir: "rtl",
        width: '100%',
        placeholder: "اختر التحصيل الدراسي",
        allowClear: true
    });
    
    jQuery('#workLocation').select2({
        language: "ar",
        dir: "rtl",
        width: '100%',
        placeholder: "اختر موقع العمل",
        allowClear: true
    });
    
    // إضافة مستمعي الأحداث للقوائم المنسدلة
    addSelect2EventListeners();
}

/**
 * إضافة مستمعي الأحداث للقوائم المنسدلة مع Select2
 */
function addSelect2EventListeners() {
    // مستمع حدث تغيير الوصف الوظيفي
    jQuery('#jobDescription').on('change', function() {
        // استدعاء وظيفة تحديث العناوين الوظيفية الأصلية
        if (typeof updateJobTitles === 'function') {
            updateJobTitles();
            
            // إعادة تهيئة Select2 بعد تحديث القائمة
            setTimeout(function() {
                jQuery('#jobTitle').select2({
                    language: "ar",
                    dir: "rtl",
                    width: '100%',
                    placeholder: "اختر العنوان الوظيفي",
                    allowClear: true
                });
            }, 100);
        }
    });
    
    // مستمع حدث تغيير العنوان الوظيفي
    jQuery('#jobTitle').on('change', function() {
        // استدعاء وظيفة تحديث العنوان الوظيفي الجديد
        if (typeof updateNewJobTitle === 'function') {
            updateNewJobTitle();
        }
    });
    
    // مستمع حدث تغيير التحصيل الدراسي
    jQuery('#education').on('change', function() {
        // استدعاء وظيفة تحديث العنوان الوظيفي الجديد
        if (typeof updateNewJobTitle === 'function') {
            updateNewJobTitle();
        }
    });
}

/**
 * تحديث القوائم المنسدلة بعد تحميل البيانات
 */
function updateSelect2AfterDataLoad() {
    // إعادة تهيئة جميع القوائم المنسدلة
    jQuery('#jobDescription, #jobTitle, #education, #workLocation').select2({
        language: "ar",
        dir: "rtl",
        width: '100%',
        placeholder: "اختر...",
        allowClear: true
    });
}

/**
 * تحديث قائمة منسدلة محددة
 * @param {string} selectId - معرف القائمة المنسدلة
 * @param {string} placeholder - نص العنصر الافتراضي
 */
function updateSpecificSelect2(selectId, placeholder) {
    jQuery('#' + selectId).select2({
        language: "ar",
        dir: "rtl",
        width: '100%',
        placeholder: placeholder,
        allowClear: true
    });
}

// تصدير الوظائف للاستخدام في ملفات أخرى
window.initSelect2 = initSelect2;
window.updateSelect2AfterDataLoad = updateSelect2AfterDataLoad;
window.updateSpecificSelect2 = updateSpecificSelect2;
