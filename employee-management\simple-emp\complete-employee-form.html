<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ملف موظف شامل - نظام إدارة العلاوات والترفيعات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #667eea;
        }

        .header h1 {
            color: #667eea;
            margin-bottom: 10px;
            font-size: 2.2em;
        }

        .form-section {
            margin-bottom: 30px;
            padding: 25px;
            border: 1px solid #ddd;
            border-radius: 15px;
            background: #f9f9f9;
        }

        .section-title {
            color: #667eea;
            font-size: 1.4em;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            padding-bottom: 10px;
            border-bottom: 2px solid #667eea;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 600;
            margin-bottom: 8px;
            color: #333;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .required {
            color: #ef4444;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }

        .photo-upload {
            border: 3px dashed #667eea;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .photo-upload:hover {
            border-color: #764ba2;
            background: #f8f9ff;
        }

        .photo-preview {
            max-width: 150px;
            max-height: 150px;
            border-radius: 10px;
            margin: 10px auto;
            display: block;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
        }

        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }

        .form-actions {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #eee;
        }

        .back-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            font-size: 16px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .help-text {
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: 10px;
        }

        .checkbox-group input[type="checkbox"] {
            width: auto;
            margin: 0;
        }

        .file-input {
            display: none;
        }

        .success-message {
            background: #f0fdf4;
            border: 1px solid #10b981;
            color: #065f46;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: none;
        }

        .error-message {
            background: #fef2f2;
            border: 1px solid #ef4444;
            color: #991b1b;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- زر العودة -->
        <a href="index.html" class="back-btn">
            <i class="fas fa-arrow-right"></i>
            العودة للرئيسية
        </a>

        <!-- رسائل النجاح والخطأ -->
        <div id="successMessage" class="success-message">
            <i class="fas fa-check-circle"></i>
            تم حفظ بيانات الموظف بنجاح!
        </div>
        <div id="errorMessage" class="error-message">
            <i class="fas fa-exclamation-triangle"></i>
            <span id="errorText">حدث خطأ أثناء حفظ البيانات</span>
        </div>

        <!-- العنوان -->
        <div class="header">
            <h1><i class="fas fa-user-plus"></i> ملف موظف شامل</h1>
            <p>إضافة جميع بيانات الموظف بالتفصيل</p>
        </div>

        <form id="employeeForm">
            <!-- البيانات الشخصية -->
            <div class="form-section">
                <h2 class="section-title">
                    <i class="fas fa-user"></i>
                    البيانات الشخصية
                </h2>
                <div class="form-grid">
                    <div class="form-group">
                        <label>الاسم الكامل <span class="required">*</span></label>
                        <input type="text" id="fullName" name="fullName" required>
                        <div class="help-text">الاسم الثلاثي أو الرباعي كاملاً</div>
                    </div>
                    <div class="form-group">
                        <label>الرقم الوظيفي <span class="required">*</span></label>
                        <input type="text" id="employeeNumber" name="employeeNumber" required>
                        <div class="help-text">الرقم الوظيفي الرسمي</div>
                    </div>
                    <div class="form-group">
                        <label>رقم الهوية الوطنية</label>
                        <input type="text" id="nationalId" name="nationalId">
                    </div>
                    <div class="form-group">
                        <label>تاريخ الميلاد (ميلادي)</label>
                        <input type="date" id="birthDate" name="birthDate">
                        <div class="help-text">التاريخ بالتقويم الميلادي</div>
                    </div>
                    <div class="form-group">
                        <label>مكان الميلاد</label>
                        <input type="text" id="birthPlace" name="birthPlace">
                    </div>
                    <div class="form-group">
                        <label>الجنسية</label>
                        <select id="nationality" name="nationality">
                            <option value="">اختر الجنسية</option>
                            <option value="عراقي">عراقي</option>
                            <option value="أخرى">أخرى</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>الجنس</label>
                        <select id="gender" name="gender">
                            <option value="">اختر الجنس</option>
                            <option value="ذكر">ذكر</option>
                            <option value="أنثى">أنثى</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>الحالة الاجتماعية</label>
                        <select id="maritalStatus" name="maritalStatus">
                            <option value="">اختر الحالة</option>
                            <option value="أعزب">أعزب</option>
                            <option value="متزوج">متزوج</option>
                            <option value="مطلق">مطلق</option>
                            <option value="أرمل">أرمل</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>عدد الأطفال</label>
                        <input type="number" id="childrenCount" name="childrenCount" min="0">
                    </div>
                </div>
            </div>

            <!-- معلومات الاتصال -->
            <div class="form-section">
                <h2 class="section-title">
                    <i class="fas fa-phone"></i>
                    معلومات الاتصال
                </h2>
                <div class="form-grid">
                    <div class="form-group">
                        <label>رقم الهاتف المحمول</label>
                        <input type="tel" id="mobilePhone" name="mobilePhone">
                    </div>
                    <div class="form-group">
                        <label>رقم الهاتف الثابت</label>
                        <input type="tel" id="homePhone" name="homePhone">
                    </div>
                    <div class="form-group">
                        <label>البريد الإلكتروني</label>
                        <input type="email" id="email" name="email">
                    </div>
                    <div class="form-group">
                        <label>العنوان الحالي</label>
                        <textarea id="currentAddress" name="currentAddress" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label>العنوان الدائم</label>
                        <textarea id="permanentAddress" name="permanentAddress" rows="3"></textarea>
                    </div>
                </div>
            </div>

            <!-- البيانات الوظيفية -->
            <div class="form-section">
                <h2 class="section-title">
                    <i class="fas fa-briefcase"></i>
                    البيانات الوظيفية
                </h2>
                <div class="form-grid">
                    <div class="form-group">
                        <label>العنوان الوظيفي <span class="required">*</span></label>
                        <input type="text" id="jobTitle" name="jobTitle" required>
                    </div>
                    <div class="form-group">
                        <label>الوصف الوظيفي</label>
                        <select id="jobDescription" name="jobDescription">
                            <option value="">اختر الوصف</option>
                            <option value="تدريسي">تدريسي</option>
                            <option value="إداري">إداري</option>
                            <option value="فني">فني</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>موقع العمل <span class="required">*</span></label>
                        <input type="text" id="workLocation" name="workLocation" required>
                    </div>
                    <div class="form-group">
                        <label>القسم/الوحدة</label>
                        <input type="text" id="department" name="department">
                    </div>
                    <div class="form-group">
                        <label>تاريخ التوظيف (ميلادي) <span class="required">*</span></label>
                        <input type="date" id="hireDate" name="hireDate" required>
                        <div class="help-text">التاريخ بالتقويم الميلادي</div>
                    </div>
                    <div class="form-group">
                        <label>نوع التوظيف</label>
                        <select id="employmentType" name="employmentType">
                            <option value="">اختر النوع</option>
                            <option value="دائم">دائم</option>
                            <option value="مؤقت">مؤقت</option>
                            <option value="عقد">عقد</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>المرجع المباشر</label>
                        <input type="text" id="directSupervisor" name="directSupervisor">
                    </div>
                </div>
            </div>

            <!-- الراتب والدرجة -->
            <div class="form-section">
                <h2 class="section-title">
                    <i class="fas fa-money-bill-wave"></i>
                    الراتب والدرجة الوظيفية
                </h2>
                <div class="form-grid">
                    <div class="form-group">
                        <label>الراتب الأساسي الحالي <span class="required">*</span></label>
                        <input type="number" id="currentSalary" name="currentSalary" required>
                        <div class="help-text">بالدينار العراقي</div>
                    </div>
                    <div class="form-group">
                        <label>الدرجة الحالية <span class="required">*</span></label>
                        <input type="number" id="currentDegree" name="currentDegree" min="1" max="15" required>
                    </div>
                    <div class="form-group">
                        <label>المرحلة الحالية <span class="required">*</span></label>
                        <input type="number" id="currentStage" name="currentStage" min="1" max="20" required>
                    </div>
                    <div class="form-group">
                        <label>تاريخ آخر ترفيع (ميلادي)</label>
                        <input type="date" id="lastPromotionDate" name="lastPromotionDate">
                    </div>
                    <div class="form-group">
                        <label>تاريخ استحقاق الترفيع القادم (ميلادي)</label>
                        <input type="date" id="nextPromotionDate" name="nextPromotionDate">
                    </div>
                    <div class="form-group">
                        <label>تاريخ آخر علاوة (ميلادي)</label>
                        <input type="date" id="lastAllowanceDate" name="lastAllowanceDate">
                    </div>
                    <div class="form-group">
                        <label>تاريخ استحقاق العلاوة القادمة (ميلادي)</label>
                        <input type="date" id="nextAllowanceDate" name="nextAllowanceDate">
                    </div>
                    <div class="form-group">
                        <label>سنوات الخدمة</label>
                        <input type="number" id="seniority" name="seniority" min="0">
                        <div class="help-text">سيتم حسابها تلقائياً من تاريخ التوظيف</div>
                    </div>
                </div>
            </div>

            <!-- التحصيل الدراسي -->
            <div class="form-section">
                <h2 class="section-title">
                    <i class="fas fa-graduation-cap"></i>
                    التحصيل الدراسي
                </h2>
                <div class="form-grid">
                    <div class="form-group">
                        <label>أعلى مؤهل علمي</label>
                        <select id="highestDegree" name="highestDegree">
                            <option value="">اختر المؤهل</option>
                            <option value="ابتدائية">ابتدائية</option>
                            <option value="متوسطة">متوسطة</option>
                            <option value="إعدادية">إعدادية</option>
                            <option value="دبلوم">دبلوم</option>
                            <option value="بكالوريوس">بكالوريوس</option>
                            <option value="ماجستير">ماجستير</option>
                            <option value="دكتوراه">دكتوراه</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>التخصص</label>
                        <input type="text" id="specialization" name="specialization">
                    </div>
                    <div class="form-group">
                        <label>الجامعة/المؤسسة التعليمية</label>
                        <input type="text" id="university" name="university">
                    </div>
                    <div class="form-group">
                        <label>سنة التخرج</label>
                        <input type="number" id="graduationYear" name="graduationYear" min="1950" max="2030">
                    </div>
                    <div class="form-group">
                        <label>المعدل/التقدير</label>
                        <input type="text" id="gpa" name="gpa">
                    </div>
                </div>
            </div>

            <!-- معلومات إضافية -->
            <div class="form-section">
                <h2 class="section-title">
                    <i class="fas fa-info-circle"></i>
                    معلومات إضافية
                </h2>
                <div class="form-grid">
                    <div class="form-group">
                        <label>اللغات</label>
                        <input type="text" id="languages" name="languages">
                        <div class="help-text">مثال: العربية، الإنجليزية، الفرنسية</div>
                    </div>
                    <div class="form-group">
                        <label>المهارات</label>
                        <textarea id="skills" name="skills" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label>الدورات التدريبية</label>
                        <textarea id="trainingCourses" name="trainingCourses" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label>الخبرات السابقة</label>
                        <textarea id="previousExperience" name="previousExperience" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label>ملاحظات</label>
                        <textarea id="notes" name="notes" rows="3"></textarea>
                    </div>
                </div>
            </div>

            <!-- صورة الموظف -->
            <div class="form-section">
                <h2 class="section-title">
                    <i class="fas fa-camera"></i>
                    صورة الموظف
                </h2>
                <div class="form-group">
                    <div class="photo-upload" onclick="document.getElementById('photoInput').click()">
                        <i class="fas fa-camera" style="font-size: 2em; color: #667eea; margin-bottom: 10px;"></i>
                        <p>انقر لاختيار صورة الموظف</p>
                        <p style="font-size: 0.9em; color: #666;">يدعم JPG, PNG (حد أقصى 5MB)</p>
                        <input type="file" id="photoInput" class="file-input" accept="image/*" onchange="previewPhoto(this)">
                        <img id="photoPreview" class="photo-preview" style="display: none;">
                    </div>
                </div>
            </div>

            <!-- أزرار الحفظ -->
            <div class="form-actions">
                <button type="submit" class="btn btn-success">
                    <i class="fas fa-save"></i>
                    حفظ بيانات الموظف
                </button>
                <button type="button" class="btn btn-secondary" onclick="resetForm()">
                    <i class="fas fa-undo"></i>
                    إعادة تعيين
                </button>
                <button type="button" class="btn" onclick="previewData()">
                    <i class="fas fa-eye"></i>
                    معاينة البيانات
                </button>
            </div>
        </form>
    </div>

    <script>
        // معاينة الصورة
        function previewPhoto(input) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.getElementById('photoPreview');
                    preview.src = e.target.result;
                    preview.style.display = 'block';
                };
                reader.readAsDataURL(input.files[0]);
            }
        }

        // حساب سنوات الخدمة تلقائياً
        document.getElementById('hireDate').addEventListener('change', function() {
            const hireDate = new Date(this.value);
            const today = new Date();
            const years = Math.floor((today - hireDate) / (365.25 * 24 * 60 * 60 * 1000));
            if (years >= 0) {
                document.getElementById('seniority').value = years;
            }
        });

        // إعادة تعيين النموذج
        function resetForm() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟')) {
                document.getElementById('employeeForm').reset();
                document.getElementById('photoPreview').style.display = 'none';
                hideMessages();
            }
        }

        // معاينة البيانات
        function previewData() {
            const formData = new FormData(document.getElementById('employeeForm'));
            let preview = 'معاينة بيانات الموظف:\n\n';
            
            for (let [key, value] of formData.entries()) {
                if (value) {
                    const label = document.querySelector(`label[for="${key}"]`)?.textContent || key;
                    preview += `${label}: ${value}\n`;
                }
            }
            
            alert(preview);
        }

        // إخفاء الرسائل
        function hideMessages() {
            document.getElementById('successMessage').style.display = 'none';
            document.getElementById('errorMessage').style.display = 'none';
        }

        // عرض رسالة نجاح
        function showSuccess(message) {
            hideMessages();
            const successDiv = document.getElementById('successMessage');
            successDiv.style.display = 'block';
            successDiv.scrollIntoView({ behavior: 'smooth' });
        }

        // عرض رسالة خطأ
        function showError(message) {
            hideMessages();
            const errorDiv = document.getElementById('errorMessage');
            const errorText = document.getElementById('errorText');
            errorText.textContent = message;
            errorDiv.style.display = 'block';
            errorDiv.scrollIntoView({ behavior: 'smooth' });
        }

        // حفظ بيانات الموظف
        document.getElementById('employeeForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            try {
                // جمع البيانات
                const formData = new FormData(this);
                const employeeData = {};
                
                // تحويل FormData إلى object
                for (let [key, value] of formData.entries()) {
                    employeeData[key] = value;
                }
                
                // إضافة معرف فريد
                employeeData.id = Date.now();
                
                // إضافة تاريخ الإنشاء
                employeeData.createdAt = new Date().toISOString();
                
                // معالجة الصورة
                const photoInput = document.getElementById('photoInput');
                if (photoInput.files[0]) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        employeeData.photo = e.target.result;
                        saveEmployee(employeeData);
                    };
                    reader.readAsDataURL(photoInput.files[0]);
                } else {
                    saveEmployee(employeeData);
                }
                
            } catch (error) {
                showError('حدث خطأ أثناء معالجة البيانات: ' + error.message);
            }
        });

        // حفظ الموظف في التخزين المحلي
        function saveEmployee(employeeData) {
            try {
                // الحصول على الموظفين الحاليين
                const employees = JSON.parse(localStorage.getItem('employees') || '[]');
                
                // التحقق من عدم تكرار الرقم الوظيفي
                const existingEmployee = employees.find(emp => emp.employeeNumber === employeeData.employeeNumber);
                if (existingEmployee) {
                    showError('الرقم الوظيفي موجود مسبقاً. يرجى استخدام رقم آخر.');
                    return;
                }
                
                // إضافة الموظف الجديد
                employees.push(employeeData);
                
                // حفظ في التخزين المحلي
                localStorage.setItem('employees', JSON.stringify(employees));
                
                showSuccess('تم حفظ بيانات الموظف بنجاح!');
                
                // إعادة تعيين النموذج بعد 2 ثانية
                setTimeout(() => {
                    if (confirm('هل تريد إضافة موظف آخر؟')) {
                        resetForm();
                    } else {
                        window.location.href = 'employees-list.html';
                    }
                }, 2000);
                
            } catch (error) {
                showError('حدث خطأ أثناء حفظ البيانات: ' + error.message);
            }
        }

        // إخفاء الرسائل عند بدء الكتابة
        document.addEventListener('input', hideMessages);
    </script>
</body>
</html>
