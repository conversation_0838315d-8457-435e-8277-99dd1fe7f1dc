/**
 * ملف التوافق مع jQuery
 * يحتوي على وظائف لضمان توافق الكود الأصلي مع jQuery
 */

// تنفيذ الكود عند تحميل المستند
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من وجود jQuery
    if (typeof jQuery !== 'undefined') {
        console.log('تم تحميل jQuery بنجاح');

        // تهيئة Select2 بعد تحميل الصفحة
        initSelect2();

        // إضافة مستمعي أحداث jQuery
        addJQueryEventListeners();
    } else {
        console.error('لم يتم تحميل jQuery');
    }
});

/**
 * تهيئة مكتبة Select2 للقوائم المنسدلة
 */
function initSelect2() {
    console.log('بدء تهيئة Select2 من ملف jquery-compatibility.js...');

    // استخدام وظيفة initializeSelect2 من ملف select2-handler.js إذا كانت متوفرة
    if (typeof initializeSelect2 === 'function') {
        console.log('استخدام وظيفة initializeSelect2 من ملف select2-handler.js');
        initializeSelect2();
        return;
    }

    try {
        // تهيئة الوصف الوظيفي
        if (jQuery('#jobDescription').length) {
            jQuery('#jobDescription').select2({
                language: "ar",
                dir: "rtl",
                width: '100%',
                placeholder: "اختر الوصف الوظيفي",
                allowClear: true,
                dropdownParent: jQuery('body')
            });

            // حفظ القيمة الحالية
            const currentValue = jQuery('#jobDescription').val();
            if (currentValue) {
                // إعادة تعيين القيمة بعد التهيئة
                setTimeout(() => {
                    jQuery('#jobDescription').val(currentValue).trigger('change');
                }, 100);
            }

            console.log('تم تهيئة Select2 للوصف الوظيفي');
        }

        // تهيئة العنوان الوظيفي
        if (jQuery('#jobTitle').length) {
            jQuery('#jobTitle').select2({
                language: "ar",
                dir: "rtl",
                width: '100%',
                placeholder: "اختر العنوان الوظيفي",
                allowClear: true,
                dropdownParent: jQuery('body')
            });

            // حفظ القيمة الحالية
            const currentValue = jQuery('#jobTitle').val();
            if (currentValue) {
                // إعادة تعيين القيمة بعد التهيئة
                setTimeout(() => {
                    jQuery('#jobTitle').val(currentValue).trigger('change');
                }, 100);
            }

            console.log('تم تهيئة Select2 للعنوان الوظيفي');
        }

        // تهيئة التحصيل الدراسي
        if (jQuery('#education').length) {
            jQuery('#education').select2({
                language: "ar",
                dir: "rtl",
                width: '100%',
                placeholder: "اختر التحصيل الدراسي",
                allowClear: true,
                dropdownParent: jQuery('body')
            });

            // حفظ القيمة الحالية
            const currentValue = jQuery('#education').val();
            if (currentValue) {
                // إعادة تعيين القيمة بعد التهيئة
                setTimeout(() => {
                    jQuery('#education').val(currentValue).trigger('change');
                }, 100);
            }

            console.log('تم تهيئة Select2 للتحصيل الدراسي');
        }

        // تهيئة موقع العمل
        if (jQuery('#workLocation').length) {
            jQuery('#workLocation').select2({
                language: "ar",
                dir: "rtl",
                width: '100%',
                placeholder: "اختر موقع العمل",
                allowClear: true,
                dropdownParent: jQuery('body')
            });

            // حفظ القيمة الحالية
            const currentValue = jQuery('#workLocation').val();
            if (currentValue) {
                // إعادة تعيين القيمة بعد التهيئة
                setTimeout(() => {
                    jQuery('#workLocation').val(currentValue).trigger('change');
                }, 100);
            }

            console.log('تم تهيئة Select2 لموقع العمل');
        }
    } catch (error) {
        console.error('خطأ في تهيئة Select2:', error);
    }
}

/**
 * إضافة مستمعي أحداث jQuery
 */
function addJQueryEventListeners() {
    // مستمع حدث تغيير الوصف الوظيفي
    jQuery('#jobDescription').on('change', function() {
        console.log('تم تغيير الوصف الوظيفي');
        // استدعاء وظيفة تحديث العناوين الوظيفية الأصلية
        if (typeof updateJobTitles === 'function') {
            updateJobTitles();

            // إعادة تهيئة Select2 بعد تحديث القائمة
            setTimeout(function() {
                jQuery('#jobTitle').select2({
                    language: "ar",
                    dir: "rtl",
                    width: '100%',
                    placeholder: "اختر العنوان الوظيفي",
                    allowClear: true,
                    dropdownParent: jQuery('body')
                });

                // تحديث العنوان الوظيفي الجديد بعد إعادة التهيئة
                setTimeout(function() {
                    if (typeof updateNewJobTitle === 'function') {
                        updateNewJobTitle();
                    }
                }, 200);
            }, 300);
        }
    });

    // مستمع حدث تغيير العنوان الوظيفي
    jQuery('#jobTitle').on('change', function() {
        console.log('تم تغيير العنوان الوظيفي');
        // استدعاء وظيفة تحديث العنوان الوظيفي الجديد
        setTimeout(function() {
            if (typeof updateNewJobTitle === 'function') {
                updateNewJobTitle();
            }
        }, 100);
    });

    // مستمع حدث تغيير التحصيل الدراسي
    jQuery('#education').on('change', function() {
        console.log('تم تغيير التحصيل الدراسي');
        // استدعاء وظيفة تحديث العنوان الوظيفي الجديد
        setTimeout(function() {
            if (typeof updateNewJobTitle === 'function') {
                updateNewJobTitle();
            }
        }, 100);
    });

    // مستمع حدث تغيير تاريخ آخر ترفيع
    jQuery('#lastPromotionDate').on('change', function() {
        console.log('تم تغيير تاريخ آخر ترفيع');
        // استدعاء وظيفة تحديث العنوان الوظيفي الجديد
        setTimeout(function() {
            if (typeof updateNewJobTitle === 'function') {
                updateNewJobTitle();
            }
        }, 100);
    });

    // مستمع حدث النقر على زر التحديث التلقائي
    jQuery('#autoUpdateJobTitleBtn').on('click', function() {
        console.log('تم النقر على زر التحديث التلقائي');
        if (typeof updateNewJobTitle === 'function') {
            const updated = updateNewJobTitle();
            if (updated) {
                showNotification('تم تحديث العنوان الوظيفي الجديد باستخدام القيمة المحددة يدوياً', 'success');
            } else {
                showNotification('تم تحديث العنوان الوظيفي الجديد باستخدام نفس العنوان الوظيفي', 'success');
            }
        }
    });

    // مستمع حدث تغيير موقع العمل
    jQuery('#workLocation').on('select2:select', function() {
        // أي وظائف إضافية يمكن استدعاؤها هنا
    });
}

/**
 * تحديث Select2 بعد تحميل البيانات
 */
function updateSelect2AfterDataLoad() {
    console.log('تحديث Select2 بعد تحميل البيانات...');

    // استخدام وظيفة destroySelect2 من ملف select2-handler.js إذا كانت متوفرة
    if (typeof destroySelect2 === 'function') {
        console.log('استخدام وظيفة destroySelect2 من ملف select2-handler.js');
        destroySelect2();
    } else {
        // تدمير جميع مثيلات Select2 أولاً
        try {
            if (jQuery('#jobDescription').data('select2')) {
                jQuery('#jobDescription').select2('destroy');
                console.log('تم تدمير Select2 للوصف الوظيفي');
            }

            if (jQuery('#jobTitle').data('select2')) {
                jQuery('#jobTitle').select2('destroy');
                console.log('تم تدمير Select2 للعنوان الوظيفي');
            }

            if (jQuery('#education').data('select2')) {
                jQuery('#education').select2('destroy');
                console.log('تم تدمير Select2 للتحصيل الدراسي');
            }

            if (jQuery('#workLocation').data('select2')) {
                jQuery('#workLocation').select2('destroy');
                console.log('تم تدمير Select2 لموقع العمل');
            }
        } catch (error) {
            console.error('خطأ في تدمير Select2:', error);
        }
    }

    // إعادة تهيئة Select2 بعد فترة قصيرة
    setTimeout(function() {
        // استخدام وظيفة initializeSelect2 من ملف select2-handler.js إذا كانت متوفرة
        if (typeof initializeSelect2 === 'function') {
            console.log('استخدام وظيفة initializeSelect2 من ملف select2-handler.js');
            initializeSelect2();
        } else {
            initSelect2();
        }

        // تحديث العناوين الوظيفية بناءً على الوصف الوظيفي المحدد
        if (typeof updateJobTitles === 'function') {
            console.log('تحديث العناوين الوظيفية بعد إعادة تهيئة Select2');
            updateJobTitles();
        }

        // حفظ القيم الحالية وإعادة تعيينها
        setTimeout(function() {
            // الحصول على القيم الحالية
            const jobDescriptionValue = jQuery('#jobDescription').val();
            const jobTitleValue = jQuery('#jobTitle').val();
            const educationValue = jQuery('#education').val();
            const workLocationValue = jQuery('#workLocation').val();

            // إعادة تعيين القيم
            if (typeof updateSelect2Value === 'function') {
                if (jobDescriptionValue) updateSelect2Value('#jobDescription', jobDescriptionValue);
                if (jobTitleValue) updateSelect2Value('#jobTitle', jobTitleValue);
                if (educationValue) updateSelect2Value('#education', educationValue);
                if (workLocationValue) updateSelect2Value('#workLocation', workLocationValue);
            } else {
                if (jobDescriptionValue) jQuery('#jobDescription').val(jobDescriptionValue).trigger('change');
                if (jobTitleValue) jQuery('#jobTitle').val(jobTitleValue).trigger('change');
                if (educationValue) jQuery('#education').val(educationValue).trigger('change');
                if (workLocationValue) jQuery('#workLocation').val(workLocationValue).trigger('change');
            }
        }, 300);
    }, 200);
}

// تصدير الوظائف للاستخدام في ملفات أخرى
window.initSelect2 = initSelect2;
window.updateSelect2AfterDataLoad = updateSelect2AfterDataLoad;
window.addJQueryEventListeners = addJQueryEventListeners;
