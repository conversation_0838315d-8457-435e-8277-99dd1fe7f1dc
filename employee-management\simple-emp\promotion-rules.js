/**
 * ملف قواعد الترفيع والعلاوات
 * يحتوي على الخوارزميات الخاصة بتحديد العنوان الوظيفي الجديد والترفيعات
 */

// قواعد الترفيع للعناوين الوظيفية
const promotionRules = {
    // قواعد الترفيع للتدريسيين
    teaching: {
        // مدرس مساعد -> مدرس
        1: { nextId: 2, minYears: 2, requiredEducation: [4] }, // دكتوراه
        // مدرس -> أستاذ مساعد
        2: { nextId: 3, minYears: 4, requiredEducation: [4] }, // دكتوراه
        // أستاذ مساعد -> أستاذ
        3: { nextId: 4, minYears: 5, requiredEducation: [4] }, // دكتوراه
        // أستاذ (لا يوجد ترفيع بعده)
        4: { nextId: null, minYears: 0, requiredEducation: [] }
    },

    // قواعد الترفيع للفنيين
    technical: {
        // فني مختبر -> مهندس
        5: { nextId: 6, minYears: 3, requiredEducation: [2, 3, 4] }, // بكالوريوس أو ماجستير أو دكتوراه
        // مهندس -> مبرمج
        6: { nextId: 7, minYears: 4, requiredEducation: [2, 3, 4] }, // بكالوريوس أو ماجستير أو دكتوراه
        // مبرمج (لا يوجد ترفيع بعده)
        7: { nextId: null, minYears: 0, requiredEducation: [] }
    },

    // قواعد الترفيع للإداريين
    administrative: {
        // موظف إداري -> رئيس قسم
        8: { nextId: 9, minYears: 5, requiredEducation: [1, 2, 3, 4] }, // دبلوم أو بكالوريوس أو ماجستير أو دكتوراه
        // رئيس قسم -> مدير
        9: { nextId: 10, minYears: 7, requiredEducation: [2, 3, 4] }, // بكالوريوس أو ماجستير أو دكتوراه
        // مدير (لا يوجد ترفيع بعده)
        10: { nextId: null, minYears: 0, requiredEducation: [] }
    }
};

/**
 * تحديد العنوان الوظيفي الجديد بناءً على العنوان الوظيفي الحالي
 * @param {string} currentJobTitleId - معرف العنوان الوظيفي الحالي
 * @param {string} jobDescription - الوصف الوظيفي (تدريسي، فني، إداري)
 * @param {string} educationId - معرف التحصيل الدراسي
 * @param {Date} lastPromotionDate - تاريخ آخر ترفيع
 * @returns {Object} - معلومات العنوان الوظيفي الجديد
 */
function determineNextJobTitle(currentJobTitleId, jobDescription, educationId, lastPromotionDate) {
    try {
        // التحقق من صحة المدخلات
        if (!currentJobTitleId || !jobDescription || !educationId) {
            throw new Error('جميع الحقول مطلوبة: العنوان الوظيفي الحالي، الوصف الوظيفي، والتحصيل الدراسي');
        }

        // التحويل إلى أرقام
        currentJobTitleId = parseInt(currentJobTitleId);
        educationId = parseInt(educationId);

        if (isNaN(currentJobTitleId) || isNaN(educationId)) {
            throw new Error('يجب أن تكون المعرفات أرقاماً صحيحة');
        }

        // الحصول على قواعد الترفيع للفئة الوظيفية
        const categoryRules = promotionRules[jobDescription];
        if (!categoryRules) {
            return { 
                id: null, 
                name: 'غير متوفر', 
                eligible: false, 
                reason: 'فئة وظيفية غير معروفة',
                error: 'job_category_unknown'
            };
        }

        // الحصول على قاعدة الترفيع للعنوان الوظيفي الحالي
        const rule = categoryRules[currentJobTitleId];
        if (!rule) {
            return { 
                id: null, 
                name: 'غير متوفر', 
                eligible: false, 
                reason: 'لا توجد قاعدة ترفيع لهذا العنوان الوظيفي',
                error: 'no_promotion_rule'
            };
        }

        // التحقق من وجود عنوان وظيفي تالي
        if (rule.nextId === null) {
            return { 
                id: null, 
                name: 'أعلى درجة وظيفية', 
                eligible: false, 
                reason: 'وصل إلى أعلى درجة وظيفية',
                error: 'max_level_reached'
            };
        }

        // التحقق من التحصيل الدراسي
        if (rule.requiredEducation.length > 0 && !rule.requiredEducation.includes(educationId)) {
            return {
                id: rule.nextId,
                name: getJobTitleName(rule.nextId),
                eligible: false,
                reason: 'التحصيل الدراسي غير كافٍ للترفيع',
                error: 'education_not_sufficient'
            };
        }

        // التحقق من المدة المطلوبة للترفيع
        if (lastPromotionDate) {
            const lastDate = new Date(lastPromotionDate);
            const today = new Date();
            const yearsDiff = (today - lastDate) / (1000 * 60 * 60 * 24 * 365);
            
            if (yearsDiff < rule.minYears) {
                return {
                    id: rule.nextId,
                    name: getJobTitleName(rule.nextId),
                    eligible: false,
                    reason: `لم تكتمل المدة المطلوبة للترفيع (${rule.minYears} سنوات)`,
                    error: 'insufficient_time',
                    remainingYears: rule.minYears - yearsDiff
                };
            }
        }

        // إذا تم اجتياز جميع الشروط
        return {
            id: rule.nextId,
            name: getJobTitleName(rule.nextId),
            eligible: true,
            reason: 'مستحق للترفيع'
        };

    } catch (error) {
        console.error('خطأ في تحديد العنوان الوظيفي الجديد:', error);
        return {
            id: null,
            name: 'خطأ',
            eligible: false,
            reason: error.message,
            error: 'system_error'
        };
    }
}

/**
 * الحصول على اسم العنوان الوظيفي من المعرف
 * @param {number} jobTitleId - معرف العنوان الوظيفي
 * @returns {string} - اسم العنوان الوظيفي
 */
function getJobTitleName(titleId) {
    try {
        const jobTitles = JSON.parse(localStorage.getItem('jobTitles') || '[]');
        const title = jobTitles.find(t => t.id === titleId);
        return title ? title.name : 'غير معروف';
    } catch (error) {
        console.error('خطأ في الحصول على اسم العنوان الوظيفي:', error);
        return 'غير معروف';
    }
}

/**
 * تحديث العنوان الوظيفي الجديد في النموذج
 */
function updateNewJobTitle() {
    const jobDescriptionSelect = document.getElementById('jobDescription');
    const jobTitleSelect = document.getElementById('jobTitle');
    const educationSelect = document.getElementById('education');
    const lastPromotionDateInput = document.getElementById('lastPromotionDate');
    const newJobTitleInput = document.getElementById('newJobTitle');

    if (!jobDescriptionSelect || !jobTitleSelect || !educationSelect || !lastPromotionDateInput || !newJobTitleInput) {
        return;
    }

    const jobDescription = jobDescriptionSelect.value;
    const currentJobTitleId = jobTitleSelect.value;
    const educationId = educationSelect.value;
    const lastPromotionDate = lastPromotionDateInput.value;

    if (!jobDescription || !currentJobTitleId || !educationId || !lastPromotionDate) {
        newJobTitleInput.value = '';
        return;
    }

    const nextJobTitle = determineNextJobTitle(currentJobTitleId, jobDescription, educationId, lastPromotionDate);

    if (nextJobTitle.eligible) {
        newJobTitleInput.value = nextJobTitle.name;
        newJobTitleInput.classList.remove('text-danger');
        newJobTitleInput.classList.add('text-success');
    } else {
        newJobTitleInput.value = `${nextJobTitle.name} (${nextJobTitle.reason})`;
        newJobTitleInput.classList.remove('text-success');
        newJobTitleInput.classList.add('text-danger');
    }
}

// إضافة مستمعي الأحداث عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تحديث العنوان الوظيفي الجديد عند تحميل الصفحة
    setTimeout(function() {
        updateNewJobTitle();
    }, 500);
});
