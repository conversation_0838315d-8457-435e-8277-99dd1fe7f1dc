<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سلم الرواتب - برنامج إدارة العلاوات والترفيعات</title>
    <!-- إضافة ملف المتغيرات الموحدة -->
    <link rel="stylesheet" href="variables.css">
    <!-- إضافة الأنماط الأساسية -->
    <link rel="stylesheet" href="styles.css">
    <!-- إضافة خط Cairo من Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <!-- إضافة Font Awesome للأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- إضافة أنماط خاصة بصفحات الملفات التعريفية -->
    <link rel="stylesheet" href="profile-pages.css">
    <!-- إضافة أنماط النوافذ المنبثقة -->
    <link rel="stylesheet" href="modals.css">
    <!-- إضافة أنماط الإشعارات -->
    <link rel="stylesheet" href="notifications.css">
    <style>
        .salary-scale-container {
            overflow-x: auto;
            margin-bottom: 20px;
        }

        .salary-scale-table {
            width: 100%;
            border-collapse: collapse;
            min-width: 1000px;
        }

        .salary-scale-table th, .salary-scale-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }

        .salary-scale-table th {
            background-color: var(--primary-color);
            color: white;
            position: sticky;
            top: 0;
        }

        .salary-scale-table tr:nth-child(even) {
            background-color: var(--background-color);
        }

        .salary-scale-table tr:hover {
            background-color: var(--dropdown-hover);
        }

        .salary-scale-table .grade-cell {
            background-color: var(--primary-color);
            color: white;
            font-weight: bold;
        }

        .salary-scale-table .annual-allowance-cell {
            background-color: var(--secondary-color);
            color: white;
            font-weight: bold;
        }

        .salary-scale-table .promotion-years-cell {
            background-color: var(--info-color);
            color: white;
            font-weight: bold;
        }

        .editable-cell {
            cursor: pointer;
            position: relative;
        }

        .editable-cell:hover::after {
            content: '\f044';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            position: absolute;
            top: 2px;
            right: 2px;
            font-size: 10px;
            color: var(--primary-color);
        }

        .salary-info {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }

        .info-card {
            background-color: var(--card-color);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            width: calc(33% - 10px);
            box-shadow: var(--box-shadow);
        }

        .info-card h3 {
            margin-top: 0;
            color: var(--primary-color);
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 8px;
        }

        .info-card p {
            margin: 5px 0;
        }

        .info-card strong {
            color: var(--text-color);
        }

        @media (max-width: 768px) {
            .info-card {
                width: 100%;
            }
        }

        .action-buttons {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <header>
        <nav>
            <div class="logo">
                <i class="fas fa-chart-line logo-icon"></i>
                برنامج إدارة العلاوات والترفيعات
            </div>
            <div class="nav-links">
                <div class="dropdown">
                    <a href="index.html"><i class="fas fa-home"></i> الرئيسية</a>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-id-card"></i> الملفات التعريفية <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="work-locations.html"><i class="fas fa-building"></i> مواقع العمل</a>
                        <a href="job-titles.html"><i class="fas fa-user-tie"></i> العناوين الوظيفية</a>
                        <a href="education-levels.html"><i class="fas fa-graduation-cap"></i> التحصيل الدراسي</a>
                        <a href="leaves.html"><i class="fas fa-calendar-alt"></i> الاجازات</a>
                        <a href="alert-periods.html"><i class="fas fa-bell"></i> فترة التنبيه</a>
                    </div>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-users"></i> الموظفين <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="employee-form.html"><i class="fas fa-user-plus"></i> فتح ملف موظف</a>
                        <a href="employees-list.html"><i class="fas fa-list"></i> عرض الموظفين</a>
                    </div>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-medal"></i> التشكرات والعقوبات <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="thanks.html"><i class="fas fa-award"></i> التشكرات</a>
                        <a href="penalties.html"><i class="fas fa-exclamation-triangle"></i> العقوبات</a>
                    </div>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-calendar-alt"></i> الاجازات والغيابات <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="employee-leaves.html"><i class="fas fa-calendar-check"></i> الاجازات</a>
                        <a href="absences.html"><i class="fas fa-calendar-times"></i> الغيابات</a>
                    </div>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle active"><i class="fas fa-chart-bar"></i> التقارير <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="allowance-reports.html"><i class="fas fa-file-alt"></i> تقرير العلاوات المستحقة</a>
                        <a href="promotion-reports.html"><i class="fas fa-file-alt"></i> تقرير الترفيعات المستحقة</a>
                        <a href="retirement-reports.html"><i class="fas fa-user-clock"></i> قرب الاحالة الى التقاعد</a>
                        <a href="salary-scale.html" class="active"><i class="fas fa-money-bill-wave"></i> سلم الرواتب</a>
                    </div>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-bell"></i> التنبيهات <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="allowance-alerts.html"><i class="fas fa-bell"></i> تنبيه العلاوات المستحقة</a>
                        <a href="promotion-alerts.html"><i class="fas fa-bell"></i> تنبيه الترفيعات المستحقة</a>
                        <a href="retirement-alerts.html"><i class="fas fa-bell"></i> تنبيه الاحالة الى التقاعد</a>
                    </div>
                </div>
            </div>
            <div class="menu-toggle" id="menuToggle">
                <i class="fas fa-bars"></i>
            </div>
        </nav>
    </header>

    <main>
        <div class="profile-container">
            <h1 class="profile-title">سلم الرواتب</h1>

            <div class="salary-info">
                <div class="info-card">
                    <h3><i class="fas fa-info-circle"></i> معلومات سلم الرواتب</h3>
                    <p><strong>عدد الدرجات:</strong> 10 درجات</p>
                    <p><strong>سنوات الخدمة:</strong> 11 سنة</p>
                    <p><strong>آخر تحديث:</strong> <span id="lastUpdate">2025/05/01</span></p>
                </div>
                <div class="info-card">
                    <h3><i class="fas fa-cogs"></i> إعدادات الترفيع</h3>
                    <p><strong>الدرجات 1-5:</strong> ترفيع كل 5 سنوات</p>
                    <p><strong>الدرجات 6-10:</strong> ترفيع كل 4 سنوات</p>
                </div>
                <div class="info-card">
                    <h3><i class="fas fa-money-bill-wave"></i> العلاوات</h3>
                    <p><strong>الدرجة 1:</strong> 20 ألف دينار سنوياً</p>
                    <p><strong>الدرجة 2:</strong> 17 ألف دينار سنوياً</p>
                    <p><strong>الدرجة 3:</strong> 10 آلاف دينار سنوياً</p>
                </div>
            </div>

            <div class="action-buttons">
                <button id="printSalaryScale" class="btn btn-primary"><i class="fas fa-print"></i> طباعة سلم الرواتب</button>
                <button id="exportSalaryScale" class="btn btn-secondary"><i class="fas fa-file-export"></i> تصدير إلى Excel</button>
                <button id="editSalaryScale" class="btn btn-accent"><i class="fas fa-edit"></i> تعديل سلم الرواتب</button>
            </div>

            <div class="salary-scale-container">
                <table id="salaryScaleTable" class="salary-scale-table">
                    <thead>
                        <tr>
                            <th rowspan="2">الدرجة الوظيفية</th>
                            <th rowspan="2">العلاوة السنوية</th>
                            <th rowspan="2">عدد سنوات الترفيع</th>
                            <th colspan="11">سنوات الخدمة</th>
                        </tr>
                        <tr>
                            <th>1</th>
                            <th>2</th>
                            <th>3</th>
                            <th>4</th>
                            <th>5</th>
                            <th>6</th>
                            <th>7</th>
                            <th>8</th>
                            <th>9</th>
                            <th>10</th>
                            <th>11</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- سيتم ملء هذا الجزء ديناميكياً -->
                    </tbody>
                </table>
            </div>
        </div>
    </main>

    <footer>
        <div class="footer-content">
            <div class="footer-logo">
                <i class="fas fa-chart-line"></i> برنامج إدارة العلاوات والترفيعات
            </div>
            <p>جميع الحقوق محفوظة &copy; 2025 - تم إنشاء النظام بواسطة الفني حسن علي موسى</p>
        </div>
    </footer>

    <div class="dark-mode-toggle" id="darkModeToggle">
        <i class="fas fa-moon"></i>
    </div>

    <!-- نافذة تعديل قيمة الراتب -->
    <div class="modal" id="editSalaryModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>تعديل قيمة الراتب</h2>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <form id="editSalaryForm" class="profile-form">
                    <input type="hidden" id="editGrade">
                    <input type="hidden" id="editYear">
                    <div class="form-group">
                        <label for="editSalaryValue">قيمة الراتب: <span class="required">*</span></label>
                        <input type="number" id="editSalaryValue" name="editSalaryValue" min="0" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button id="saveSalaryBtn" class="btn btn-primary"><i class="fas fa-save"></i> حفظ</button>
                <button id="cancelSalaryBtn" class="btn btn-secondary"><i class="fas fa-times"></i> إلغاء</button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
    <script src="salary-scale.js"></script>
</body>
</html>
