/* متغيرات الألوان الموحدة للنظام */
:root {
    /* الألوان الأساسية */
    --primary-color: #2563eb;
    --primary-light: #60a5fa;
    --primary-dark: #1d4ed8;
    
    /* الألوان الثانوية */
    --secondary-color: #059669;
    --secondary-light: #10b981;
    --secondary-dark: #047857;
    
    /* ألوان التحذير */
    --warning-color: #d97706;
    --warning-light: #f59e0b;
    --warning-dark: #b45309;
    
    /* ألوان الخطر */
    --danger-color: #dc2626;
    --danger-light: #ef4444;
    --danger-dark: #b91c1c;
    
    /* ألوان المعلومات */
    --info-color: #6366f1;
    --info-light: #818cf8;
    --info-dark: #4f46e5;
    
    /* ألوان إضافية */
    --purple-color: #8b5cf6;
    --purple-light: #a78bfa;
    --purple-dark: #7c3aed;
    
    /* ألوان النص */
    --text-color: #111827;
    --text-light: #4b5563;
    --text-muted: #9ca3af;
    
    /* ألوان الخلفية */
    --background-color: #f3f4f6;
    --card-color: #ffffff;
    --border-color: #e5e7eb;
    
    /* ألوان شريط التنقل */
    --navbar-bg: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    --navbar-text: #ffffff;
    --navbar-hover: rgba(255, 255, 255, 0.15);
    --navbar-active: #ffffff;
    --navbar-active-bg: rgba(255, 255, 255, 0.2);
    
    /* ألوان القائمة المنسدلة */
    --dropdown-bg: #ffffff;
    --dropdown-text: #1f2937;
    --dropdown-hover: #f3f4f6;
    --dropdown-hover-text: #3b82f6;
    --dropdown-border: rgba(0, 0, 0, 0.05);
    
    /* ألوان الوضع المظلم */
    --dark-background: #121212;
    --dark-card: #1e1e1e;
    --dark-text: #e0e0e0;
    --dark-text-light: #a0a0a0;
    --dark-border: #2d2d2d;
    
    /* تأثيرات */
    --border-radius: 12px;
    --box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --transition: all 0.3s ease;
    
    /* تدرجات لونية */
    --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    --gradient-secondary: linear-gradient(135deg, var(--secondary-color), var(--secondary-light));
    --gradient-warning: linear-gradient(135deg, var(--warning-color), var(--warning-light));
    --gradient-danger: linear-gradient(135deg, var(--danger-color), var(--danger-light));
    --gradient-info: linear-gradient(135deg, var(--info-color), var(--info-light));
    --gradient-purple: linear-gradient(135deg, var(--purple-color), var(--purple-light));
}

/* تعريفات الوضع المظلم */
.dark-mode {
    --background-color: var(--dark-background);
    --card-color: var(--dark-card);
    --text-color: var(--dark-text);
    --text-light: var(--dark-text-light);
    --border-color: var(--dark-border);
    
    /* تعديل ألوان الوضع المظلم */
    --primary-light: #3b82f6;
    --secondary-light: #059669;
    --warning-light: #d97706;
    --danger-light: #dc2626;
    --info-light: #6366f1;
    --purple-light: #8b5cf6;
    
    /* تعديل تدرجات الوضع المظلم */
    --gradient-primary: linear-gradient(135deg, #1e40af, #3b82f6);
    --gradient-secondary: linear-gradient(135deg, #047857, #059669);
    --gradient-warning: linear-gradient(135deg, #b45309, #d97706);
    --gradient-danger: linear-gradient(135deg, #b91c1c, #dc2626);
    --gradient-info: linear-gradient(135deg, #4f46e5, #6366f1);
    --gradient-purple: linear-gradient(135deg, #7c3aed, #8b5cf6);
}
