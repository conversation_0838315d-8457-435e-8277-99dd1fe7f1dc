<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إجازات الموظفين - برنامج إدارة العلاوات والترفيعات</title>
    <link rel="stylesheet" href="styles.css">
    <!-- إضافة خط Cairo من Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <!-- إضافة Font Awesome للأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- إضافة أنماط خاصة بصفحة الإجازات -->
    <link rel="stylesheet" href="employee-leaves.css">
    <!-- إضافة أنماط النوافذ المنبثقة -->
    <link rel="stylesheet" href="modals.css">
    <!-- إضافة أنماط الإشعارات -->
    <link rel="stylesheet" href="notifications.css">
</head>
<body>
    <header>
        <nav>
            <div class="logo">
                <i class="fas fa-chart-line logo-icon"></i>
                برنامج إدارة العلاوات والترفيعات
            </div>
            <div class="nav-links">
                <div class="dropdown">
                    <a href="index.html"><i class="fas fa-home"></i> الرئيسية</a>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-id-card"></i> الملفات التعريفية <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="work-locations.html"><i class="fas fa-building"></i> مواقع العمل</a>
                        <a href="job-titles.html"><i class="fas fa-user-tie"></i> العناوين الوظيفية</a>
                        <a href="education-levels.html"><i class="fas fa-graduation-cap"></i> التحصيل الدراسي</a>
                        <a href="leaves.html"><i class="fas fa-calendar-alt"></i> الاجازات</a>
                        <a href="alert-periods.html"><i class="fas fa-bell"></i> فترة التنبيه</a>
                    </div>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-users"></i> الموظفين <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="employee-form.html"><i class="fas fa-user-plus"></i> فتح ملف موظف</a>
                        <a href="employees-list.html"><i class="fas fa-list"></i> عرض الموظفين</a>
                    </div>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-medal"></i> التشكرات والعقوبات <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="thanks.html"><i class="fas fa-award"></i> التشكرات</a>
                        <a href="penalties.html"><i class="fas fa-exclamation-triangle"></i> العقوبات</a>
                    </div>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle active"><i class="fas fa-calendar-alt"></i> الاجازات والغيابات <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="employee-leaves.html" class="active"><i class="fas fa-calendar-check"></i> الاجازات</a>
                        <a href="absences.html"><i class="fas fa-calendar-times"></i> الغيابات</a>
                    </div>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-chart-bar"></i> التقارير <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="allowance-reports.html"><i class="fas fa-file-alt"></i> تقرير العلاوات المستحقة</a>
                        <a href="promotion-reports.html"><i class="fas fa-file-alt"></i> تقرير الترفيعات المستحقة</a>
                        <a href="retirement-reports.html"><i class="fas fa-user-clock"></i> قرب الاحالة الى التقاعد</a>
                    </div>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-bell"></i> التنبيهات <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="allowance-alerts.html"><i class="fas fa-bell"></i> تنبيه العلاوات المستحقة</a>
                        <a href="promotion-alerts.html"><i class="fas fa-bell"></i> تنبيه الترفيعات المستحقة</a>
                        <a href="retirement-alerts.html"><i class="fas fa-bell"></i> تنبيه الاحالة الى التقاعد</a>
                    </div>
                </div>
            </div>
            <div class="menu-toggle" id="menuToggle">
                <i class="fas fa-bars"></i>
            </div>
        </nav>
    </header>

    <main>
        <div class="employee-leaves-container">
            <h1 class="page-title">إدارة إجازات الموظفين</h1>

            <div class="employee-leaves-content">
                <div class="employee-leaves-form-section">
                    <h2><i class="fas fa-plus-circle"></i> إضافة إجازة جديدة</h2>
                    <form id="addLeaveForm" class="employee-leaves-form">
                        <div class="form-group">
                            <label for="employeeId">الموظف: <span class="required">*</span></label>
                            <select id="employeeId" name="employeeId" required>
                                <option value="">اختر الموظف</option>
                                <!-- سيتم ملء هذا الجزء ديناميكياً -->
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="leaveType">نوع الإجازة: <span class="required">*</span></label>
                            <select id="leaveType" name="leaveType" required>
                                <option value="">اختر نوع الإجازة</option>
                                <!-- سيتم ملء هذا الجزء ديناميكياً -->
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="startDate">تاريخ البداية: <span class="required">*</span></label>
                            <input type="date" id="startDate" name="startDate" required>
                        </div>
                        <div class="form-group">
                            <label for="endDate">تاريخ النهاية: <span class="required">*</span></label>
                            <input type="date" id="endDate" name="endDate" required>
                        </div>
                        <div class="form-group">
                            <label for="leaveDays">عدد الأيام:</label>
                            <input type="number" id="leaveDays" name="leaveDays" readonly>
                        </div>
                        <div class="form-group">
                            <label for="leaveNumber">رقم الكتاب:</label>
                            <input type="text" id="leaveNumber" name="leaveNumber">
                        </div>
                        <div class="form-group">
                            <label for="leaveNotes">ملاحظات:</label>
                            <textarea id="leaveNotes" name="leaveNotes" rows="3"></textarea>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary"><i class="fas fa-save"></i> حفظ</button>
                            <button type="reset" class="btn btn-secondary"><i class="fas fa-undo"></i> إعادة تعيين</button>
                        </div>
                    </form>
                </div>

                <div class="employee-leaves-list-section">
                    <div class="list-header">
                        <h2><i class="fas fa-list"></i> قائمة الإجازات</h2>
                        <div class="list-actions">
                            <div class="search-container">
                                <input type="text" id="searchLeave" placeholder="بحث..." class="search-input">
                                <button class="search-btn"><i class="fas fa-search"></i></button>
                            </div>
                            <select id="filterLeaveType" class="filter-select">
                                <option value="">جميع الأنواع</option>
                                <!-- سيتم ملء هذا الجزء ديناميكياً -->
                            </select>
                            <select id="filterLeaveStatus" class="filter-select">
                                <option value="">جميع الحالات</option>
                                <option value="active">جارية</option>
                                <option value="completed">منتهية</option>
                                <option value="upcoming">قادمة</option>
                            </select>
                        </div>
                    </div>

                    <div class="employee-leaves-list">
                        <table id="employeeLeavesTable">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>الموظف</th>
                                    <th>نوع الإجازة</th>
                                    <th>تاريخ البداية</th>
                                    <th>تاريخ النهاية</th>
                                    <th>عدد الأيام</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- سيتم ملء هذا الجزء ديناميكياً -->
                            </tbody>
                        </table>
                    </div>

                    <div class="pagination">
                        <button class="pagination-btn" disabled><i class="fas fa-chevron-right"></i></button>
                        <span class="pagination-info">صفحة <span id="currentPage">1</span> من <span id="totalPages">1</span></span>
                        <button class="pagination-btn" disabled><i class="fas fa-chevron-left"></i></button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="footer-content">
            <div class="footer-logo">
                <i class="fas fa-chart-line"></i> برنامج إدارة العلاوات والترفيعات
            </div>
            <p>جميع الحقوق محفوظة &copy; 2025 - تم إنشاء النظام بواسطة الفني حسن علي موسى</p>
        </div>
    </footer>

    <div class="dark-mode-toggle" id="darkModeToggle">
        <i class="fas fa-moon"></i>
    </div>

    <!-- نافذة عرض تفاصيل الإجازة -->
    <div class="modal" id="viewLeaveModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>تفاصيل الإجازة</h2>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <div class="leave-details">
                    <div class="info-group">
                        <label>الموظف:</label>
                        <span id="viewEmployeeName"></span>
                    </div>
                    <div class="info-group">
                        <label>نوع الإجازة:</label>
                        <span id="viewLeaveType"></span>
                    </div>
                    <div class="info-group">
                        <label>تاريخ البداية:</label>
                        <span id="viewStartDate"></span>
                    </div>
                    <div class="info-group">
                        <label>تاريخ النهاية:</label>
                        <span id="viewEndDate"></span>
                    </div>
                    <div class="info-group">
                        <label>عدد الأيام:</label>
                        <span id="viewLeaveDays"></span>
                    </div>
                    <div class="info-group">
                        <label>رقم الكتاب:</label>
                        <span id="viewLeaveNumber"></span>
                    </div>
                    <div class="info-group">
                        <label>ملاحظات:</label>
                        <span id="viewLeaveNotes"></span>
                    </div>
                    <div class="info-group">
                        <label>الحالة:</label>
                        <span id="viewLeaveStatus"></span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button id="editLeaveBtn" class="btn btn-primary"><i class="fas fa-edit"></i> تعديل</button>
                <button id="closeViewBtn" class="btn btn-secondary"><i class="fas fa-times"></i> إغلاق</button>
            </div>
        </div>
    </div>

    <!-- نافذة تعديل الإجازة -->
    <div class="modal" id="editModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>تعديل الإجازة</h2>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <form id="editLeaveForm" class="employee-leaves-form">
                    <input type="hidden" id="editLeaveId">
                    <div class="form-group">
                        <label for="editEmployeeId">الموظف: <span class="required">*</span></label>
                        <select id="editEmployeeId" name="editEmployeeId" required>
                            <option value="">اختر الموظف</option>
                            <!-- سيتم ملء هذا الجزء ديناميكياً -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="editLeaveType">نوع الإجازة: <span class="required">*</span></label>
                        <select id="editLeaveType" name="editLeaveType" required>
                            <option value="">اختر نوع الإجازة</option>
                            <!-- سيتم ملء هذا الجزء ديناميكياً -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="editStartDate">تاريخ البداية: <span class="required">*</span></label>
                        <input type="date" id="editStartDate" name="editStartDate" required>
                    </div>
                    <div class="form-group">
                        <label for="editEndDate">تاريخ النهاية: <span class="required">*</span></label>
                        <input type="date" id="editEndDate" name="editEndDate" required>
                    </div>
                    <div class="form-group">
                        <label for="editLeaveDays">عدد الأيام:</label>
                        <input type="number" id="editLeaveDays" name="editLeaveDays" readonly>
                    </div>
                    <div class="form-group">
                        <label for="editLeaveNumber">رقم الكتاب:</label>
                        <input type="text" id="editLeaveNumber" name="editLeaveNumber">
                    </div>
                    <div class="form-group">
                        <label for="editLeaveNotes">ملاحظات:</label>
                        <textarea id="editLeaveNotes" name="editLeaveNotes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button id="saveEditBtn" class="btn btn-primary"><i class="fas fa-save"></i> حفظ التغييرات</button>
                <button id="cancelEditBtn" class="btn btn-secondary"><i class="fas fa-times"></i> إلغاء</button>
            </div>
        </div>
    </div>

    <!-- نافذة تأكيد الحذف -->
    <div class="modal" id="deleteModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>تأكيد الحذف</h2>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من رغبتك في حذف هذه الإجازة؟</p>
                <p class="warning-text">هذا الإجراء لا يمكن التراجع عنه.</p>
            </div>
            <div class="modal-footer">
                <button id="confirmDeleteBtn" class="btn btn-danger"><i class="fas fa-trash"></i> تأكيد الحذف</button>
                <button id="cancelDeleteBtn" class="btn btn-secondary"><i class="fas fa-times"></i> إلغاء</button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
    <script src="employee-leaves.js"></script>
</body>
</html>
