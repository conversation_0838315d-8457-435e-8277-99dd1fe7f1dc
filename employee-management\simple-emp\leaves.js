// انتظر حتى يتم تحميل المستند بالكامل
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة صفحة الإجازات
    initLeavesPage();
});

// تهيئة صفحة الإجازات
function initLeavesPage() {
    // تحميل أنواع الإجازات
    loadLeaves();

    // تهيئة نموذج إضافة نوع إجازة
    initAddLeaveForm();

    // تهيئة البحث
    initSearch();

    // تهيئة النوافذ المنبثقة
    initModals();
}

// تحميل أنواع الإجازات
function loadLeaves() {
    // في الإصدار النهائي، سيتم استبدال هذا بطلب API لجلب البيانات من الخادم
    // هنا نستخدم بيانات تجريبية للعرض

    // التحقق من وجود بيانات في التخزين المحلي
    let leaves = localStorage.getItem('leaves');

    if (!leaves) {
        // إذا لم تكن هناك بيانات، استخدم بيانات افتراضية
        leaves = [
            { id: 1, name: 'إجازة سنوية', maxDays: 30, affectsAllowancePromotion: false, description: 'الإجازة السنوية المستحقة للموظف' },
            { id: 2, name: 'إجازة مرضية', maxDays: 15, affectsAllowancePromotion: false, description: 'إجازة مرضية بتقرير طبي' },
            { id: 3, name: 'إجازة بدون راتب', maxDays: 0, affectsAllowancePromotion: true, description: 'إجازة بدون راتب حسب الأيام المحددة - تؤثر على العلاوة والترفيع' },
            { id: 4, name: 'إجازة أمومة', maxDays: 90, affectsAllowancePromotion: false, description: 'إجازة أمومة للموظفات' },
            { id: 5, name: 'إجازة حج', maxDays: 21, affectsAllowancePromotion: false, description: 'إجازة لأداء فريضة الحج' },
            { id: 6, name: 'إجازة خمس سنوات', maxDays: 1825, affectsAllowancePromotion: true, description: 'إجازة لمدة خمس سنوات - تؤثر على العلاوة والترفيع' },
            { id: 7, name: 'إجازة دراسية', maxDays: 365, affectsAllowancePromotion: false, description: 'إجازة لإكمال الدراسة' },
            { id: 8, name: 'إجازة مرضية (عجز صحي)', maxDays: 180, affectsAllowancePromotion: false, description: 'إجازة مرضية لمدة 6 أشهر بسبب عجز صحي' }
        ];

        // حفظ البيانات في التخزين المحلي
        localStorage.setItem('leaves', JSON.stringify(leaves));
    } else {
        // تحويل البيانات من JSON إلى كائن JavaScript
        leaves = JSON.parse(leaves);
    }

    // عرض البيانات في الجدول
    displayLeaves(leaves);
}

// عرض أنواع الإجازات في الجدول
function displayLeaves(leaves) {
    const tableBody = document.querySelector('#leavesTable tbody');
    if (!tableBody) return;

    // مسح محتوى الجدول
    tableBody.innerHTML = '';

    // إذا لم تكن هناك أنواع إجازات، عرض رسالة
    if (leaves.length === 0) {
        const emptyRow = document.createElement('tr');
        emptyRow.innerHTML = `<td colspan="5" class="text-center">لا توجد أنواع إجازات. أضف نوع إجازة جديد.</td>`;
        tableBody.appendChild(emptyRow);
        return;
    }

    // إضافة الصفوف إلى الجدول
    leaves.forEach((leave, index) => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${index + 1}</td>
            <td>${leave.name}</td>
            <td>${leave.maxDays > 0 ? leave.maxDays + ' يوم' : 'حسب الطلب'}</td>
            <td>${leave.affectsAllowancePromotion ? 'نعم' : 'لا'}</td>
            <td>
                <div class="action-buttons">
                    <button class="action-btn edit-btn" data-id="${leave.id}" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn delete-btn" data-id="${leave.id}" data-name="${leave.name}" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;
        tableBody.appendChild(row);
    });

    // إضافة مستمعي الأحداث لأزرار التعديل والحذف
    addActionButtonsEventListeners();
}

// إضافة مستمعي الأحداث لأزرار التعديل والحذف
function addActionButtonsEventListeners() {
    // أزرار التعديل
    const editButtons = document.querySelectorAll('.edit-btn');
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const leaveId = this.getAttribute('data-id');
            openEditModal(leaveId);
        });
    });

    // أزرار الحذف
    const deleteButtons = document.querySelectorAll('.delete-btn');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const leaveId = this.getAttribute('data-id');
            const leaveName = this.getAttribute('data-name');
            openDeleteModal(leaveId, leaveName);
        });
    });
}

// تهيئة نموذج إضافة نوع إجازة
function initAddLeaveForm() {
    const addForm = document.getElementById('addLeaveForm');
    if (!addForm) return;

    addForm.addEventListener('submit', function(e) {
        e.preventDefault();

        // الحصول على قيم الحقول
        const name = document.getElementById('leaveName').value.trim();
        const maxDays = document.getElementById('leaveMaxDays').value;
        const affectsAllowancePromotion = document.getElementById('leaveAffectsAllowancePromotion').value === 'true';
        const description = document.getElementById('leaveDescription').value.trim();

        // التحقق من صحة البيانات
        if (!name) {
            alert('يرجى إدخال اسم الإجازة');
            return;
        }

        if (!maxDays || maxDays < 0) {
            alert('يرجى إدخال الحد الأقصى للأيام بشكل صحيح');
            return;
        }

        // إضافة نوع الإجازة الجديد
        addLeave(name, maxDays, affectsAllowancePromotion, description);

        // إعادة تعيين النموذج
        this.reset();
    });
}

// إضافة نوع إجازة جديد
function addLeave(name, maxDays, affectsAllowancePromotion, description) {
    // الحصول على أنواع الإجازات الحالية
    let leaves = JSON.parse(localStorage.getItem('leaves') || '[]');

    // إنشاء معرف فريد جديد
    const newId = leaves.length > 0 ? Math.max(...leaves.map(leave => leave.id)) + 1 : 1;

    // إنشاء نوع الإجازة الجديد
    const newLeave = {
        id: newId,
        name: name,
        maxDays: parseInt(maxDays),
        affectsAllowancePromotion: affectsAllowancePromotion,
        description: description
    };

    // إضافة نوع الإجازة الجديد إلى المصفوفة
    leaves.push(newLeave);

    // حفظ البيانات المحدثة في التخزين المحلي
    localStorage.setItem('leaves', JSON.stringify(leaves));

    // تحديث عرض الجدول
    displayLeaves(leaves);

    // عرض رسالة نجاح
    showNotification('تم إضافة نوع الإجازة بنجاح', 'success');
}

// تهيئة البحث
function initSearch() {
    const searchInput = document.getElementById('searchLeave');
    if (!searchInput) return;

    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.trim().toLowerCase();

        // الحصول على أنواع الإجازات
        const leaves = JSON.parse(localStorage.getItem('leaves') || '[]');

        // تصفية أنواع الإجازات بناءً على مصطلح البحث
        const filteredLeaves = leaves.filter(leave => {
            return leave.name.toLowerCase().includes(searchTerm) ||
                   leave.description.toLowerCase().includes(searchTerm);
        });

        // عرض النتائج المصفاة
        displayLeaves(filteredLeaves);
    });
}

// تهيئة النوافذ المنبثقة
function initModals() {
    // الحصول على عناصر النوافذ المنبثقة
    const editModal = document.getElementById('editModal');
    const deleteModal = document.getElementById('deleteModal');
    const closeButtons = document.querySelectorAll('.close-modal');

    // أزرار الإغلاق
    closeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const modal = this.closest('.modal');
            closeModal(modal);
        });
    });

    // إغلاق النافذة عند النقر خارجها
    window.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal')) {
            closeModal(e.target);
        }
    });

    // زر إلغاء التعديل
    const cancelEditBtn = document.getElementById('cancelEditBtn');
    if (cancelEditBtn) {
        cancelEditBtn.addEventListener('click', function() {
            closeModal(editModal);
        });
    }

    // زر حفظ التعديل
    const saveEditBtn = document.getElementById('saveEditBtn');
    if (saveEditBtn) {
        saveEditBtn.addEventListener('click', function() {
            saveEditedLeave();
        });
    }

    // زر إلغاء الحذف
    const cancelDeleteBtn = document.getElementById('cancelDeleteBtn');
    if (cancelDeleteBtn) {
        cancelDeleteBtn.addEventListener('click', function() {
            closeModal(deleteModal);
        });
    }

    // زر تأكيد الحذف
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    if (confirmDeleteBtn) {
        confirmDeleteBtn.addEventListener('click', function() {
            const leaveId = this.getAttribute('data-id');
            deleteLeave(leaveId);
            closeModal(deleteModal);
        });
    }
}

// فتح نافذة التعديل
function openEditModal(leaveId) {
    // الحصول على نوع الإجازة المراد تعديله
    const leaves = JSON.parse(localStorage.getItem('leaves') || '[]');
    const leave = leaves.find(leave => leave.id == leaveId);

    if (!leave) return;

    // ملء حقول النموذج بالبيانات الحالية
    document.getElementById('editLeaveId').value = leave.id;
    document.getElementById('editLeaveName').value = leave.name;
    document.getElementById('editLeaveMaxDays').value = leave.maxDays;
    document.getElementById('editLeaveAffectsAllowancePromotion').value = leave.affectsAllowancePromotion.toString();
    document.getElementById('editLeaveDescription').value = leave.description || '';

    // فتح النافذة
    const editModal = document.getElementById('editModal');
    openModal(editModal);
}

// فتح نافذة تأكيد الحذف
function openDeleteModal(leaveId, leaveName) {
    // عرض اسم نوع الإجازة في رسالة التأكيد
    document.getElementById('deleteLeaveName').textContent = leaveName;

    // تعيين معرف نوع الإجازة لزر التأكيد
    document.getElementById('confirmDeleteBtn').setAttribute('data-id', leaveId);

    // فتح النافذة
    const deleteModal = document.getElementById('deleteModal');
    openModal(deleteModal);
}

// حفظ نوع الإجازة المعدل
function saveEditedLeave() {
    // الحصول على قيم الحقول
    const id = document.getElementById('editLeaveId').value;
    const name = document.getElementById('editLeaveName').value.trim();
    const maxDays = document.getElementById('editLeaveMaxDays').value;
    const affectsAllowancePromotion = document.getElementById('editLeaveAffectsAllowancePromotion').value === 'true';
    const description = document.getElementById('editLeaveDescription').value.trim();

    // التحقق من صحة البيانات
    if (!name) {
        alert('يرجى إدخال اسم الإجازة');
        return;
    }

    if (!maxDays || maxDays < 0) {
        alert('يرجى إدخال الحد الأقصى للأيام بشكل صحيح');
        return;
    }

    // الحصول على أنواع الإجازات الحالية
    let leaves = JSON.parse(localStorage.getItem('leaves') || '[]');

    // البحث عن نوع الإجازة وتحديثه
    const index = leaves.findIndex(leave => leave.id == id);
    if (index !== -1) {
        leaves[index] = {
            id: parseInt(id),
            name: name,
            maxDays: parseInt(maxDays),
            affectsAllowancePromotion: affectsAllowancePromotion,
            description: description
        };

        // حفظ البيانات المحدثة في التخزين المحلي
        localStorage.setItem('leaves', JSON.stringify(leaves));

        // تحديث عرض الجدول
        displayLeaves(leaves);

        // إغلاق النافذة
        closeModal(document.getElementById('editModal'));

        // عرض رسالة نجاح
        showNotification('تم تحديث نوع الإجازة بنجاح', 'success');
    }
}

// حذف نوع الإجازة
function deleteLeave(leaveId) {
    // الحصول على أنواع الإجازات الحالية
    let leaves = JSON.parse(localStorage.getItem('leaves') || '[]');

    // حذف نوع الإجازة
    leaves = leaves.filter(leave => leave.id != leaveId);

    // حفظ البيانات المحدثة في التخزين المحلي
    localStorage.setItem('leaves', JSON.stringify(leaves));

    // تحديث عرض الجدول
    displayLeaves(leaves);

    // عرض رسالة نجاح
    showNotification('تم حذف نوع الإجازة بنجاح', 'success');
}

// فتح نافذة منبثقة
function openModal(modal) {
    if (modal) {
        modal.style.display = 'block';
    }
}

// إغلاق نافذة منبثقة
function closeModal(modal) {
    if (modal) {
        modal.style.display = 'none';
    }
}

// عرض إشعار
function showNotification(message, type = 'info') {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-info-circle'}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close"><i class="fas fa-times"></i></button>
    `;

    // إضافة الإشعار إلى الصفحة
    document.body.appendChild(notification);

    // إضافة مستمع حدث لزر الإغلاق
    const closeButton = notification.querySelector('.notification-close');
    closeButton.addEventListener('click', function() {
        notification.remove();
    });

    // إزالة الإشعار تلقائيًا بعد 5 ثوانٍ
    setTimeout(() => {
        notification.classList.add('fade-out');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 5000);
}
