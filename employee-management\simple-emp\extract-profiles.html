<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>استخراج البيانات التعريفية</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="enhanced-styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <div class="card">
            <h2><i class="fas fa-database"></i> استخراج البيانات التعريفية</h2>
            <div class="result-section" id="resultSection"></div>
            <div class="button-group">
                <button onclick="extractData()" class="btn primary-btn">
                    <i class="fas fa-sync"></i> استخراج البيانات
                </button>
                <button onclick="showResults()" class="btn secondary-btn">
                    <i class="fas fa-eye"></i> عرض النتائج
                </button>
            </div>
        </div>
    </div>

    <script src="debug-storage.js"></script>
    <script>
    function extractData() {
        try {
            // استخراج البيانات
            extractProfileDataFromEmployees();
            
            // عرض نتيجة العملية
            const resultSection = document.getElementById('resultSection');
            resultSection.innerHTML = `
                <div class="success-message">
                    <i class="fas fa-check-circle"></i>
                    تم استخراج البيانات بنجاح
                </div>
            `;
        } catch (error) {
            const resultSection = document.getElementById('resultSection');
            resultSection.innerHTML = `
                <div class="error-message">
                    <i class="fas fa-exclamation-circle"></i>
                    حدث خطأ: ${error.message}
                </div>
            `;
        }
    }

    function showResults() {
        const resultSection = document.getElementById('resultSection');
        try {
            // جلب البيانات من التخزين المحلي
            const jobTitles = JSON.parse(localStorage.getItem('jobTitles') || '[]');
            const workLocations = JSON.parse(localStorage.getItem('workLocations') || '[]');
            const educationLevels = JSON.parse(localStorage.getItem('educationLevels') || '[]');
            const employees = JSON.parse(localStorage.getItem('employees') || '[]');
            
            // تحليل بيانات الرواتب وتواريخ التقاعد
            const salaryRanges = {};
            const retirementYears = {};
            
            employees.forEach(emp => {
                // تحليل الرواتب
                const salaryRange = Math.floor(emp.currentSalary / 100000) * 100000;
                salaryRanges[salaryRange] = (salaryRanges[salaryRange] || 0) + 1;
                
                // تحليل تواريخ التقاعد
                if (emp.retirementDate) {
                    const year = new Date(emp.retirementDate).getFullYear();
                    retirementYears[year] = (retirementYears[year] || 0) + 1;
                }
            });

            // عرض النتائج
            resultSection.innerHTML = `
                <div class="results-container">
                    <div class="result-group">
                        <h3><i class="fas fa-user-tie"></i> العناوين الوظيفية (${jobTitles.length})</h3>
                        <ul>
                            ${jobTitles.map(title => `
                                <li>
                                    ${title.name}
                                    <span class="badge ${title.category}">
                                        ${title.category === 'teaching' ? 'تدريسي' : 
                                          title.category === 'technical' ? 'فني' : 'إداري'}
                                    </span>
                                </li>
                            `).join('')}
                        </ul>
                    </div>
                    <div class="result-group">
                        <h3><i class="fas fa-building"></i> مواقع العمل (${workLocations.length})</h3>
                        <ul>
                            ${workLocations.map(location => `
                                <li>${location.name}</li>
                            `).join('')}
                        </ul>
                    </div>
                    <div class="result-group">
                        <h3><i class="fas fa-graduation-cap"></i> التحصيل الدراسي (${educationLevels.length})</h3>
                        <ul>
                            ${educationLevels.map(edu => `
                                <li>
                                    ${edu.name}
                                    <span class="badge education">
                                        ${edu.level}
                                    </span>
                                </li>
                            `).join('')}
                        </ul>
                    </div>
                    <div class="result-group">
                        <h3><i class="fas fa-money-bill-wave"></i> فئات الرواتب</h3>
                        <ul>
                            ${Object.entries(salaryRanges)
                                .sort(([a], [b]) => Number(a) - Number(b))
                                .map(([range, count]) => `
                                    <li>
                                        ${Number(range).toLocaleString()} - ${(Number(range) + 99999).toLocaleString()} د.ع
                                        <span class="badge salary">${count} موظف</span>
                                    </li>
                                `).join('')}
                        </ul>
                    </div>
                    <div class="result-group">
                        <h3><i class="fas fa-hourglass-end"></i> تواريخ التقاعد</h3>
                        <ul>
                            ${Object.entries(retirementYears)
                                .sort(([a], [b]) => Number(a) - Number(b))
                                .map(([year, count]) => `
                                    <li>
                                        ${year}
                                        <span class="badge retirement">${count} موظف</span>
                                    </li>
                                `).join('')}
                        </ul>
                    </div>
                </div>
            `;
        } catch (error) {
            resultSection.innerHTML = `
                <div class="error-message">
                    <i class="fas fa-exclamation-circle"></i>
                    حدث خطأ في عرض النتائج: ${error.message}
                </div>
            `;
        }
    }
    </script>

    <style>
    .container {
        max-width: 800px;
        margin: 20px auto;
        padding: 20px;
    }
    .card {
        background: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .button-group {
        display: flex;
        gap: 10px;
        margin-top: 20px;
    }
    .result-section {
        margin: 20px 0;
    }
    .success-message {
        background: #e8f5e9;
        color: #2e7d32;
        padding: 10px;
        border-radius: 4px;
        margin: 10px 0;
    }
    .error-message {
        background: #ffebee;
        color: #c62828;
        padding: 10px;
        border-radius: 4px;
        margin: 10px 0;
    }
    .results-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-top: 20px;
    }
    .result-group {
        background: #f5f5f5;
        padding: 15px;
        border-radius: 4px;
        min-width: 300px;
    }
    .result-group h3 {
        margin-top: 0;
        color: #333;
    }
    .result-group ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    .result-group li {
        padding: 5px 0;
        border-bottom: 1px solid #ddd;
    }
    .badge {
        display: inline-block;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.8em;
        margin-right: 8px;
    }
    .badge.teaching {
        background: #e3f2fd;
        color: #1565c0;
    }
    .badge.technical {
        background: #f3e5f5;
        color: #7b1fa2;
    }
    .badge.administrative {
        background: #e8f5e9;
        color: #2e7d32;
    }
    .badge.education {
        background: #fff3e0;
        color: #e65100;
    }
    .badge.salary {
        background: #e8eaf6;
        color: #283593;
    }
    .badge.retirement {
        background: #efebe9;
        color: #4e342e;
    }
    </style>
</body>
</html> 