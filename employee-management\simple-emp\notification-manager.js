/**
 * مدير الإشعارات المركزي
 * يتحكم في عرض وإخفاء الإشعارات في النظام
 */

// متغير للتحكم في عرض الإشعارات
let showNotificationsEnabled = false;

/**
 * عرض إشعار
 * @param {string} message - نص الرسالة
 * @param {string} type - نوع الإشعار (success, info, warning, error)
 * @param {number} duration - مدة ظهور الإشعار بالمللي ثانية
 */
function showNotification(message, type = 'info', duration = 3000) {
    // التحقق من تفعيل الإشعارات
    if (!showNotificationsEnabled) {
        // تسجيل الرسالة في وحدة التحكم فقط
        console.log(`إشعار (${type}): ${message}`);
        return;
    }

    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    
    // تحديد الأيقونة المناسبة حسب نوع الإشعار
    let icon = 'fa-info-circle';
    if (type === 'success') {
        icon = 'fa-check-circle';
    } else if (type === 'error') {
        icon = 'fa-exclamation-circle';
    } else if (type === 'warning') {
        icon = 'fa-exclamation-triangle';
    }

    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas ${icon}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close"><i class="fas fa-times"></i></button>
    `;

    // إضافة الإشعار إلى الصفحة
    document.body.appendChild(notification);

    // إضافة مستمع حدث لزر الإغلاق
    const closeButton = notification.querySelector('.notification-close');
    closeButton.addEventListener('click', function() {
        closeNotification(notification);
    });

    // إخفاء الإشعار تلقائياً بعد المدة المحددة
    setTimeout(function() {
        closeNotification(notification);
    }, duration);
}

/**
 * إغلاق الإشعار بتأثير متلاشي
 * @param {HTMLElement} notification - عنصر الإشعار
 */
function closeNotification(notification) {
    notification.classList.add('fade-out');
    
    // إزالة الإشعار من DOM بعد انتهاء التأثير
    setTimeout(function() {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 300);
}

/**
 * تفعيل أو تعطيل الإشعارات
 * @param {boolean} enabled - حالة تفعيل الإشعارات
 */
function setNotificationsEnabled(enabled) {
    showNotificationsEnabled = enabled;
    console.log(`تم ${enabled ? 'تفعيل' : 'تعطيل'} الإشعارات`);
}

// تصدير الوظائف
window.showNotification = showNotification;
window.setNotificationsEnabled = setNotificationsEnabled;

// تعطيل الإشعارات افتراضياً
setNotificationsEnabled(false);
