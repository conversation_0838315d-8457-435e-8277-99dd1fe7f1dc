/* استيراد ملف المتغيرات الموحدة */
@import url('variables.css');

/* إعدادات عامة */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
    transition: var(--transition);
}

a {
    text-decoration: none;
    color: inherit;
}

ul {
    list-style: none;
}

.btn {
    display: inline-block;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    background-color: var(--primary-color);
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    border: none;
}

.btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
}

.btn-secondary {
    background-color: var(--secondary-color);
}

.btn-secondary:hover {
    background-color: var(--secondary-light);
}

.btn-warning {
    background-color: var(--warning-color);
}

.btn-warning:hover {
    background-color: var(--warning-light);
}

/* الهيدر وشريط التنقل */
header {
    background: #1e3a8a;
    color: white;
    padding: 1.2rem 0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    position: sticky;
    top: 0;
    z-index: 100;
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    position: relative;
}

/* خط زخرفي أسفل الهيدر */
nav::after {
    content: '';
    position: absolute;
    bottom: -1.2rem;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg,
        var(--primary-color) 0%,
        var(--secondary-color) 33%,
        var(--warning-color) 66%,
        var(--primary-color) 100%);
}

.logo {
    font-size: 1.6rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    color: white;
    letter-spacing: 0.5px;
}

.logo-icon {
    margin-left: 0.8rem;
    font-size: 1.8rem;
    color: white;
}

.nav-links {
    display: flex;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50px;
    padding: 0.3rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    gap: 0.5rem;
}

.nav-links a {
    padding: 0.7rem 1rem;
    border-radius: 50px;
    transition: var(--transition);
    position: relative;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.95rem;
    color: rgba(255, 255, 255, 0.8);
}

.nav-links a i {
    font-size: 1.1rem;
}

.nav-links a:hover {
    color: white;
}

.nav-links a.active {
    background-color: white;
    color: var(--primary-color);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    font-weight: 600;
}

/* القوائم المنسدلة */
.dropdown {
    position: relative;
}

.dropdown-toggle {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.dropdown-toggle .fa-chevron-down {
    font-size: 0.8rem;
    margin-right: 0.3rem;
    transition: transform 0.3s ease;
}

.dropdown:hover .fa-chevron-down {
    transform: rotate(180deg);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    min-width: 220px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.3s ease;
    z-index: 100;
    padding: 0.8rem 0;
    margin-top: 0.5rem;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* تأكيد عمل القوائم المنسدلة على الأجهزة الكبيرة */
@media (min-width: 993px) {
    .dropdown:hover .dropdown-menu {
        display: block;
    }
}

.dropdown-menu a {
    color: var(--text-color) !important;
    padding: 0.7rem 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.8rem;
    border-radius: 0;
    transition: background-color 0.3s ease;
}

.dropdown-menu a:hover {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--primary-color) !important;
}

.dropdown-menu a i {
    color: var(--primary-color);
    font-size: 1rem;
}

/* تأثير السهم للقائمة المنسدلة */
.dropdown-menu::before {
    content: '';
    position: absolute;
    top: -8px;
    right: 20px;
    width: 16px;
    height: 16px;
    background-color: white;
    transform: rotate(45deg);
    border-radius: 2px;
}

.menu-toggle {
    display: none;
    font-size: 1.4rem;
    cursor: pointer;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

/* تصميم متجاوب للقائمة */
@media (max-width: 1200px) {
    .nav-links {
        gap: 0.2rem;
    }

    .nav-links a {
        padding: 0.7rem 0.8rem;
        font-size: 0.85rem;
    }
}

@media (max-width: 992px) {
    .logo {
        font-size: 1.4rem;
    }

    .nav-links {
        display: none;
        position: absolute;
        top: 100%;
        right: 0;
        left: 0;
        flex-direction: column;
        background-color: #1e3a8a;
        border-radius: 0 0 12px 12px;
        padding: 1rem;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        z-index: 100;
        margin-top: 1rem;
        gap: 0.5rem;
        max-height: 80vh;
        overflow-y: auto;
    }

    .nav-links.active {
        display: flex;
    }

    .nav-links a {
        width: 100%;
        border-radius: 8px;
    }

    .menu-toggle {
        display: flex;
    }

    .menu-toggle.active {
        background-color: rgba(255, 255, 255, 0.2);
    }

    /* تعديلات القوائم المنسدلة للأجهزة المحمولة */
    .dropdown {
        width: 100%;
    }

    .dropdown-menu {
        position: static;
        opacity: 0;
        visibility: hidden;
        height: 0;
        transform: none;
        box-shadow: none;
        background-color: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        margin: 0.5rem 0;
        padding: 0;
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .dropdown.mobile-active .dropdown-menu {
        opacity: 1;
        visibility: visible;
        height: auto;
        padding: 0.5rem 0;
    }

    .dropdown-menu::before {
        display: none;
    }

    .dropdown-menu a {
        color: rgba(255, 255, 255, 0.8) !important;
        padding: 0.7rem 1rem 0.7rem 2rem;
    }

    .dropdown-menu a:hover {
        background-color: rgba(255, 255, 255, 0.1);
        color: white !important;
    }

    .dropdown-menu a i {
        color: rgba(255, 255, 255, 0.9);
    }
}

/* القسم الرئيسي */
main {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
}



/* نموذج البحث */
.search-form {
    margin: 2rem auto 3rem;
    display: flex;
    gap: 1rem;
    max-width: 800px;
    position: relative;
    z-index: 10;
}

.search-input {
    flex: 1;
    padding: 1.2rem 1.5rem;
    border: none;
    border-radius: 50px;
    font-family: 'Cairo', sans-serif;
    font-size: 1.1rem;
    box-shadow: var(--box-shadow);
}

.search-input:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3), var(--box-shadow);
}

.search-form .btn {
    border-radius: 50px;
    padding: 0 2rem;
    font-size: 1.1rem;
}

/* قسم الإحصائيات */
.stats {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.stat-card {
    background-color: var(--card-color);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    text-align: center;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    border: none;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: var(--gradient-primary);
    transition: var(--transition);
}

.stat-card:nth-child(2)::before {
    background: linear-gradient(135deg, #3b82f6, #93c5fd); /* تدريسي */
}

.stat-card:nth-child(3)::before {
    background: linear-gradient(135deg, #6366f1, #a5b4fc); /* فني */
}

.stat-card:nth-child(4)::before {
    background: linear-gradient(135deg, #8b5cf6, #c4b5fd); /* إداري */
}

.stat-card:nth-child(5)::before {
    background: var(--gradient-secondary); /* كتب الشكر */
}

.stat-card:nth-child(6)::before {
    background: linear-gradient(135deg, var(--danger-color), #f87171); /* العقوبات */
}

.stat-card:nth-child(7)::before {
    background: var(--gradient-warning); /* الإجازات */
}

.stat-card:nth-child(8)::before {
    background: linear-gradient(135deg, #0ea5e9, #7dd3fc); /* مواقع العمل */
}

.stat-card:nth-child(9)::before {
    background: linear-gradient(135deg, #14b8a6, #5eead4); /* العناوين الوظيفية */
}

.stat-card:hover {
    transform: translateY(-10px);
}

.stat-card:hover::before {
    height: 100%;
    opacity: 0.05;
}

.stat-card h3 {
    font-size: 1.1rem;
    margin-bottom: 1rem;
    color: var(--text-light);
    font-weight: 600;
}

.stat-number {
    font-size: 3rem;
    font-weight: 800;
    color: var(--primary-color);
    margin-bottom: 1rem;
    line-height: 1;
}

.stat-card:nth-child(2) .stat-number {
    color: #3b82f6; /* تدريسي */
}

.stat-card:nth-child(3) .stat-number {
    color: #6366f1; /* فني */
}

.stat-card:nth-child(4) .stat-number {
    color: #8b5cf6; /* إداري */
}

.stat-card:nth-child(5) .stat-number {
    color: var(--secondary-color); /* كتب الشكر */
}

.stat-card:nth-child(6) .stat-number {
    color: var(--danger-color); /* العقوبات */
}

.stat-card:nth-child(7) .stat-number {
    color: var(--warning-color); /* الإجازات */
}

.stat-card:nth-child(8) .stat-number {
    color: #0ea5e9; /* مواقع العمل */
}

.stat-card:nth-child(9) .stat-number {
    color: #14b8a6; /* العناوين الوظيفية */
}

.stat-icon {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    display: inline-block;
    padding: 1.2rem;
    border-radius: 50%;
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--primary-color);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.stat-card:nth-child(2) .stat-icon {
    background-color: rgba(5, 150, 105, 0.1);
    color: var(--secondary-color);
}

.stat-card:nth-child(3) .stat-icon {
    background-color: rgba(220, 38, 38, 0.1);
    color: var(--danger-color);
}

.stat-card:nth-child(4) .stat-icon {
    background-color: rgba(217, 119, 6, 0.1);
    color: var(--warning-color);
}

.stat-card:nth-child(5) .stat-icon {
    background-color: rgba(99, 102, 241, 0.1);
    color: #6366f1;
}

.stat-card:nth-child(6) .stat-icon {
    background-color: rgba(139, 92, 246, 0.1);
    color: #8b5cf6;
}

/* قسم الميزات */
.features {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 2.5rem;
    margin-bottom: 5rem;
}

.feature-card {
    padding: 3rem 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    z-index: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    min-height: 350px;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.95);
    z-index: -1;
}

.feature-card:hover {
    transform: translateY(-15px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.feature-card h2 {
    font-size: 1.8rem;
    margin: 1.5rem 0;
    position: relative;
    padding-bottom: 1rem;
    font-weight: 700;
}

.feature-card h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    border-radius: 4px;
}

.feature-card p {
    color: var(--text-light);
    margin-bottom: 2rem;
    font-size: 1.1rem;
    line-height: 1.7;
    flex-grow: 1;
}

.feature-icon {
    font-size: 3.5rem;
    display: inline-block;
    padding: 1.5rem;
    border-radius: 50%;
    margin-bottom: 1rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.feature-card .btn {
    margin-top: auto;
    min-width: 160px;
    padding: 0.8rem 1.5rem;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1.1rem;
}

/* Blue Feature Card */
.blue {
    background-image: url('https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80');
    background-size: cover;
    background-position: center;
    border: none;
}

.blue::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.9) 0%, rgba(59, 130, 246, 0.8) 100%);
    z-index: -1;
    opacity: 0.1;
    transition: var(--transition);
}

.blue:hover::after {
    opacity: 0.15;
}

.blue h2 {
    color: var(--primary-color);
}

.blue h2::after {
    background-color: var(--primary-color);
}

.blue .feature-icon {
    color: white;
    background-color: var(--primary-color);
}

/* Green Feature Card */
.green {
    background-image: url('https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80');
    background-size: cover;
    background-position: center;
    border: none;
}

.green::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(5, 150, 105, 0.9) 0%, rgba(16, 185, 129, 0.8) 100%);
    z-index: -1;
    opacity: 0.1;
    transition: var(--transition);
}

.green:hover::after {
    opacity: 0.15;
}

.green h2 {
    color: var(--secondary-color);
}

.green h2::after {
    background-color: var(--secondary-color);
}

.green .feature-icon {
    color: white;
    background-color: var(--secondary-color);
}

/* Amber Feature Card */
.amber {
    background-image: url('https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80');
    background-size: cover;
    background-position: center;
    border: none;
}

.amber::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(217, 119, 6, 0.9) 0%, rgba(245, 158, 11, 0.8) 100%);
    z-index: -1;
    opacity: 0.1;
    transition: var(--transition);
}

.amber:hover::after {
    opacity: 0.15;
}

.amber h2 {
    color: var(--warning-color);
}

.amber h2::after {
    background-color: var(--warning-color);
}

.amber .feature-icon {
    color: white;
    background-color: var(--warning-color);
}

/* قسم لوحة التحكم */
.dashboard {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2.5rem;
    margin-bottom: 5rem;
}

.dashboard-section {
    background-color: var(--card-color);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    height: 100%;
    position: relative;
    overflow: hidden;
}

.dashboard-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: var(--gradient-primary);
}

.dashboard-section:nth-child(2)::before {
    background: var(--gradient-secondary);
}

.dashboard-section:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.dashboard-section h2 {
    font-size: 1.4rem;
    margin-bottom: 1.8rem;
    padding-bottom: 0.8rem;
    border-bottom: 1px solid #eee;
    position: relative;
    display: flex;
    align-items: center;
    font-weight: 700;
    color: var(--text-color);
}

.dashboard-section h2 i {
    margin-left: 0.8rem;
    font-size: 1.5rem;
    color: var(--primary-color);
}

.dashboard-section:nth-child(2) h2 i {
    color: var(--secondary-color);
}

.dashboard-section h2::before {
    content: '';
    width: 4px;
    height: 24px;
    background-color: var(--primary-color);
    margin-left: 0.8rem;
    border-radius: 2px;
}

.dashboard-section:nth-child(2) h2::before {
    background-color: var(--secondary-color);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 0.8rem;
    border-bottom: 1px solid #eee;
}

.section-header h2 {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.view-all {
    font-size: 1rem;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    font-weight: 600;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    background-color: rgba(59, 130, 246, 0.1);
    transition: var(--transition);
}

.dashboard-section:nth-child(2) .view-all {
    color: var(--secondary-color);
    background-color: rgba(5, 150, 105, 0.1);
}

.view-all i {
    margin-right: 0.5rem;
    transition: var(--transition);
}

.view-all:hover {
    background-color: rgba(59, 130, 246, 0.2);
}

.dashboard-section:nth-child(2) .view-all:hover {
    background-color: rgba(5, 150, 105, 0.2);
}

.view-all:hover i {
    transform: translateX(-5px);
}

/* التنبيهات */
.alert {
    padding: 1.5rem;
    border-radius: var(--border-radius);
    margin-bottom: 1.5rem;
    border-right: 5px solid;
    background-color: var(--card-color);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.alert:last-child {
    margin-bottom: 0;
}

.alert:hover {
    transform: translateX(8px);
}

.blue-alert {
    background-color: rgba(59, 130, 246, 0.03);
    border-color: var(--primary-color);
}

.green-alert {
    background-color: rgba(16, 185, 129, 0.03);
    border-color: var(--secondary-color);
}

.amber-alert {
    background-color: rgba(245, 158, 11, 0.03);
    border-color: var(--warning-color);
}

.alert::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0) 100%);
    z-index: -1;
}

.blue-alert::after {
    background: linear-gradient(90deg, rgba(59, 130, 246, 0.03) 0%, rgba(59, 130, 246, 0) 100%);
}

.green-alert::after {
    background: linear-gradient(90deg, rgba(16, 185, 129, 0.03) 0%, rgba(16, 185, 129, 0) 100%);
}

.amber-alert::after {
    background: linear-gradient(90deg, rgba(245, 158, 11, 0.03) 0%, rgba(245, 158, 11, 0) 100%);
}

.alert-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.8rem;
}

.alert h3 {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    font-weight: 600;
}

.alert h3 i {
    margin-left: 0.8rem;
    font-size: 1.3rem;
}

.blue-alert h3 i {
    color: var(--primary-color);
}

.green-alert h3 i {
    color: var(--secondary-color);
}

.amber-alert h3 i {
    color: var(--warning-color);
}

.alert-badge {
    font-size: 0.75rem;
    padding: 0.3rem 0.8rem;
    border-radius: 50px;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.blue-alert .alert-badge {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--primary-color);
}

.green-alert .alert-badge {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--secondary-color);
}

.amber-alert .alert-badge {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.alert p {
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 0.8rem;
}

.date {
    font-size: 0.85rem;
    color: var(--text-light);
    display: flex;
    align-items: center;
    margin-top: 0.8rem;
}

.date i {
    margin-left: 0.5rem;
    font-size: 0.9rem;
}

/* بطاقات الموظفين */
.employee-card {
    display: flex;
    justify-content: space-between;
    padding: 1.2rem;
    border-bottom: 1px solid #eee;
    transition: var(--transition);
    border-radius: var(--border-radius);
    margin-bottom: 0.5rem;
}

.employee-card:hover {
    background-color: rgba(59, 130, 246, 0.05);
    transform: translateX(5px);
}

.employee-card:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.employee-info {
    display: flex;
    align-items: center;
}

.employee-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: var(--gradient-primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-left: 1.2rem;
    font-size: 1.1rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.employee-name-title h3 {
    font-size: 1.1rem;
    margin-bottom: 0.4rem;
    font-weight: 600;
    color: var(--text-color);
}

.employee-name-title p, .employee-details p {
    font-size: 0.95rem;
    color: var(--text-light);
    display: flex;
    align-items: center;
}

.employee-name-title p::before, .employee-details p:first-child::before {
    content: '';
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-left: 0.5rem;
}

.employee-name-title p::before {
    background-color: var(--secondary-color);
}

.employee-details p:first-child::before {
    background-color: var(--primary-color);
}

.employee-details {
    text-align: left;
}

/* نموذج البحث */
.search-form {
    margin-bottom: 2rem;
    display: flex;
    gap: 1rem;
}

.search-input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 1px solid #e5e7eb;
    border-radius: var(--border-radius);
    font-family: 'Cairo', sans-serif;
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* الفوتر */
footer {
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
    color: white;
    text-align: center;
    padding: 4rem 1rem 2rem;
    margin-top: 5rem;
    position: relative;
    overflow: hidden;
}

footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 10px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--warning-color));
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
}

.footer-logo {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.footer-logo i {
    margin-left: 1rem;
    font-size: 2.2rem;
    background-color: rgba(255, 255, 255, 0.1);
    padding: 0.8rem;
    border-radius: 50%;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
}

.footer-links {
    display: flex;
    gap: 2.5rem;
    margin-bottom: 2.5rem;
    font-size: 1.1rem;
}

.footer-links a {
    position: relative;
    padding: 0.5rem 0;
    transition: var(--transition);
}

.footer-links a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: white;
    transition: var(--transition);
}

.footer-links a:hover {
    transform: translateY(-3px);
}

.footer-links a:hover::after {
    width: 100%;
}

.footer-social {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 2.5rem;
}

.social-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    font-size: 1.2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
}

.social-icon:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-5px);
}

footer p {
    font-size: 1rem;
    opacity: 0.8;
}

/* الوضع المظلم */
.dark-mode-toggle {
    position: fixed;
    bottom: 30px;
    left: 30px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--gradient-primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2);
    z-index: 100;
    transition: var(--transition);
    font-size: 1.5rem;
}

.dark-mode-toggle:hover {
    transform: rotate(45deg) scale(1.1);
}

/* تأثيرات إضافية للوضع المظلم */
.dark-mode {
    --background-color: #121212;
    --card-color: #1e1e1e;
    --text-color: #f3f4f6;
    --text-light: #9ca3af;
}

.dark-mode .hero::before {
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.9) 0%, rgba(5, 150, 105, 0.9) 100%);
}

.dark-mode .feature-card::before {
    background-color: rgba(0, 0, 0, 0.85);
}

.dark-mode .dashboard-section,
.dark-mode .alert,
.dark-mode .stat-card {
    background-color: var(--card-color);
    border-color: var(--card-color);
}

.dark-mode .dashboard-section h2,
.dark-mode .section-header {
    border-color: #333;
}

.dark-mode .employee-card {
    border-color: #333;
}

.dark-mode .employee-card:hover {
    background-color: rgba(59, 130, 246, 0.1);
}

.dark-mode .search-input {
    background-color: #2a2a2a;
    color: white;
    border: 1px solid #444;
}

.dark-mode .search-input::placeholder {
    color: #9ca3af;
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    .nav-links {
        display: none;
    }

    .menu-toggle {
        display: block;
    }



    .features, .dashboard {
        grid-template-columns: 1fr;
    }

    .stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .hero-buttons {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .stats {
        grid-template-columns: 1fr;
    }

    .hero h1 {
        font-size: 2rem;
    }
}
