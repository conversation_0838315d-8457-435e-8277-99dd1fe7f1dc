<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الربط - نظام إدارة العلاوات والترفيعات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }

        .header h1 {
            color: #333;
            margin-bottom: 10px;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: #f9f9f9;
        }

        .test-section h2 {
            color: #667eea;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            background: white;
            border-left: 4px solid #667eea;
        }

        .success {
            border-left-color: #10b981;
            background: #f0fdf4;
        }

        .error {
            border-left-color: #ef4444;
            background: #fef2f2;
        }

        .employee-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }

        .employee-card h4 {
            color: #333;
            margin-bottom: 10px;
        }

        .employee-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            font-size: 0.9rem;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
        }

        .info-label {
            font-weight: bold;
            color: #666;
        }

        .info-value {
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-cogs"></i> اختبار ربط النظام</h1>
            <p>اختبار الربط بين نموذج الموظف ونظام الشكر</p>
        </div>

        <!-- اختبار إضافة موظف تجريبي -->
        <div class="test-section">
            <h2><i class="fas fa-user-plus"></i> إضافة موظف تجريبي</h2>
            <button class="btn" onclick="addTestEmployee()">إضافة موظف تجريبي</button>
            <div id="addEmployeeResult" class="result" style="display: none;"></div>
        </div>

        <!-- عرض الموظفين الحاليين -->
        <div class="test-section">
            <h2><i class="fas fa-users"></i> الموظفين الحاليين</h2>
            <button class="btn" onclick="showEmployees()">عرض جميع الموظفين</button>
            <div id="employeesResult" class="result" style="display: none;"></div>
        </div>

        <!-- اختبار نظام الشكر -->
        <div class="test-section">
            <h2><i class="fas fa-medal"></i> اختبار نظام الشكر</h2>
            <button class="btn" onclick="testGenderFilter()">اختبار فلتر الجنس</button>
            <button class="btn" onclick="testJobTitleFilter()">اختبار فلتر العنوان الوظيفي</button>
            <button class="btn" onclick="testSpecializationFilter()">اختبار فلتر التخصص</button>
            <div id="thanksResult" class="result" style="display: none;"></div>
        </div>

        <!-- مسح البيانات -->
        <div class="test-section">
            <h2><i class="fas fa-trash"></i> مسح البيانات</h2>
            <button class="btn" onclick="clearAllData()" style="background: #ef4444;">مسح جميع البيانات</button>
            <div id="clearResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        // إضافة موظف تجريبي
        function addTestEmployee() {
            const testEmployee = {
                id: Date.now(),
                name: 'د. فاطمة أحمد محمد',
                gender: 'أنثى',
                jobDescription: 'teaching',
                jobTitle: 'أستاذ',
                currentJobTitle: 'أستاذ',
                workLocation: 'كلية الهندسة',
                education: 'دكتوراه',
                educationLevel: 'دكتوراه',
                specialization: 'هندسة حاسوب',
                birthDate: '1980-05-15',
                hireDate: '2010-09-01',
                currentDegree: 3,
                currentStage: 5,
                allowanceSeniority: 24,
                promotionSeniority: 36,
                currentSalary: '650000',
                lastPromotionDate: '2020-09-01',
                nextPromotionDate: '2025-09-01',
                currentDueDate: '2023-09-01',
                newDueDate: '2025-09-01',
                newJobTitle: 'أستاذ مساعد',
                retirementDate: '2043-05-15',
                status: 'active'
            };

            // الحصول على الموظفين الحاليين
            let employees = JSON.parse(localStorage.getItem('employees') || '[]');
            
            // إضافة الموظف التجريبي
            employees.push(testEmployee);
            
            // حفظ في التخزين المحلي
            localStorage.setItem('employees', JSON.stringify(employees));

            // عرض النتيجة
            const resultDiv = document.getElementById('addEmployeeResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result success';
            resultDiv.innerHTML = `
                <h4>✅ تم إضافة الموظف التجريبي بنجاح</h4>
                <p><strong>الاسم:</strong> ${testEmployee.name}</p>
                <p><strong>الجنس:</strong> ${testEmployee.gender}</p>
                <p><strong>العنوان الوظيفي:</strong> ${testEmployee.currentJobTitle}</p>
                <p><strong>التخصص:</strong> ${testEmployee.specialization}</p>
            `;
        }

        // عرض جميع الموظفين
        function showEmployees() {
            const employees = JSON.parse(localStorage.getItem('employees') || '[]');
            const resultDiv = document.getElementById('employeesResult');
            
            resultDiv.style.display = 'block';
            
            if (employees.length === 0) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '<h4>❌ لا توجد موظفين في النظام</h4>';
                return;
            }

            resultDiv.className = 'result success';
            let html = `<h4>📋 الموظفين في النظام (${employees.length})</h4>`;
            
            employees.forEach(emp => {
                html += `
                    <div class="employee-card">
                        <h4>${emp.name}</h4>
                        <div class="employee-info">
                            <div class="info-item">
                                <span class="info-label">الجنس:</span>
                                <span class="info-value">${emp.gender || 'غير محدد'}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">العنوان الوظيفي:</span>
                                <span class="info-value">${emp.currentJobTitle || emp.jobTitle || 'غير محدد'}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">التخصص:</span>
                                <span class="info-value">${emp.specialization || 'غير محدد'}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">التعليم:</span>
                                <span class="info-value">${emp.educationLevel || emp.education || 'غير محدد'}</span>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            resultDiv.innerHTML = html;
        }

        // اختبار فلتر الجنس
        function testGenderFilter() {
            const employees = JSON.parse(localStorage.getItem('employees') || '[]');
            const females = employees.filter(emp => emp.gender === 'أنثى');
            const males = employees.filter(emp => emp.gender === 'ذكر');
            
            const resultDiv = document.getElementById('thanksResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result success';
            resultDiv.innerHTML = `
                <h4>🔍 نتائج فلتر الجنس</h4>
                <p><strong>النساء:</strong> ${females.length} موظفة</p>
                <p><strong>الرجال:</strong> ${males.length} موظف</p>
                <p><strong>إجمالي:</strong> ${employees.length} موظف</p>
            `;
        }

        // اختبار فلتر العنوان الوظيفي
        function testJobTitleFilter() {
            const employees = JSON.parse(localStorage.getItem('employees') || '[]');
            const jobTitles = {};
            
            employees.forEach(emp => {
                const title = emp.currentJobTitle || emp.jobTitle || 'غير محدد';
                jobTitles[title] = (jobTitles[title] || 0) + 1;
            });
            
            const resultDiv = document.getElementById('thanksResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result success';
            
            let html = '<h4>🔍 نتائج فلتر العنوان الوظيفي</h4>';
            Object.entries(jobTitles).forEach(([title, count]) => {
                html += `<p><strong>${title}:</strong> ${count} موظف</p>`;
            });
            
            resultDiv.innerHTML = html;
        }

        // اختبار فلتر التخصص
        function testSpecializationFilter() {
            const employees = JSON.parse(localStorage.getItem('employees') || '[]');
            const specializations = {};
            
            employees.forEach(emp => {
                const spec = emp.specialization || 'غير محدد';
                specializations[spec] = (specializations[spec] || 0) + 1;
            });
            
            const resultDiv = document.getElementById('thanksResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result success';
            
            let html = '<h4>🔍 نتائج فلتر التخصص</h4>';
            Object.entries(specializations).forEach(([spec, count]) => {
                html += `<p><strong>${spec}:</strong> ${count} موظف</p>`;
            });
            
            resultDiv.innerHTML = html;
        }

        // مسح جميع البيانات
        function clearAllData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟')) {
                localStorage.removeItem('employees');
                localStorage.removeItem('thanks');
                
                const resultDiv = document.getElementById('clearResult');
                resultDiv.style.display = 'block';
                resultDiv.className = 'result success';
                resultDiv.innerHTML = '<h4>✅ تم مسح جميع البيانات بنجاح</h4>';
            }
        }

        // تحميل البيانات عند فتح الصفحة
        window.addEventListener('load', function() {
            showEmployees();
        });
    </script>
</body>
</html>
