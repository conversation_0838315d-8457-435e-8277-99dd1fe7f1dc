/**
 * أنماط شريط التنقل العصري
 */

:root {
    --primary-color: #1e88e5;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --info-color: #17a2b8;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --body-bg: #f5f7fa;
    --card-bg: #ffffff;
    --text-color: #212529;
    --border-color: #e9ecef;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --transition-speed: 0.3s;
    --border-radius: 10px;
    --font-family: 'Cairo', sans-serif;
    --navbar-height: 70px;
    --navbar-bg: #ffffff;
    --navbar-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
    --navbar-text: #212529;
    --navbar-active: #1e88e5;
    --navbar-hover-bg: rgba(30, 136, 229, 0.1);
}

/* الوضع الداكن */
body.dark-mode {
    --navbar-bg: #1a1a2e;
    --navbar-shadow: 0 2px 15px rgba(0, 0, 0, 0.3);
    --navbar-text: #ffffff;
    --navbar-hover-bg: rgba(255, 255, 255, 0.1);
}

/* شريط التنقل الرئيسي */
header {
    position: sticky;
    top: 0;
    z-index: 1000;
    width: 100%;
    background-color: var(--navbar-bg);
    box-shadow: var(--navbar-shadow);
    transition: background-color var(--transition-speed), box-shadow var(--transition-speed);
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: var(--navbar-height);
    padding: 0 20px;
    max-width: 1400px;
    margin: 0 auto;
}

/* الشعار */
.logo {
    display: flex;
    align-items: center;
    font-weight: 700;
    font-size: 18px;
    color: var(--navbar-text);
    transition: color var(--transition-speed);
}

.logo-icon {
    margin-left: 10px;
    font-size: 24px;
    color: var(--primary-color);
    transition: color var(--transition-speed), transform var(--transition-speed);
    background-color: rgba(30, 136, 229, 0.1);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.logo:hover .logo-icon {
    transform: rotate(15deg);
    background-color: rgba(30, 136, 229, 0.2);
}

/* روابط التنقل */
.nav-links {
    display: flex;
    align-items: center;
    gap: 5px;
}

.nav-links a {
    color: var(--navbar-text);
    text-decoration: none;
    padding: 10px 15px;
    border-radius: var(--border-radius);
    font-weight: 500;
    font-size: 15px;
    transition: color var(--transition-speed), background-color var(--transition-speed);
    display: flex;
    align-items: center;
    position: relative;
}

.nav-links a i:not(.fa-chevron-down) {
    margin-left: 8px;
    font-size: 16px;
}

.nav-links a:hover {
    color: var(--navbar-active);
    background-color: var(--navbar-hover-bg);
}

.nav-links a.active {
    color: var(--navbar-active);
    font-weight: 600;
}

.nav-links a.active {
    background-color: rgba(30, 136, 229, 0.15);
}

.nav-links a.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 3px;
    background-color: var(--primary-color);
    border-radius: 3px;
}

/* القوائم المنسدلة */
.dropdown {
    position: relative;
}

.dropdown-toggle .fa-chevron-down {
    margin-right: 5px;
    font-size: 12px;
    transition: transform var(--transition-speed);
}

.dropdown:hover .fa-chevron-down {
    transform: rotate(180deg);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    min-width: 220px;
    background-color: var(--navbar-bg);
    border-radius: var(--border-radius);
    box-shadow: 0 5px 20px var(--shadow-color);
    padding: 10px 0;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: opacity var(--transition-speed), visibility var(--transition-speed), transform var(--transition-speed);
    z-index: 1001;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu a {
    padding: 10px 15px;
    display: flex;
    align-items: center;
    width: 100%;
    border-radius: 0;
}

.dropdown-menu a:hover {
    background-color: var(--navbar-hover-bg);
}

/* أزرار الإجراءات في الشريط العلوي */
.nav-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* زر التبديل للقائمة في الشاشات الصغيرة */
.menu-toggle {
    display: none;
    flex-direction: column;
    justify-content: space-between;
    width: 30px;
    height: 21px;
    cursor: pointer;
    z-index: 1002;
}

.menu-toggle span {
    display: block;
    height: 3px;
    width: 100%;
    background-color: var(--navbar-text);
    border-radius: 3px;
    transition: all var(--transition-speed);
}

/* تحسينات للجوال */
@media (max-width: 992px) {
    .menu-toggle {
        display: flex;
    }

    .nav-links {
        position: fixed;
        top: var(--navbar-height);
        right: -100%;
        width: 80%;
        max-width: 300px;
        height: calc(100vh - var(--navbar-height));
        background-color: var(--navbar-bg);
        box-shadow: -5px 0 15px var(--shadow-color);
        flex-direction: column;
        align-items: flex-start;
        padding: 20px;
        gap: 10px;
        transition: right var(--transition-speed);
        overflow-y: auto;
        z-index: 1000;
    }

    .nav-actions .search-btn,
    .nav-actions .profile-btn {
        display: flex;
    }

    .nav-actions .search-btn.hidden,
    .nav-actions .profile-btn.hidden {
        display: none;
    }

    .nav-links.active {
        right: 0;
    }

    .nav-links a, .dropdown-toggle {
        width: 100%;
    }

    .dropdown-menu {
        position: static;
        box-shadow: none;
        opacity: 1;
        visibility: visible;
        transform: none;
        display: none;
        width: 100%;
        padding: 0 0 0 20px;
    }

    .dropdown.mobile-active .dropdown-menu {
        display: block;
    }

    .dropdown-toggle .fa-chevron-down {
        margin-right: auto;
    }

    .nav-links a.active::after {
        width: 5px;
        height: 100%;
        left: 0;
        top: 0;
        transform: none;
    }

    .menu-toggle.active span:nth-child(1) {
        transform: translateY(9px) rotate(45deg);
    }

    .menu-toggle.active span:nth-child(2) {
        opacity: 0;
    }

    .menu-toggle.active span:nth-child(3) {
        transform: translateY(-9px) rotate(-45deg);
    }
}

/* تحسينات إضافية */
.nav-links .notification-badge {
    position: absolute;
    top: 5px;
    left: 5px;
    background-color: var(--danger-color);
    color: white;
    font-size: 10px;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    animation: pulse 1.5s infinite;
}

/* تأثيرات إضافية */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.nav-links a:active {
    animation: pulse 0.3s ease;
}

/* تحسين شكل القوائم المنسدلة */
.dropdown-menu::before {
    content: '';
    position: absolute;
    top: -5px;
    right: 20px;
    width: 10px;
    height: 10px;
    background-color: var(--navbar-bg);
    transform: rotate(45deg);
}

/* تحسين زر البحث */
.search-btn {
    background-color: transparent;
    border: none;
    color: var(--navbar-text);
    font-size: 16px;
    cursor: pointer;
    padding: 10px;
    border-radius: 50%;
    transition: background-color var(--transition-speed);
}

.search-btn:hover {
    background-color: var(--navbar-hover-bg);
}

/* تحسين زر الإشعارات */
.notifications-btn {
    position: relative;
    background-color: transparent;
    border: none;
    color: var(--navbar-text);
    font-size: 16px;
    cursor: pointer;
    padding: 10px;
    border-radius: 50%;
    transition: background-color var(--transition-speed);
}

.notifications-btn:hover {
    background-color: var(--navbar-hover-bg);
}

/* تحسين زر الملف الشخصي */
.profile-btn {
    display: flex;
    align-items: center;
    background-color: transparent;
    border: none;
    color: var(--navbar-text);
    font-size: 14px;
    cursor: pointer;
    padding: 5px 10px;
    border-radius: var(--border-radius);
    transition: background-color var(--transition-speed);
}

.profile-btn:hover {
    background-color: var(--navbar-hover-bg);
}

.profile-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-left: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.profile-name {
    font-weight: 500;
}
