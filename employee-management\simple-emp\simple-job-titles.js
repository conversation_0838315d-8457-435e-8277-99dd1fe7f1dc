/**
 * ملف بسيط للتعامل مع العناوين الوظيفية
 */

// عرض إشعار - تم نقل هذه الوظيفة إلى ملف simple-messages.js

// العناوين الوظيفية الافتراضية - قائمة فارغة
const DEFAULT_JOB_TITLES = {
    teaching: [],
    technical: [],
    administrative: []
};

// تحميل العناوين الوظيفية من التخزين المحلي
function loadJobTitles() {
    console.log('بدء تحميل العناوين الوظيفية...');

    try {
        // محاولة تحميل العناوين الوظيفية من التخزين المحلي
        const storedJobTitles = localStorage.getItem('jobTitles');
        console.log('البيانات المخزنة في التخزين المحلي:', storedJobTitles ? 'موجودة' : 'غير موجودة');

        if (storedJobTitles) {
            // تحويل البيانات المخزنة إلى كائن JavaScript
            const allJobTitles = JSON.parse(storedJobTitles);
            console.log('عدد العناوين الوظيفية المحملة:', allJobTitles.length);

            // تنظيم العناوين الوظيفية حسب الفئة
            const organizedJobTitles = {
                teaching: [],
                technical: [],
                administrative: []
            };

            // تصنيف العناوين الوظيفية حسب الفئة
            allJobTitles.forEach(title => {
                if (organizedJobTitles[title.category]) {
                    organizedJobTitles[title.category].push({
                        id: title.id,
                        name: title.name,
                        newJobTitle: title.newJobTitle || '' // إضافة العنوان الوظيفي الجديد
                    });
                    console.log(`تمت إضافة العنوان الوظيفي "${title.name}" إلى فئة "${title.category}"`);
                } else {
                    console.warn(`فئة غير معروفة: ${title.category} للعنوان: ${title.name}`);
                }
            });

            // طباعة عدد العناوين الوظيفية في كل فئة
            console.log('عدد العناوين الوظيفية في فئة "تدريسي":', organizedJobTitles.teaching.length);
            console.log('عدد العناوين الوظيفية في فئة "فني":', organizedJobTitles.technical.length);
            console.log('عدد العناوين الوظيفية في فئة "اداري":', organizedJobTitles.administrative.length);

            // التحقق من وجود عناوين وظيفية في كل فئة
            const hasAnyCategory = organizedJobTitles.teaching.length > 0 ||
                                  organizedJobTitles.technical.length > 0 ||
                                  organizedJobTitles.administrative.length > 0;

            if (hasAnyCategory) {
                console.log('تم العثور على عناوين وظيفية في بعض الفئات على الأقل');
                return organizedJobTitles;
            } else {
                console.warn('لا توجد عناوين وظيفية في أي فئة، سيتم استخدام البيانات الافتراضية');
            }
        } else {
            console.log('لم يتم العثور على بيانات في التخزين المحلي، سيتم استخدام البيانات الافتراضية');
        }
    } catch (error) {
        console.error('خطأ في تحميل العناوين الوظيفية:', error);
    }

    // إذا لم يتم العثور على بيانات صالحة، استخدم البيانات الافتراضية
    console.log('استخدام البيانات الافتراضية للعناوين الوظيفية');

    // إنشاء بيانات افتراضية
    const defaultJobTitlesArray = [
        { id: 1, name: 'مدرس', category: 'teaching', newJobTitle: 'مدرس أول' },
        { id: 2, name: 'أستاذ مساعد', category: 'teaching', newJobTitle: 'أستاذ' },
        { id: 3, name: 'أستاذ', category: 'teaching', newJobTitle: 'أستاذ' },
        { id: 4, name: 'فني', category: 'technical', newJobTitle: 'فني أول' },
        { id: 5, name: 'مهندس', category: 'technical', newJobTitle: 'مهندس أول' },
        { id: 6, name: 'موظف إداري', category: 'administrative', newJobTitle: 'رئيس قسم' },
        { id: 7, name: 'رئيس قسم', category: 'administrative', newJobTitle: 'مدير' }
    ];

    localStorage.setItem('jobTitles', JSON.stringify(defaultJobTitlesArray));
    console.log('تم حفظ البيانات الافتراضية في التخزين المحلي');

    // تنظيم العناوين الوظيفية الافتراضية حسب الفئة
    const organizedDefaultJobTitles = {
        teaching: defaultJobTitlesArray.filter(title => title.category === 'teaching').map(title => ({
            id: title.id,
            name: title.name,
            newJobTitle: title.newJobTitle
        })),
        technical: defaultJobTitlesArray.filter(title => title.category === 'technical').map(title => ({
            id: title.id,
            name: title.name,
            newJobTitle: title.newJobTitle
        })),
        administrative: defaultJobTitlesArray.filter(title => title.category === 'administrative').map(title => ({
            id: title.id,
            name: title.name,
            newJobTitle: title.newJobTitle
        }))
    };

    return organizedDefaultJobTitles;
}

// تحديث قائمة العناوين الوظيفية
function updateJobTitlesList(jobDescriptionValue) {
    console.log('تحديث قائمة العناوين الوظيفية للوصف الوظيفي:', jobDescriptionValue);

    // الحصول على عنصر القائمة المنسدلة للعناوين الوظيفية
    const jobTitleSelect = document.getElementById('jobTitle');

    if (!jobTitleSelect) {
        console.error('عنصر القائمة المنسدلة للعناوين الوظيفية غير موجود');
        return;
    }

    // الحصول على العناوين الوظيفية - إعادة تحميل من التخزين المحلي لضمان الحصول على أحدث البيانات
    localStorage.removeItem('jobTitlesCache'); // مسح الذاكرة المؤقتة إن وجدت
    const jobTitles = loadJobTitles();
    console.log('تم تحميل العناوين الوظيفية:', jobTitles);

    // الحصول على العناوين الوظيفية للفئة المحددة
    const categoryTitles = jobTitles[jobDescriptionValue] || [];
    console.log('العناوين الوظيفية للفئة المحددة:', categoryTitles);

    // حفظ القيمة المحددة حالياً
    const currentValue = jobTitleSelect.value;
    console.log('القيمة المحددة حالياً:', currentValue);

    // حذف جميع الخيارات الحالية ما عدا الخيار الأول
    while (jobTitleSelect.options.length > 1) {
        jobTitleSelect.remove(1);
    }

    // إضافة الخيارات الجديدة
    categoryTitles.forEach(title => {
        const option = document.createElement('option');
        option.value = title.id;
        option.textContent = title.name;
        jobTitleSelect.appendChild(option);
        console.log('تمت إضافة خيار:', title.name, '(', title.id, ')');
    });

    // إعادة تحديد القيمة السابقة إذا كانت موجودة
    if (currentValue) {
        const optionExists = Array.from(jobTitleSelect.options).some(option => option.value === currentValue);
        if (optionExists) {
            jobTitleSelect.value = currentValue;
            console.log('تم إعادة تحديد القيمة السابقة:', currentValue);
        } else {
            console.log('القيمة السابقة لم تعد موجودة في القائمة الجديدة');
        }
    }

    // تحديث Select2 إذا كان موجوداً
    if (typeof jQuery !== 'undefined' && typeof jQuery.fn.select2 !== 'undefined') {
        try {
            // تدمير مثيل Select2 الحالي إذا كان موجوداً
            try {
                if (jQuery('#jobTitle').data('select2')) {
                    jQuery('#jobTitle').select2('destroy');
                    console.log('تم تدمير Select2 للعنوان الوظيفي');
                }
            } catch (e) {
                console.log('لم يكن هناك مثيل Select2 للتدمير:', e);
            }

            // إعادة تهيئة Select2
            jQuery('#jobTitle').select2({
                language: "ar",
                dir: "rtl",
                width: '100%',
                placeholder: "اختر العنوان الوظيفي",
                allowClear: true,
                dropdownParent: jQuery('body')
            });

            console.log('تم تحديث Select2 بنجاح');

            // تحديث القيمة المحددة في Select2
            if (currentValue) {
                // التحقق من وجود القيمة في القائمة
                const selectElement = jQuery('#jobTitle')[0];
                let optionExists = false;

                for (let i = 0; i < selectElement.options.length; i++) {
                    if (selectElement.options[i].value == currentValue) {
                        optionExists = true;
                        break;
                    }
                }

                if (!optionExists && currentValue) {
                    console.warn(`القيمة ${currentValue} غير موجودة في القائمة، إضافتها...`);

                    // الحصول على النص المناسب للقيمة
                    let optionText = currentValue;

                    // محاولة الحصول على النص من مصادر البيانات المختلفة
                    if (window.appData && window.appData.jobTitles) {
                        // البحث في جميع الفئات
                        for (const category in window.appData.jobTitles) {
                            const title = window.appData.jobTitles[category].find(t => t.id == currentValue);
                            if (title) {
                                optionText = title.name;
                                break;
                            }
                        }
                    }

                    // إضافة الخيار إلى القائمة
                    const newOption = new Option(optionText, currentValue, true, true);
                    jQuery('#jobTitle').append(newOption);
                    console.log(`تمت إضافة الخيار ${optionText} (${currentValue}) إلى القائمة`);
                }

                // تحديث القيمة
                jQuery('#jobTitle').val(currentValue).trigger('change');
                console.log('تم تحديث قيمة Select2:', currentValue);
            }
        } catch (error) {
            console.error('خطأ في تحديث Select2:', error);
        }
    } else {
        console.log('مكتبة jQuery أو Select2 غير متوفرة');
    }

    // تم إزالة الإشعار هنا
}

// إضافة مستمع حدث لتغيير الوصف الوظيفي
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل المستند، بدء تهيئة مستمعات الأحداث...');

    const jobDescriptionSelect = document.getElementById('jobDescription');

    if (jobDescriptionSelect) {
        console.log('تم العثور على عنصر الوصف الوظيفي، إضافة مستمع حدث...');

        jobDescriptionSelect.addEventListener('change', function() {
            console.log('تم تغيير الوصف الوظيفي إلى:', this.value);
            updateJobTitlesList(this.value);
        });

        // تحديث قائمة العناوين الوظيفية عند تحميل الصفحة
        if (jobDescriptionSelect.value) {
            console.log('الوصف الوظيفي محدد مسبقاً، تحديث قائمة العناوين الوظيفية...');
            updateJobTitlesList(jobDescriptionSelect.value);
        }
    } else {
        console.log('لم يتم العثور على عنصر الوصف الوظيفي');
    }

    // إضافة مستمع للرسائل من النوافذ الأخرى
    window.addEventListener('message', function(event) {
        console.log('تم استلام رسالة:', event.data);

        // التحقق من نوع الرسالة
        if (event.data && event.data.type === 'jobTitlesChanged') {
            console.log('تم تغيير العناوين الوظيفية في نافذة أخرى، جاري التحديث...');
            console.log('تفاصيل التغيير:', event.data);

            // مسح ذاكرة التخزين المؤقت للعناوين الوظيفية
            localStorage.removeItem('jobTitlesCache');

            // تحديث قائمة العناوين الوظيفية إذا كان هناك وصف وظيفي محدد
            if (jobDescriptionSelect && jobDescriptionSelect.value) {
                // التحقق مما إذا كان التغيير يؤثر على الفئة المحددة حالياً
                let shouldUpdate = true;

                if (event.data.action && event.data.data) {
                    if (event.data.action === 'edit') {
                        // في حالة التعديل، نتحقق مما إذا كان العنوان الوظيفي القديم أو الجديد ينتمي إلى الفئة المحددة
                        shouldUpdate = (event.data.oldData && event.data.oldData.category === jobDescriptionSelect.value) ||
                                      (event.data.newData && event.data.newData.category === jobDescriptionSelect.value);
                    } else if (event.data.action === 'delete' || event.data.action === 'add') {
                        // في حالة الحذف أو الإضافة، نتحقق مما إذا كان العنوان الوظيفي ينتمي إلى الفئة المحددة
                        shouldUpdate = event.data.data.category === jobDescriptionSelect.value;
                    }
                }

                if (shouldUpdate) {
                    console.log('التغيير يؤثر على الفئة المحددة حالياً، تحديث القائمة...');
                    updateJobTitlesList(jobDescriptionSelect.value);

                    // تم إزالة الإشعار هنا
                } else {
                    console.log('التغيير لا يؤثر على الفئة المحددة حالياً، تجاهل التحديث');
                }
            } else {
                console.log('لا يوجد وصف وظيفي محدد حالياً');
            }
        }
    });

    // إضافة مستمع لتغييرات التخزين المحلي
    window.addEventListener('storage', function(event) {
        console.log('تم اكتشاف تغيير في التخزين المحلي:', event);

        // التحقق من أن التغيير حدث في العناوين الوظيفية
        if (event.key === 'jobTitles' || event.key === 'jobTitlesUpdated') {
            console.log('تم تحديث العناوين الوظيفية في التخزين المحلي، جاري التحديث...');

            // مسح ذاكرة التخزين المؤقت للعناوين الوظيفية
            localStorage.removeItem('jobTitlesCache');

            // تحديث قائمة العناوين الوظيفية إذا كان هناك وصف وظيفي محدد
            if (jobDescriptionSelect && jobDescriptionSelect.value) {
                console.log('تحديث قائمة العناوين الوظيفية للوصف الوظيفي:', jobDescriptionSelect.value);
                updateJobTitlesList(jobDescriptionSelect.value);

                // تم إزالة الإشعار هنا
            } else {
                console.log('لا يوجد وصف وظيفي محدد حالياً');
            }
        }
    });

    console.log('تم تهيئة جميع مستمعات الأحداث بنجاح');
});
