'use client';

import { useState } from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON>ead<PERSON>, 
  CardBody, 
  CardFooter,
  Button, 
  Input,
  Select,
  SelectItem,
  Divider,
  Textarea
} from '@nextui-org/react';
import { FaSave, FaTimes } from 'react-icons/fa';
import Link from 'next/link';

// بيانات تجريبية للقوائم المنسدلة
const workLocations = [
  { id: '1', name: 'المقر الرئيسي' },
  { id: '2', name: 'الفرع الأول' },
  { id: '3', name: 'الفرع الثاني' },
  { id: '4', name: 'الفرع الثالث' },
];

const jobTitles = [
  { id: '1', name: 'مدير' },
  { id: '2', name: 'مهندس برمجيات' },
  { id: '3', name: 'محاسب' },
  { id: '4', name: 'مسؤول موارد بشرية' },
  { id: '5', name: 'مدير مبيعات' },
];

const educations = [
  { id: '1', name: 'دكتوراه' },
  { id: '2', name: 'ماجستير' },
  { id: '3', name: 'بكالوريوس' },
  { id: '4', name: 'دبلوم' },
];

export default function NewEmployeePage() {
  const [formData, setFormData] = useState({
    fullName: '',
    employeeNumber: '',
    birthDate: '',
    hireDate: '',
    phoneNumber: '',
    email: '',
    address: '',
    salary: '',
    workLocationId: '',
    jobTitleId: '',
    educationId: '',
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // هنا سيتم إرسال البيانات إلى الخادم
    console.log('Form data submitted:', formData);
    // يمكن إضافة رسالة نجاح أو توجيه المستخدم إلى صفحة أخرى
  };

  return (
    <div>
      <Card className="shadow-md">
        <CardHeader className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">إضافة موظف جديد</h1>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardBody className="gap-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-4">
                <h2 className="text-xl font-semibold">البيانات الشخصية</h2>
                <Input
                  label="الاسم الكامل"
                  name="fullName"
                  value={formData.fullName}
                  onChange={handleChange}
                  isRequired
                />
                <Input
                  label="الرقم الوظيفي"
                  name="employeeNumber"
                  value={formData.employeeNumber}
                  onChange={handleChange}
                  isRequired
                />
                <Input
                  label="تاريخ الميلاد"
                  name="birthDate"
                  type="date"
                  value={formData.birthDate}
                  onChange={handleChange}
                  isRequired
                />
                <Input
                  label="تاريخ التعيين"
                  name="hireDate"
                  type="date"
                  value={formData.hireDate}
                  onChange={handleChange}
                  isRequired
                />
              </div>

              <div className="space-y-4">
                <h2 className="text-xl font-semibold">معلومات الاتصال</h2>
                <Input
                  label="رقم الهاتف"
                  name="phoneNumber"
                  value={formData.phoneNumber}
                  onChange={handleChange}
                />
                <Input
                  label="البريد الإلكتروني"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleChange}
                />
                <Textarea
                  label="العنوان"
                  name="address"
                  value={formData.address}
                  onChange={(e) => setFormData((prev) => ({ ...prev, address: e.target.value }))}
                />
              </div>
            </div>

            <Divider className="my-4" />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-4">
                <h2 className="text-xl font-semibold">البيانات الوظيفية</h2>
                <Input
                  label="الراتب"
                  name="salary"
                  type="number"
                  value={formData.salary}
                  onChange={handleChange}
                  isRequired
                />
                <Select
                  label="موقع العمل"
                  name="workLocationId"
                  value={formData.workLocationId}
                  onChange={handleChange}
                  isRequired
                >
                  {workLocations.map((location) => (
                    <SelectItem key={location.id} value={location.id}>
                      {location.name}
                    </SelectItem>
                  ))}
                </Select>
                <Select
                  label="العنوان الوظيفي"
                  name="jobTitleId"
                  value={formData.jobTitleId}
                  onChange={handleChange}
                  isRequired
                >
                  {jobTitles.map((title) => (
                    <SelectItem key={title.id} value={title.id}>
                      {title.name}
                    </SelectItem>
                  ))}
                </Select>
                <Select
                  label="التحصيل الدراسي"
                  name="educationId"
                  value={formData.educationId}
                  onChange={handleChange}
                  isRequired
                >
                  {educations.map((education) => (
                    <SelectItem key={education.id} value={education.id}>
                      {education.name}
                    </SelectItem>
                  ))}
                </Select>
              </div>
            </div>
          </CardBody>
          <CardFooter className="flex justify-end gap-2">
            <Link href="/employees">
              <Button color="danger" variant="light" startContent={<FaTimes />}>
                إلغاء
              </Button>
            </Link>
            <Button color="primary" type="submit" startContent={<FaSave />}>
              حفظ
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}
