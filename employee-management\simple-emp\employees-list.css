/* أنماط خاصة بصفحة عرض الموظفين */

/* حاوية الموظفين */
.employees-container {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 1rem;
}

/* عنوان الصفحة */
.page-title {
    font-size: 1.8rem;
    margin-bottom: 2rem;
    color: var(--primary-color);
    text-align: center;
    font-weight: 700;
    position: relative;
    padding-bottom: 1rem;
}

.page-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: 2px;
}

/* قسم الفلاتر */
.filters-section {
    background-color: var(--card-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
    margin-bottom: 2rem;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
}

/* حاوية البحث */
.search-container {
    flex: 1;
    min-width: 250px;
    position: relative;
}

.search-input {
    width: 100%;
    padding: 0.8rem 1rem;
    padding-left: 2.5rem;
    border: 1px solid #e5e7eb;
    border-radius: 50px;
    font-family: 'Cairo', sans-serif;
    font-size: 1rem;
    transition: var(--transition);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-btn {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    font-size: 1rem;
}

/* الفلاتر */
.filters {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    flex: 2;
    min-width: 300px;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-group label {
    font-weight: 600;
    color: var(--text-color);
    white-space: nowrap;
}

.filter-select {
    padding: 0.6rem 1rem;
    border: 1px solid #e5e7eb;
    border-radius: var(--border-radius);
    font-family: 'Cairo', sans-serif;
    font-size: 0.9rem;
    min-width: 120px;
    transition: var(--transition);
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* الإجراءات */
.actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.actions .btn {
    padding: 0.6rem 1rem;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    white-space: nowrap;
}

/* حاوية جدول الموظفين */
.employees-table-container {
    background-color: var(--card-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1rem;
    overflow-x: auto;
    margin-bottom: 1.5rem;
}

/* جدول الموظفين */
.employees-table {
    width: 100%;
    border-collapse: collapse;
    text-align: right;
}

.employees-table th,
.employees-table td {
    padding: 1rem;
    border-bottom: 1px solid #eee;
}

.employees-table th {
    background-color: rgba(59, 130, 246, 0.05);
    font-weight: 600;
    color: var(--text-color);
    white-space: nowrap;
}

.employees-table tbody tr {
    transition: var(--transition);
}

.employees-table tbody tr:hover {
    background-color: rgba(59, 130, 246, 0.03);
}

/* أزرار الإجراءات في الجدول */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.action-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.9rem;
}

.view-btn {
    background-color: rgba(5, 150, 105, 0.1);
    color: var(--secondary-color);
}

.view-btn:hover {
    background-color: var(--secondary-color);
    color: white;
}

.edit-btn {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--primary-color);
}

.edit-btn:hover {
    background-color: var(--primary-color);
    color: white;
}

.delete-btn {
    background-color: rgba(220, 38, 38, 0.1);
    color: var(--danger-color);
}

.delete-btn:hover {
    background-color: var(--danger-color);
    color: white;
}

/* ترقيم الصفحات */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-top: 1.5rem;
}

.pagination-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--primary-color);
    border: none;
    cursor: pointer;
    transition: var(--transition);
}

.pagination-btn:hover:not(:disabled) {
    background-color: var(--primary-color);
    color: white;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-info {
    font-size: 0.9rem;
    color: var(--text-light);
}

/* نافذة عرض تفاصيل الموظف */
.modal-lg {
    max-width: 800px;
}

.employee-header {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #eee;
}

.employee-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: rgba(59, 130, 246, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: 2.5rem;
}

.employee-basic-info h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
    color: var(--text-color);
}

.employee-basic-info p {
    margin: 0;
    color: var(--text-light);
}

.employee-info-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
}

.info-group {
    display: flex;
    flex-direction: column;
    gap: 0.3rem;
}

.info-group label {
    font-weight: 600;
    color: var(--text-light);
    font-size: 0.9rem;
}

.info-group span {
    color: var(--text-color);
    font-size: 1rem;
}

/* تصميم متجاوب */
@media (max-width: 992px) {
    .employee-info-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .filters-section {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-container {
        width: 100%;
    }
    
    .filters {
        flex-direction: column;
    }
    
    .actions {
        justify-content: center;
    }
    
    .employee-info-grid {
        grid-template-columns: 1fr;
    }
    
    .employee-header {
        flex-direction: column;
        text-align: center;
    }
}

/* تأثيرات الوضع المظلم */
.dark-mode .filters-section,
.dark-mode .employees-table-container {
    background-color: var(--card-color);
}

.dark-mode .employees-table th {
    background-color: rgba(59, 130, 246, 0.1);
}

.dark-mode .employees-table td {
    border-color: #333;
}

.dark-mode .search-input,
.dark-mode .filter-select {
    background-color: #2a2a2a;
    border-color: #444;
    color: white;
}

.dark-mode .search-input::placeholder,
.dark-mode .filter-select::placeholder {
    color: #9ca3af;
}

.dark-mode .employee-header {
    border-color: #333;
}

/* حالات الموظف */
.status-badge {
    display: inline-block;
    padding: 0.3rem 0.8rem;
    border-radius: 50px;
    font-size: 0.8rem;
    font-weight: 600;
    text-align: center;
}

.status-active {
    background-color: rgba(5, 150, 105, 0.1);
    color: var(--secondary-color);
}

.status-retired {
    background-color: rgba(107, 114, 128, 0.1);
    color: #6b7280;
}

.status-suspended {
    background-color: rgba(220, 38, 38, 0.1);
    color: var(--danger-color);
}

/* تأثيرات إضافية */
.highlight-row {
    background-color: rgba(59, 130, 246, 0.05);
}

.promotion-due {
    color: var(--danger-color);
    font-weight: 600;
}

.promotion-upcoming {
    color: var(--warning-color);
    font-weight: 600;
}
