/**
 * أنماط لتنظيم كتب الشكر بشكل أفضل
 */

/* تحسين جدول كتب الشكر */
.thanks-list table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    overflow: hidden;
}

.thanks-list th {
    background-color: #f8f9fa;
    color: #495057;
    font-weight: 600;
    text-align: right;
    padding: 12px 15px;
    border-bottom: 2px solid #e9ecef;
    position: sticky;
    top: 0;
    z-index: 10;
}

.thanks-list td {
    padding: 12px 15px;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
}

.thanks-list tr:last-child td {
    border-bottom: none;
}

.thanks-list tr:hover td {
    background-color: #f8f9fa;
}

/* تحسين عرض نوع كتاب الشكر */
.thanks-type {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
}

.thanks-ministerial {
    background-color: rgba(0, 123, 255, 0.1);
    color: #007bff;
}

.thanks-presidential {
    background-color: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.thanks-university {
    background-color: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

/* تحسين أزرار الإجراءات */
.action-buttons {
    display: flex;
    gap: 5px;
    justify-content: center;
}

.action-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
    background-color: #f8f9fa;
}

.view-btn {
    color: #17a2b8;
}

.view-btn:hover {
    background-color: rgba(23, 162, 184, 0.1);
}

.edit-btn {
    color: #ffc107;
}

.edit-btn:hover {
    background-color: rgba(255, 193, 7, 0.1);
}

.delete-btn {
    color: #dc3545;
}

.delete-btn:hover {
    background-color: rgba(220, 53, 69, 0.1);
}

/* تحسين نموذج إضافة كتاب شكر */
.thanks-form {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.thanks-form .form-group {
    margin-bottom: 15px;
}

.thanks-form label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #495057;
}

.thanks-form input,
.thanks-form select,
.thanks-form textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    transition: border-color 0.2s;
}

.thanks-form input:focus,
.thanks-form select:focus,
.thanks-form textarea:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.thanks-form .required {
    color: #dc3545;
}

.thanks-form small {
    display: block;
    margin-top: 5px;
    color: #6c757d;
}

/* تحسين تنظيم الصفحة */
.thanks-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 20px;
}

.thanks-form-section,
.thanks-list-section {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.thanks-form-section h2,
.thanks-list-section h2 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #495057;
    font-size: 18px;
    display: flex;
    align-items: center;
}

.thanks-form-section h2 i,
.thanks-list-section h2 i {
    margin-left: 10px;
    color: #007bff;
}

/* تحسين البحث والتصفية */
.list-actions {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.search-container {
    position: relative;
    flex-grow: 1;
}

.search-input {
    width: 100%;
    padding: 8px 35px 8px 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
}

.search-btn {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
}

.filter-select {
    padding: 8px 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    min-width: 150px;
}

/* تحسين عرض التنبيهات */
.thanks-alert {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 8px;
    background-color: #f8f9fa;
    border-right: 4px solid #007bff;
}

.thanks-alert.info {
    background-color: rgba(0, 123, 255, 0.1);
    border-right-color: #007bff;
}

.thanks-alert.warning {
    background-color: rgba(255, 193, 7, 0.1);
    border-right-color: #ffc107;
}

.thanks-alert h3 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 16px;
    display: flex;
    align-items: center;
}

.thanks-alert h3 i {
    margin-left: 10px;
}

.thanks-alert p {
    margin: 0;
}

/* تحسين عرض الصفحة على الأجهزة المحمولة */
@media (max-width: 992px) {
    .thanks-content {
        grid-template-columns: 1fr;
    }

    .list-actions {
        flex-direction: column;
    }

    .filter-select {
        width: 100%;
    }
}

/* تحسين عرض تفاصيل كتاب الشكر */
.thanks-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.info-group {
    margin-bottom: 15px;
}

.info-group label {
    display: block;
    font-weight: 600;
    margin-bottom: 5px;
    color: #495057;
}

.info-group span {
    display: block;
    padding: 8px 0;
}

/* تحسين عرض تأثير كتاب الشكر */
.thanks-effect {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    background-color: rgba(0, 123, 255, 0.1);
    color: #007bff;
}

.thanks-effect.none {
    background-color: rgba(108, 117, 125, 0.1);
    color: #6c757d;
}

.thanks-effect.university {
    background-color: rgba(23, 162, 184, 0.1);
    color: #17a2b8;
}

.thanks-effect.minister {
    background-color: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

.thanks-effect.pm {
    background-color: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

/* تحسين عرض تاريخ كتاب الشكر */
.thanks-date {
    display: flex;
    align-items: center;
}

.thanks-date i {
    margin-left: 5px;
    color: #6c757d;
}

/* تحسين عرض الموظف في كتاب الشكر */
.employee-name {
    display: flex;
    align-items: center;
}

.employee-avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #007bff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-left: 10px;
    font-size: 12px;
}

/* تحسين زر تحديث تواريخ جميع الموظفين */
.update-all-container {
    margin-top: 20px;
    text-align: center;
}

.update-all-btn {
    padding: 10px 20px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
    font-weight: 600;
}

.update-all-btn:hover {
    background-color: #0069d9;
}

.update-all-btn i {
    margin-left: 10px;
}

/* أنماط الإضافة السريعة */
.quick-add-section {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    border-right: 4px solid #28a745;
}

.section-header h3 {
    margin: 0 0 10px 0;
    color: #28a745;
    display: flex;
    align-items: center;
}

.section-header h3 i {
    margin-left: 10px;
}

.section-description {
    margin: 0 0 15px 0;
    color: #6c757d;
    font-size: 14px;
}

.quick-add-form {
    background-color: white;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.form-row {
    display: flex;
    gap: 15px;
    align-items: end;
}

.form-row .form-group {
    flex: 1;
    margin-bottom: 0;
}

.form-row .form-group:last-child {
    flex: 0 0 auto;
}

.btn-success {
    background-color: #28a745;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
    font-weight: 600;
}

.btn-success:hover {
    background-color: #218838;
}

.btn-success i {
    margin-left: 8px;
}

/* أنماط الإحصائيات */
.thanks-stats-section {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.stat-card {
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 15px;
    border-right: 3px solid #007bff;
    transition: transform 0.2s;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.stat-header h4 {
    margin: 0;
    font-size: 16px;
    color: #495057;
}

.total-thanks {
    background-color: #007bff;
    color: white;
    padding: 3px 8px;
    border-radius: 10px;
    font-size: 12px;
    font-weight: 600;
}

.stat-details {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 3px 0;
}

.stat-label {
    font-size: 14px;
    color: #6c757d;
}

.stat-value {
    font-weight: 600;
    color: #495057;
    background-color: #e9ecef;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
}

/* تحسين عرض النموذج المبسط */
.thanks-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* عرض متجاوب للأجهزة المحمولة */
@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: 10px;
    }

    .form-row .form-group:last-child {
        flex: 1;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }
}
