import Link from 'next/link';

export default function Home() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white py-16 mb-12 rounded-lg">
        <div className="text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            برنامج إدارة العلاوات والترفيعات
          </h1>
          <p className="text-xl mb-8 opacity-90">
            نظام متكامل وحديث لإدارة شؤون الموظفين والعلاوات والترفيعات
          </p>
          <Link 
            href="/employees/new"
            className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors inline-block"
          >
            ابدأ الآن
          </Link>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
        <div className="bg-white p-6 rounded-lg shadow-md border-r-4 border-blue-500">
          <div className="flex items-center">
            <div className="bg-blue-100 p-3 rounded-full">
              <svg className="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"/>
              </svg>
            </div>
            <div className="mr-4">
              <p className="text-2xl font-bold text-gray-900">150</p>
              <p className="text-gray-600">إجمالي الموظفين</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-md border-r-4 border-green-500">
          <div className="flex items-center">
            <div className="bg-green-100 p-3 rounded-full">
              <svg className="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd"/>
              </svg>
            </div>
            <div className="mr-4">
              <p className="text-2xl font-bold text-gray-900">25</p>
              <p className="text-gray-600">العلاوات هذا الشهر</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-md border-r-4 border-purple-500">
          <div className="flex items-center">
            <div className="bg-purple-100 p-3 rounded-full">
              <svg className="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"/>
              </svg>
            </div>
            <div className="mr-4">
              <p className="text-2xl font-bold text-gray-900">12</p>
              <p className="text-gray-600">الترفيعات هذا الشهر</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-md border-r-4 border-orange-500">
          <div className="flex items-center">
            <div className="bg-orange-100 p-3 rounded-full">
              <svg className="w-6 h-6 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd"/>
              </svg>
            </div>
            <div className="mr-4">
              <p className="text-2xl font-bold text-gray-900">5</p>
              <p className="text-gray-600">التنبيهات النشطة</p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Features */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <Link href="/employees" className="group">
          <div className="bg-white p-8 rounded-lg shadow-md hover:shadow-lg transition-shadow group-hover:scale-105 transform transition-transform">
            <div className="bg-blue-100 w-16 h-16 rounded-lg flex items-center justify-center mb-6">
              <svg className="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"/>
              </svg>
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-3">إدارة الموظفين</h3>
            <p className="text-gray-600 mb-4">إضافة وتعديل وحذف بيانات الموظفين مع إمكانية البحث والتصفية المتقدمة</p>
            <span className="text-blue-600 font-semibold group-hover:text-blue-800">
              عرض التفاصيل ←
            </span>
          </div>
        </Link>

        <Link href="/allowances" className="group">
          <div className="bg-white p-8 rounded-lg shadow-md hover:shadow-lg transition-shadow group-hover:scale-105 transform transition-transform">
            <div className="bg-green-100 w-16 h-16 rounded-lg flex items-center justify-center mb-6">
              <svg className="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd"/>
              </svg>
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-3">العلاوات والترفيعات</h3>
            <p className="text-gray-600 mb-4">متابعة وإدارة العلاوات والترفيعات للموظفين مع نظام تنبيهات ذكي</p>
            <span className="text-green-600 font-semibold group-hover:text-green-800">
              عرض التفاصيل ←
            </span>
          </div>
        </Link>

        <Link href="/reports" className="group">
          <div className="bg-white p-8 rounded-lg shadow-md hover:shadow-lg transition-shadow group-hover:scale-105 transform transition-transform">
            <div className="bg-purple-100 w-16 h-16 rounded-lg flex items-center justify-center mb-6">
              <svg className="w-8 h-8 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"/>
              </svg>
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-3">التقارير والإحصائيات</h3>
            <p className="text-gray-600 mb-4">عرض تقارير وإحصائيات تفصيلية عن الموظفين والعلاوات والترفيعات</p>
            <span className="text-purple-600 font-semibold group-hover:text-purple-800">
              عرض التفاصيل ←
            </span>
          </div>
        </Link>

        <Link href="/alerts" className="group">
          <div className="bg-white p-8 rounded-lg shadow-md hover:shadow-lg transition-shadow group-hover:scale-105 transform transition-transform">
            <div className="bg-orange-100 w-16 h-16 rounded-lg flex items-center justify-center mb-6">
              <svg className="w-8 h-8 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd"/>
              </svg>
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-3">التنبيهات والتذكيرات</h3>
            <p className="text-gray-600 mb-4">نظام تنبيهات ذكي لمتابعة مواعيد العلاوات والترفيعات والتقاعد</p>
            <span className="text-orange-600 font-semibold group-hover:text-orange-800">
              عرض التفاصيل ←
            </span>
          </div>
        </Link>

        <div className="bg-white p-8 rounded-lg shadow-md">
          <div className="bg-red-100 w-16 h-16 rounded-lg flex items-center justify-center mb-6">
            <svg className="w-8 h-8 text-red-600" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd"/>
            </svg>
          </div>
          <h3 className="text-xl font-bold text-gray-900 mb-3">إدارة الإجازات</h3>
          <p className="text-gray-600 mb-4">متابعة وإدارة إجازات الموظفين وأرصدة الإجازات المتاحة</p>
          <span className="text-red-600 font-semibold">
            قريباً...
          </span>
        </div>

        <div className="bg-white p-8 rounded-lg shadow-md">
          <div className="bg-indigo-100 w-16 h-16 rounded-lg flex items-center justify-center mb-6">
            <svg className="w-8 h-8 text-indigo-600" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8 0a1 1 0 011-1h4a1 1 0 011 1v6a1 1 0 01-1 1h-4a1 1 0 01-1-1V8z" clipRule="evenodd"/>
            </svg>
          </div>
          <h3 className="text-xl font-bold text-gray-900 mb-3">لوحة التحكم المتقدمة</h3>
          <p className="text-gray-600 mb-4">لوحة تحكم شاملة مع رسوم بيانية وإحصائيات تفاعلية</p>
          <span className="text-indigo-600 font-semibold">
            قريباً...
          </span>
        </div>
      </div>
    </div>
  );
}
