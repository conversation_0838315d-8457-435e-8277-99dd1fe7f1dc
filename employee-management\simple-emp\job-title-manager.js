/**
 * مدير العناوين الوظيفية
 * ملف يحتوي على جميع الوظائف المتعلقة بإدارة العناوين الوظيفية
 */

// كائن عام لإدارة العناوين الوظيفية
const JobTitleManager = {
    // العناوين الوظيفية المنظمة حسب الفئة
    jobTitles: {
        teaching: [],
        technical: [],
        administrative: []
    },

    // تحميل العناوين الوظيفية من التخزين المحلي
    loadJobTitles: function() {
        console.log('تحميل العناوين الوظيفية من التخزين المحلي...');

        // الحصول على العناوين الوظيفية من التخزين المحلي
        let allJobTitles = JSON.parse(localStorage.getItem('jobTitles') || '[]');
        console.log('العناوين الوظيفية المحملة من التخزين المحلي:', allJobTitles);

        // إذا لم تكن هناك عناوين وظيفية، استخدم البيانات الافتراضية
        if (allJobTitles.length === 0) {
            console.log('لا توجد عناوين وظيفية، استخدام البيانات الافتراضية');
            allJobTitles = this.getDefaultJobTitles();

            // حفظ البيانات الافتراضية في التخزين المحلي
            localStorage.setItem('jobTitles', JSON.stringify(allJobTitles));
            console.log('تم حفظ البيانات الافتراضية في التخزين المحلي');
        }

        // إعادة تعيين العناوين الوظيفية المنظمة
        this.jobTitles = {
            teaching: [],
            technical: [],
            administrative: []
        };

        // تنظيم العناوين الوظيفية حسب الفئة
        allJobTitles.forEach(title => {
            if (this.jobTitles[title.category]) {
                this.jobTitles[title.category].push({
                    id: title.id,
                    name: title.name
                });
                console.log(`تمت إضافة العنوان الوظيفي "${title.name}" إلى فئة "${title.category}"`);
            } else {
                console.warn(`فئة غير معروفة: ${title.category} للعنوان: ${title.name}`);
            }
        });

        // التحقق من وجود عناوين وظيفية في كل فئة
        if (this.jobTitles.teaching.length === 0) {
            console.warn('لا توجد عناوين وظيفية في فئة "تدريسي"');
        }

        if (this.jobTitles.technical.length === 0) {
            console.warn('لا توجد عناوين وظيفية في فئة "فني"');
        }

        if (this.jobTitles.administrative.length === 0) {
            console.warn('لا توجد عناوين وظيفية في فئة "إداري"');
        }

        console.log('تم تحميل العناوين الوظيفية:', this.jobTitles);

        return this.jobTitles;
    },

    // الحصول على العناوين الوظيفية الافتراضية
    getDefaultJobTitles: function() {
        return [
            { id: 1, name: 'مدرس مساعد', category: 'teaching', description: 'مدرس مساعد في الجامعة' },
            { id: 2, name: 'مدرس', category: 'teaching', description: 'مدرس في الجامعة' },
            { id: 3, name: 'أستاذ مساعد', category: 'teaching', description: 'أستاذ مساعد في الجامعة' },
            { id: 4, name: 'أستاذ', category: 'teaching', description: 'أستاذ في الجامعة' },
            { id: 5, name: 'فني مختبر', category: 'technical', description: 'فني مختبر في الجامعة' },
            { id: 6, name: 'مهندس', category: 'technical', description: 'مهندس في الجامعة' },
            { id: 7, name: 'مبرمج', category: 'technical', description: 'مبرمج في الجامعة' },
            { id: 8, name: 'موظف إداري', category: 'administrative', description: 'موظف إداري في الجامعة' },
            { id: 9, name: 'رئيس قسم', category: 'administrative', description: 'رئيس قسم في الجامعة' },
            { id: 10, name: 'مدير', category: 'administrative', description: 'مدير في الجامعة' }
        ];
    },

    // الحصول على العناوين الوظيفية حسب الفئة
    getJobTitlesByCategory: function(category) {
        if (!category || !this.jobTitles[category]) {
            return [];
        }

        return this.jobTitles[category];
    },

    // الحصول على اسم العنوان الوظيفي حسب المعرف
    getJobTitleNameById: function(id) {
        // الحصول على جميع العناوين الوظيفية
        const allJobTitles = JSON.parse(localStorage.getItem('jobTitles') || '[]');

        // البحث عن العنوان الوظيفي حسب المعرف
        const jobTitle = allJobTitles.find(title => title.id == id);

        return jobTitle ? jobTitle.name : '';
    },

    // تحديث قائمة العناوين الوظيفية في النموذج
    updateJobTitleSelect: function(selectElement, category) {
        if (!selectElement) {
            console.error('عنصر القائمة المنسدلة غير موجود');
            return;
        }

        console.log(`تحديث قائمة العناوين الوظيفية للفئة ${category}...`);

        // الحصول على العناوين الوظيفية حسب الفئة
        const jobTitles = this.getJobTitlesByCategory(category);
        console.log('العناوين الوظيفية المتاحة:', jobTitles);

        // حفظ القيمة المحددة حالياً
        const currentValue = selectElement.value;
        console.log('القيمة الحالية:', currentValue);

        // تدمير Select2 إذا كان موجوداً
        if (typeof jQuery !== 'undefined' && typeof jQuery.fn.select2 !== 'undefined') {
            try {
                if (jQuery(selectElement).data('select2')) {
                    jQuery(selectElement).select2('destroy');
                }
            } catch (error) {
                console.error('خطأ في تدمير Select2:', error);
            }
        }

        // حذف جميع الخيارات الحالية ما عدا الخيار الأول
        while (selectElement.options.length > 1) {
            selectElement.remove(1);
        }

        // إضافة الخيارات الجديدة
        jobTitles.forEach(title => {
            const option = document.createElement('option');
            option.value = title.id;
            option.textContent = title.name;
            selectElement.appendChild(option);
            console.log(`تمت إضافة خيار: ${title.name} (${title.id})`);
        });

        // إعادة تحديد القيمة السابقة إذا كانت موجودة
        if (currentValue) {
            // التحقق من وجود الخيار في القائمة
            const optionExists = Array.from(selectElement.options).some(option => option.value === currentValue);

            if (optionExists) {
                selectElement.value = currentValue;
                console.log('تم إعادة تحديد القيمة السابقة:', currentValue);
            }
        }

        // إعادة تهيئة Select2
        if (typeof jQuery !== 'undefined' && typeof jQuery.fn.select2 !== 'undefined') {
            try {
                jQuery(selectElement).select2({
                    language: "ar",
                    dir: "rtl",
                    width: '100%',
                    placeholder: "اختر العنوان الوظيفي",
                    allowClear: true
                });

                // تحديث القيمة المحددة
                if (currentValue) {
                    jQuery(selectElement).val(currentValue).trigger('change');
                }
            } catch (error) {
                console.error('خطأ في إعادة تهيئة Select2:', error);
            }
        }
    },

    // إعلام جميع النوافذ المفتوحة بتغيير العناوين الوظيفية
    notifyJobTitlesChanged: function() {
        console.log('إعلام جميع النوافذ المفتوحة بتغيير العناوين الوظيفية...');

        // تحديث وقت آخر تغيير في التخزين المحلي
        localStorage.setItem('jobTitlesLastUpdate', new Date().toISOString());

        // إرسال رسالة إلى النافذة الأم
        try {
            if (window.opener) {
                window.opener.postMessage({ type: 'jobTitlesChanged' }, '*');
            }
        } catch (error) {
            console.error('خطأ في إرسال رسالة إلى النافذة الأم:', error);
        }

        // إرسال رسالة إلى جميع النوافذ المفتوحة
        try {
            window.postMessage({ type: 'jobTitlesChanged' }, '*');
        } catch (error) {
            console.error('خطأ في إرسال رسالة إلى النوافذ المفتوحة:', error);
        }
    },

    // إضافة مستمع لتغييرات العناوين الوظيفية
    addJobTitlesChangeListener: function(callback) {
        console.log('إضافة مستمع لتغييرات العناوين الوظيفية...');

        // إضافة مستمع لرسائل النوافذ
        window.addEventListener('message', function(event) {
            // التحقق من نوع الرسالة
            if (event.data && event.data.type === 'jobTitlesChanged') {
                console.log('تم استلام إشعار بتغيير العناوين الوظيفية');

                // استدعاء الدالة المستمعة
                if (typeof callback === 'function') {
                    callback();
                }
            }
        });
    },

    // تهيئة مدير العناوين الوظيفية
    init: function() {
        console.log('تهيئة مدير العناوين الوظيفية...');

        // تحميل العناوين الوظيفية
        this.loadJobTitles();

        // إضافة مستمع لتغييرات العناوين الوظيفية
        this.addJobTitlesChangeListener(() => {
            console.log('تم تغيير العناوين الوظيفية، إعادة تحميل البيانات...');
            this.loadJobTitles();
        });

        console.log('تم تهيئة مدير العناوين الوظيفية بنجاح');
    }
};

// تصدير الكائن للاستخدام في ملفات أخرى
window.JobTitleManager = JobTitleManager;

// تهيئة مدير العناوين الوظيفية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    JobTitleManager.init();
});
