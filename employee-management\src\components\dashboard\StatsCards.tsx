'use client';

import { Card, CardBody } from '@nextui-org/react';
import { 
  FaUserTie, 
  FaAward, 
  FaExclamationTriangle, 
  FaCalendarAlt,
  FaUserGraduate,
  FaBuilding
} from 'react-icons/fa';

type StatCardProps = {
  title: string;
  value: number;
  icon: React.ReactNode;
  color: string;
};

const StatCard = ({ title, value, icon, color }: StatCardProps) => {
  return (
    <Card className="shadow-md">
      <CardBody className="flex flex-row items-center justify-between">
        <div>
          <p className="text-sm text-gray-500">{title}</p>
          <p className="text-2xl font-bold">{value}</p>
        </div>
        <div className={`p-3 rounded-full ${color}`}>
          {icon}
        </div>
      </CardBody>
    </Card>
  );
};

export default function StatsCards() {
  // بيانات تجريبية
  const stats = [
    {
      title: 'إجمالي الموظفين',
      value: 120,
      icon: <FaUserTie size={24} className="text-white" />,
      color: 'bg-blue-500',
    },
    {
      title: 'كتب الشكر',
      value: 45,
      icon: <FaAward size={24} className="text-white" />,
      color: 'bg-green-500',
    },
    {
      title: 'العقوبات',
      value: 12,
      icon: <FaExclamationTriangle size={24} className="text-white" />,
      color: 'bg-red-500',
    },
    {
      title: 'الإجازات الحالية',
      value: 8,
      icon: <FaCalendarAlt size={24} className="text-white" />,
      color: 'bg-purple-500',
    },
    {
      title: 'مواقع العمل',
      value: 4,
      icon: <FaBuilding size={24} className="text-white" />,
      color: 'bg-amber-500',
    },
    {
      title: 'العناوين الوظيفية',
      value: 15,
      icon: <FaUserGraduate size={24} className="text-white" />,
      color: 'bg-teal-500',
    },
  ];

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mt-6">
      {stats.map((stat, index) => (
        <StatCard
          key={index}
          title={stat.title}
          value={stat.value}
          icon={stat.icon}
          color={stat.color}
        />
      ))}
    </div>
  );
}
