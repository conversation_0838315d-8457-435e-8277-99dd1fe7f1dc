/* أنماط الإشعارات */

.notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-width: 300px;
    z-index: 1000;
    animation: slideIn 0.3s ease-out;
}

.notification.success {
    border-right: 4px solid var(--secondary-color);
}

.notification.info {
    border-right: 4px solid var(--primary-color);
}

.notification.error {
    border-right: 4px solid var(--danger-color);
}

.notification.warning {
    border-right: 4px solid var(--warning-color);
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 0.8rem;
}

.notification-content i {
    font-size: 1.2rem;
}

.notification.success i {
    color: var(--secondary-color);
}

.notification.info i {
    color: var(--primary-color);
}

.notification.error i {
    color: var(--danger-color);
}

.notification.warning i {
    color: var(--warning-color);
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    font-size: 0.9rem;
    padding: 0.3rem;
    transition: var(--transition);
}

.notification-close:hover {
    color: var(--text-color);
}

.notification.fade-out {
    opacity: 0;
    transform: translateX(30px);
    transition: opacity 0.3s, transform 0.3s;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* تأثيرات الوضع المظلم */
.dark-mode .notification {
    background-color: #2a2a2a;
    color: white;
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    .notification {
        min-width: 250px;
        max-width: 90%;
    }
}
