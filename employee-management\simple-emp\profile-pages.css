/* أنماط خاصة بصفحات الملفات التعريفية */

/* حاوية الملف التعريفي */
.profile-container {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 1rem;
}

/* عنوان الصفحة */
.profile-title {
    font-size: 1.8rem;
    margin-bottom: 2rem;
    color: var(--primary-color);
    text-align: center;
    font-weight: 700;
    position: relative;
    padding-bottom: 1rem;
}

.profile-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: 2px;
}

/* محتوى الملف التعريفي */
.profile-content {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 1.5rem;
}

/* قسم النموذج */
.profile-form-section {
    background-color: var(--card-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1rem;
    position: relative;
    overflow: hidden;
}

.profile-form-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: var(--gradient-primary);
}

.profile-form-section h2 {
    font-size: 1.3rem;
    margin-bottom: 1.5rem;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.profile-form-section h2 i {
    font-size: 1.4rem;
}

/* نموذج الملف التعريفي */
.profile-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.profile-form .form-group {
    margin-bottom: 1rem;
}

.profile-form .form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-color);
}

.required {
    color: var(--danger-color);
}

.profile-form .form-group input,
.profile-form .form-group select,
.profile-form .form-group textarea {
    width: 100%;
    padding: 0.8rem 1rem;
    border: 1px solid #e5e7eb;
    border-radius: var(--border-radius);
    font-family: 'Cairo', sans-serif;
    font-size: 1rem;
    transition: var(--transition);
}

.profile-form .form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.profile-form .form-group input:focus,
.profile-form .form-group select:focus,
.profile-form .form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* أزرار النموذج */
.form-actions {
    display: flex;
    justify-content: flex-start;
    gap: 1rem;
    margin-top: 1.5rem;
}

.form-actions .btn {
    padding: 0.7rem 1.5rem;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-actions .btn i {
    font-size: 1.1rem;
}

/* قسم القائمة */
.profile-list-section {
    background-color: var(--card-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1rem;
    position: relative;
    overflow: hidden;
}

.profile-list-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: var(--gradient-secondary);
}

/* رأس القائمة */
.list-header {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #eee;
}

@media (min-width: 768px) {
    .list-header {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
    }
}

.list-header h2 {
    font-size: 1.3rem;
    color: var(--secondary-color);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0;
}

.list-header h2 i {
    font-size: 1.4rem;
}

/* إجراءات القائمة */
.list-actions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    width: 100%;
}

@media (min-width: 768px) {
    .list-actions {
        flex-direction: row;
        gap: 1rem;
        align-items: center;
        width: auto;
    }
}

/* حاوية البحث */
.search-container {
    display: flex;
    align-items: center;
    position: relative;
    width: 100%;
}

.search-input {
    padding: 0.6rem 1rem;
    border: 1px solid #e5e7eb;
    border-radius: 50px;
    font-family: 'Cairo', sans-serif;
    font-size: 0.9rem;
    width: 100%;
    transition: var(--transition);
}

.search-input:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
}

@media (min-width: 768px) {
    .search-container {
        width: auto;
    }

    .search-input {
        width: 180px;
    }

    .search-input:focus {
        width: 220px;
    }
}

.search-btn {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    font-size: 0.9rem;
}

/* قائمة الملفات التعريفية */
.profile-list {
    overflow-x: auto;
    margin-bottom: 1rem;
}

.profile-list table {
    width: 100%;
    border-collapse: collapse;
    text-align: right;
}

.profile-list table th,
.profile-list table td {
    padding: 0.8rem 1rem;
    border-bottom: 1px solid #eee;
}

.profile-list table th {
    background-color: rgba(5, 150, 105, 0.05);
    font-weight: 600;
    color: var(--text-color);
}

.profile-list table tr:hover {
    background-color: rgba(5, 150, 105, 0.03);
}

/* أزرار الإجراءات في الجدول */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.action-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.9rem;
}

.edit-btn {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--primary-color);
}

.edit-btn:hover {
    background-color: var(--primary-color);
    color: white;
}

.delete-btn {
    background-color: rgba(220, 38, 38, 0.1);
    color: var(--danger-color);
}

.delete-btn:hover {
    background-color: var(--danger-color);
    color: white;
}

/* ترقيم الصفحات */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-top: 1.5rem;
}

.pagination-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(5, 150, 105, 0.1);
    color: var(--secondary-color);
    border: none;
    cursor: pointer;
    transition: var(--transition);
}

.pagination-btn:hover:not(:disabled) {
    background-color: var(--secondary-color);
    color: white;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-info {
    font-size: 0.9rem;
    color: var(--text-light);
}

/* النوافذ المنبثقة */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    overflow: auto;
    padding: 1rem;
}

.modal-content {
    background-color: var(--card-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    max-width: 450px;
    width: 95%;
    margin: 0 auto;
    animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.3rem;
    color: var(--primary-color);
}

.close-modal {
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-light);
    transition: var(--transition);
}

.close-modal:hover {
    color: var(--danger-color);
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

.warning-text {
    color: var(--danger-color);
    font-weight: 600;
}

/* زر الخطر */
.btn-danger {
    background-color: var(--danger-color);
}

.btn-danger:hover {
    background-color: #b91c1c;
}

/* تصميم متجاوب */
@media (max-width: 992px) {
    .profile-content {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .profile-container {
        margin: 1rem auto;
    }

    .profile-title {
        font-size: 1.5rem;
        margin-bottom: 1.5rem;
    }
}

@media (max-width: 576px) {
    .profile-container {
        margin: 0.5rem auto;
        padding: 0 0.5rem;
    }

    .profile-title {
        font-size: 1.3rem;
        margin-bottom: 1rem;
    }

    .profile-form-section,
    .profile-list-section {
        padding: 0.75rem;
    }
}



/* تأثيرات الوضع المظلم */
.dark-mode .profile-form-section,
.dark-mode .profile-list-section,
.dark-mode .modal-content {
    background-color: var(--card-color);
}

.dark-mode .list-header,
.dark-mode .modal-header,
.dark-mode .modal-footer {
    border-color: #333;
}

.dark-mode .profile-list table th {
    background-color: rgba(5, 150, 105, 0.1);
}

.dark-mode .profile-list table td {
    border-color: #333;
}

.dark-mode .profile-form .form-group input,
.dark-mode .profile-form .form-group select,
.dark-mode .profile-form .form-group textarea,
.dark-mode .search-input {
    background-color: #2a2a2a;
    border-color: #444;
    color: white;
}

.dark-mode .profile-form .form-group input::placeholder,
.dark-mode .profile-form .form-group select::placeholder,
.dark-mode .profile-form .form-group textarea::placeholder,
.dark-mode .search-input::placeholder {
    color: #9ca3af;
}
