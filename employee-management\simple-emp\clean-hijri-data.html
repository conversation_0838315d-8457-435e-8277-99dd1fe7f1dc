<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تنظيف البيانات الهجرية - نظام إدارة العلاوات والترفيعات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }

        .header h1 {
            color: #333;
            margin-bottom: 10px;
        }

        .warning {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
        }

        .warning h3 {
            color: #92400e;
            margin-bottom: 10px;
        }

        .warning p {
            color: #92400e;
        }

        .action-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: #f9f9f9;
        }

        .action-section h2 {
            color: #667eea;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
            font-size: 16px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn.danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        }

        .btn.success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }

        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            background: white;
            border-left: 4px solid #667eea;
        }

        .success {
            border-left-color: #10b981;
            background: #f0fdf4;
            color: #065f46;
        }

        .error {
            border-left-color: #ef4444;
            background: #fef2f2;
            color: #991b1b;
        }

        .info {
            border-left-color: #3b82f6;
            background: #eff6ff;
            color: #1e40af;
        }

        .progress {
            background: #e5e7eb;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-bar {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            height: 20px;
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }

        .data-item {
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            margin: 5px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .data-item.fixed {
            border-left: 4px solid #10b981;
            background: #f0fdf4;
        }

        .data-item.issue {
            border-left: 4px solid #ef4444;
            background: #fef2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-broom"></i> تنظيف البيانات الهجرية</h1>
            <p>إزالة وتصحيح أي تواريخ هجرية موجودة في النظام</p>
        </div>

        <div class="warning">
            <h3><i class="fas fa-exclamation-triangle"></i> تحذير مهم</h3>
            <p>هذه العملية ستقوم بفحص وتصحيح جميع التواريخ في النظام لضمان استخدام التقويم الميلادي فقط</p>
        </div>

        <!-- فحص البيانات -->
        <div class="action-section">
            <h2><i class="fas fa-search"></i> فحص البيانات</h2>
            <button class="btn" onclick="scanData()">فحص البيانات الموجودة</button>
            <div id="scanResult" class="result" style="display: none;"></div>
        </div>

        <!-- تنظيف البيانات -->
        <div class="action-section">
            <h2><i class="fas fa-tools"></i> تنظيف البيانات</h2>
            <button class="btn success" onclick="cleanData()">تنظيف البيانات</button>
            <button class="btn danger" onclick="resetAllData()">إعادة تعيين كاملة</button>
            <div id="cleanResult" class="result" style="display: none;"></div>
        </div>

        <!-- التحقق النهائي -->
        <div class="action-section">
            <h2><i class="fas fa-check-circle"></i> التحقق النهائي</h2>
            <button class="btn" onclick="finalVerification()">التحقق من النظافة</button>
            <div id="verifyResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        // فحص البيانات الموجودة
        function scanData() {
            const resultDiv = document.getElementById('scanResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            
            const employees = JSON.parse(localStorage.getItem('employees') || '[]');
            const thanks = JSON.parse(localStorage.getItem('thanks') || '[]');
            
            let issues = [];
            let totalItems = 0;
            
            // فحص بيانات الموظفين
            employees.forEach((emp, index) => {
                totalItems++;
                const dateFields = ['birthDate', 'hireDate', 'lastPromotionDate', 'nextPromotionDate', 'currentDueDate', 'newDueDate', 'retirementDate'];
                dateFields.forEach(field => {
                    if (emp[field]) {
                        // فحص إذا كان التاريخ يحتوي على كلمات هجرية أو تنسيق غير صحيح
                        const hijriWords = ['هـ', 'محرم', 'صفر', 'ربيع', 'جمادى', 'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'];
                        const hasHijri = hijriWords.some(word => emp[field].includes(word));
                        const isValidISO = /^\d{4}-\d{2}-\d{2}$/.test(emp[field]);
                        
                        if (hasHijri || !isValidISO) {
                            issues.push({
                                type: 'employee',
                                id: emp.id,
                                name: emp.name,
                                field: field,
                                value: emp[field],
                                issue: hasHijri ? 'تاريخ هجري' : 'تنسيق غير صحيح'
                            });
                        }
                    }
                });
            });
            
            // فحص بيانات الشكر
            thanks.forEach((thank, index) => {
                totalItems++;
                if (thank.date) {
                    const hijriWords = ['هـ', 'محرم', 'صفر', 'ربيع', 'جمادى', 'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'];
                    const hasHijri = hijriWords.some(word => thank.date.includes(word));
                    const isValidISO = /^\d{4}-\d{2}-\d{2}$/.test(thank.date);
                    
                    if (hasHijri || !isValidISO) {
                        const employee = employees.find(emp => emp.id == thank.employeeId);
                        issues.push({
                            type: 'thanks',
                            id: thank.id || index,
                            employeeName: employee ? employee.name : 'غير معروف',
                            field: 'date',
                            value: thank.date,
                            issue: hasHijri ? 'تاريخ هجري' : 'تنسيق غير صحيح'
                        });
                    }
                }
            });
            
            let html = `
                <h4>نتائج الفحص:</h4>
                <p><strong>إجمالي العناصر:</strong> ${totalItems}</p>
                <p><strong>المشاكل المكتشفة:</strong> ${issues.length}</p>
            `;
            
            if (issues.length > 0) {
                html += '<h5>تفاصيل المشاكل:</h5>';
                issues.forEach(issue => {
                    html += `
                        <div class="data-item issue">
                            <span>${issue.type === 'employee' ? 'موظف' : 'شكر'}: ${issue.name || issue.employeeName} - ${issue.field}</span>
                            <span>${issue.issue}: ${issue.value}</span>
                        </div>
                    `;
                });
                resultDiv.className = 'result error';
            } else {
                html += '<p style="color: #10b981;"><strong>✅ لا توجد مشاكل! جميع التواريخ صحيحة</strong></p>';
                resultDiv.className = 'result success';
            }
            
            resultDiv.innerHTML = html;
        }
        
        // تنظيف البيانات
        function cleanData() {
            const resultDiv = document.getElementById('cleanResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.innerHTML = '<p>جاري تنظيف البيانات...</p>';
            
            let employees = JSON.parse(localStorage.getItem('employees') || '[]');
            let thanks = JSON.parse(localStorage.getItem('thanks') || '[]');
            
            let fixedCount = 0;
            let removedCount = 0;
            
            // تنظيف بيانات الموظفين
            employees.forEach(emp => {
                const dateFields = ['birthDate', 'hireDate', 'lastPromotionDate', 'nextPromotionDate', 'currentDueDate', 'newDueDate', 'retirementDate'];
                dateFields.forEach(field => {
                    if (emp[field]) {
                        const hijriWords = ['هـ', 'محرم', 'صفر', 'ربيع', 'جمادى', 'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'];
                        const hasHijri = hijriWords.some(word => emp[field].includes(word));
                        const isValidISO = /^\d{4}-\d{2}-\d{2}$/.test(emp[field]);
                        
                        if (hasHijri || !isValidISO) {
                            // محاولة تحويل التاريخ
                            const convertedDate = convertToGregorian(emp[field]);
                            if (convertedDate) {
                                emp[field] = convertedDate;
                                fixedCount++;
                            } else {
                                // إذا فشل التحويل، احذف التاريخ
                                delete emp[field];
                                removedCount++;
                            }
                        }
                    }
                });
            });
            
            // تنظيف بيانات الشكر
            thanks.forEach(thank => {
                if (thank.date) {
                    const hijriWords = ['هـ', 'محرم', 'صفر', 'ربيع', 'جمادى', 'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'];
                    const hasHijri = hijriWords.some(word => thank.date.includes(word));
                    const isValidISO = /^\d{4}-\d{2}-\d{2}$/.test(thank.date);
                    
                    if (hasHijri || !isValidISO) {
                        const convertedDate = convertToGregorian(thank.date);
                        if (convertedDate) {
                            thank.date = convertedDate;
                            fixedCount++;
                        } else {
                            // استخدام التاريخ الحالي كبديل
                            thank.date = new Date().toISOString().split('T')[0];
                            fixedCount++;
                        }
                    }
                }
            });
            
            // حفظ البيانات المنظفة
            localStorage.setItem('employees', JSON.stringify(employees));
            localStorage.setItem('thanks', JSON.stringify(thanks));
            
            resultDiv.className = 'result success';
            resultDiv.innerHTML = `
                <h4>✅ تم تنظيف البيانات بنجاح!</h4>
                <p><strong>التواريخ المصححة:</strong> ${fixedCount}</p>
                <p><strong>التواريخ المحذوفة:</strong> ${removedCount}</p>
                <p><strong>جميع التواريخ الآن تستخدم التقويم الميلادي</strong></p>
            `;
        }
        
        // تحويل التاريخ إلى ميلادي
        function convertToGregorian(dateString) {
            // محاولة تحليل التاريخ كتاريخ عادي أولاً
            const date = new Date(dateString);
            if (!isNaN(date.getTime())) {
                return date.toISOString().split('T')[0];
            }
            
            // إذا فشل، استخدم التاريخ الحالي
            return new Date().toISOString().split('T')[0];
        }
        
        // إعادة تعيين كاملة
        function resetAllData() {
            if (confirm('هل أنت متأكد من حذف جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه!')) {
                localStorage.removeItem('employees');
                localStorage.removeItem('thanks');
                localStorage.removeItem('employeeFiles');
                
                const resultDiv = document.getElementById('cleanResult');
                resultDiv.style.display = 'block';
                resultDiv.className = 'result success';
                resultDiv.innerHTML = '<h4>✅ تم حذف جميع البيانات بنجاح!</h4>';
            }
        }
        
        // التحقق النهائي
        function finalVerification() {
            const resultDiv = document.getElementById('verifyResult');
            resultDiv.style.display = 'block';
            
            const employees = JSON.parse(localStorage.getItem('employees') || '[]');
            const thanks = JSON.parse(localStorage.getItem('thanks') || '[]');
            
            let allClean = true;
            let issues = [];
            
            // فحص الموظفين
            employees.forEach(emp => {
                const dateFields = ['birthDate', 'hireDate', 'lastPromotionDate', 'nextPromotionDate', 'currentDueDate', 'newDueDate', 'retirementDate'];
                dateFields.forEach(field => {
                    if (emp[field]) {
                        const hijriWords = ['هـ', 'محرم', 'صفر', 'ربيع', 'جمادى', 'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'];
                        const hasHijri = hijriWords.some(word => emp[field].includes(word));
                        if (hasHijri) {
                            allClean = false;
                            issues.push(`موظف ${emp.name} - ${field}`);
                        }
                    }
                });
            });
            
            // فحص الشكر
            thanks.forEach(thank => {
                if (thank.date) {
                    const hijriWords = ['هـ', 'محرم', 'صفر', 'ربيع', 'جمادى', 'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'];
                    const hasHijri = hijriWords.some(word => thank.date.includes(word));
                    if (hasHijri) {
                        allClean = false;
                        issues.push(`شكر رقم ${thank.id || 'غير معروف'}`);
                    }
                }
            });
            
            if (allClean) {
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <h4>🎉 النظام نظيف تماماً!</h4>
                    <p><strong>✅ لا توجد تواريخ هجرية في النظام</strong></p>
                    <p><strong>✅ جميع التواريخ تستخدم التقويم الميلادي</strong></p>
                    <p><strong>عدد الموظفين:</strong> ${employees.length}</p>
                    <p><strong>عدد كتب الشكر:</strong> ${thanks.length}</p>
                `;
            } else {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h4>❌ لا يزال هناك مشاكل!</h4>
                    <p><strong>العناصر التي تحتاج تنظيف:</strong></p>
                    <ul>${issues.map(issue => `<li>${issue}</li>`).join('')}</ul>
                `;
            }
        }
        
        // تشغيل فحص أولي عند تحميل الصفحة
        window.addEventListener('load', function() {
            setTimeout(scanData, 500);
        });
    </script>
</body>
</html>
