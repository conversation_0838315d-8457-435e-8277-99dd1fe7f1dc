/* أنماط النوافذ المنبثقة */

.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    overflow: auto;
    padding: 2rem;
}

.modal-content {
    background-color: var(--card-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    max-width: 500px;
    margin: 0 auto;
    animation: modalFadeIn 0.3s ease-out;
}

.modal-lg {
    max-width: 800px;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.3rem;
    color: var(--primary-color);
}

.close-modal {
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-light);
    transition: var(--transition);
}

.close-modal:hover {
    color: var(--danger-color);
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

.warning-text {
    color: var(--danger-color);
    font-weight: 600;
}

/* زر الخطر */
.btn-danger {
    background-color: var(--danger-color);
}

.btn-danger:hover {
    background-color: #b91c1c;
}

/* تأثيرات الوضع المظلم */
.dark-mode .modal-content {
    background-color: var(--card-color);
}

.dark-mode .modal-header,
.dark-mode .modal-footer {
    border-color: #333;
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    .modal-content {
        max-width: 90%;
    }
}
