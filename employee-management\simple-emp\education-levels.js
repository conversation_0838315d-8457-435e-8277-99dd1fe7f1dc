// انتظر حتى يتم تحميل المستند بالكامل
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة صفحة التحصيل الدراسي
    initEducationLevelsPage();
});

// تهيئة صفحة التحصيل الدراسي
function initEducationLevelsPage() {
    // تحميل التحصيل الدراسي
    loadEducationLevels();

    // تهيئة نموذج إضافة تحصيل دراسي
    initAddEducationForm();

    // تهيئة البحث
    initSearch();

    // تهيئة النوافذ المنبثقة
    initModals();
}

// تحميل التحصيل الدراسي
function loadEducationLevels() {
    // في الإصدار النهائي، سيتم استبدال هذا بطلب API لجلب البيانات من الخادم
    // هنا نستخدم بيانات تجريبية للعرض

    // التحقق من وجود بيانات في التخزين المحلي
    let educationLevels = localStorage.getItem('educationLevels');

    if (!educationLevels) {
        // إذا لم تكن هناك بيانات، استخدم بيانات افتراضية
        educationLevels = [
            { id: 1, name: 'دبلوم' },
            { id: 2, name: 'بكالوريوس' },
            { id: 3, name: 'ماجستير' },
            { id: 4, name: 'دكتوراه' }
        ];

        // حفظ البيانات في التخزين المحلي
        localStorage.setItem('educationLevels', JSON.stringify(educationLevels));
    } else {
        // تحويل البيانات من JSON إلى كائن JavaScript
        educationLevels = JSON.parse(educationLevels);
    }

    // عرض البيانات في الجدول
    displayEducationLevels(educationLevels);
}

// عرض التحصيل الدراسي في الجدول
function displayEducationLevels(educationLevels) {
    const tableBody = document.querySelector('#educationLevelsTable tbody');
    if (!tableBody) return;

    // مسح محتوى الجدول
    tableBody.innerHTML = '';

    // إذا لم تكن هناك تحصيلات دراسية، عرض رسالة
    if (educationLevels.length === 0) {
        const emptyRow = document.createElement('tr');
        emptyRow.innerHTML = `<td colspan="3" class="text-center">لا توجد تحصيلات دراسية. أضف تحصيل دراسي جديد.</td>`;
        tableBody.appendChild(emptyRow);
        return;
    }

    // إضافة الصفوف إلى الجدول
    educationLevels.forEach((education, index) => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${index + 1}</td>
            <td>${education.name}</td>
            <td>
                <div class="action-buttons">
                    <button class="action-btn edit-btn" data-id="${education.id}" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn delete-btn" data-id="${education.id}" data-name="${education.name}" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;
        tableBody.appendChild(row);
    });

    // إضافة مستمعي الأحداث لأزرار التعديل والحذف
    addActionButtonsEventListeners();
}

// إضافة مستمعي الأحداث لأزرار التعديل والحذف
function addActionButtonsEventListeners() {
    // أزرار التعديل
    const editButtons = document.querySelectorAll('.edit-btn');
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const educationId = this.getAttribute('data-id');
            openEditModal(educationId);
        });
    });

    // أزرار الحذف
    const deleteButtons = document.querySelectorAll('.delete-btn');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const educationId = this.getAttribute('data-id');
            const educationName = this.getAttribute('data-name');
            openDeleteModal(educationId, educationName);
        });
    });
}

// تهيئة نموذج إضافة تحصيل دراسي
function initAddEducationForm() {
    const addForm = document.getElementById('addEducationForm');
    if (!addForm) return;

    addForm.addEventListener('submit', function(e) {
        e.preventDefault();

        // الحصول على قيم الحقول
        const name = document.getElementById('educationName').value.trim();

        // التحقق من صحة البيانات
        if (!name) {
            alert('يرجى إدخال اسم التحصيل الدراسي');
            return;
        }

        // إضافة التحصيل الدراسي الجديد
        addEducationLevel(name);

        // إعادة تعيين النموذج
        this.reset();
    });
}

// إضافة تحصيل دراسي جديد
function addEducationLevel(name) {
    // الحصول على التحصيلات الدراسية الحالية
    let educationLevels = JSON.parse(localStorage.getItem('educationLevels') || '[]');

    // إنشاء معرف فريد جديد
    const newId = educationLevels.length > 0 ? Math.max(...educationLevels.map(edu => edu.id)) + 1 : 1;

    // إنشاء التحصيل الدراسي الجديد
    const newEducation = {
        id: newId,
        name: name
    };

    // إضافة التحصيل الدراسي الجديد إلى المصفوفة
    educationLevels.push(newEducation);

    // حفظ البيانات المحدثة في التخزين المحلي
    localStorage.setItem('educationLevels', JSON.stringify(educationLevels));

    // تحديث عرض الجدول
    displayEducationLevels(educationLevels);

    // عرض رسالة نجاح
    showNotification('تمت الإضافة', 'success');
}

// تهيئة البحث
function initSearch() {
    const searchInput = document.getElementById('searchEducation');
    if (!searchInput) return;

    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.trim().toLowerCase();

        // الحصول على التحصيلات الدراسية
        const educationLevels = JSON.parse(localStorage.getItem('educationLevels') || '[]');

        // تصفية التحصيلات الدراسية بناءً على مصطلح البحث
        const filteredEducations = educationLevels.filter(education => {
            return education.name.toLowerCase().includes(searchTerm);
        });

        // عرض النتائج المصفاة
        displayEducationLevels(filteredEducations);
    });
}

// تهيئة النوافذ المنبثقة
function initModals() {
    // الحصول على عناصر النوافذ المنبثقة
    const editModal = document.getElementById('editModal');
    const deleteModal = document.getElementById('deleteModal');
    const closeButtons = document.querySelectorAll('.close-modal');

    // أزرار الإغلاق
    closeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const modal = this.closest('.modal');
            closeModal(modal);
        });
    });

    // إغلاق النافذة عند النقر خارجها
    window.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal')) {
            closeModal(e.target);
        }
    });

    // زر إلغاء التعديل
    const cancelEditBtn = document.getElementById('cancelEditBtn');
    if (cancelEditBtn) {
        cancelEditBtn.addEventListener('click', function() {
            closeModal(editModal);
        });
    }

    // زر حفظ التعديل
    const saveEditBtn = document.getElementById('saveEditBtn');
    if (saveEditBtn) {
        saveEditBtn.addEventListener('click', function() {
            saveEditedEducation();
        });
    }

    // زر إلغاء الحذف
    const cancelDeleteBtn = document.getElementById('cancelDeleteBtn');
    if (cancelDeleteBtn) {
        cancelDeleteBtn.addEventListener('click', function() {
            closeModal(deleteModal);
        });
    }

    // زر تأكيد الحذف
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    if (confirmDeleteBtn) {
        confirmDeleteBtn.addEventListener('click', function() {
            const educationId = this.getAttribute('data-id');
            deleteEducation(educationId);
            closeModal(deleteModal);
        });
    }
}

// فتح نافذة التعديل
function openEditModal(educationId) {
    // الحصول على التحصيل الدراسي المراد تعديله
    const educationLevels = JSON.parse(localStorage.getItem('educationLevels') || '[]');
    const education = educationLevels.find(edu => edu.id == educationId);

    if (!education) return;

    // ملء حقول النموذج بالبيانات الحالية
    document.getElementById('editEducationId').value = education.id;
    document.getElementById('editEducationName').value = education.name;

    // فتح النافذة
    const editModal = document.getElementById('editModal');
    openModal(editModal);
}

// فتح نافذة تأكيد الحذف
function openDeleteModal(educationId, educationName) {
    // عرض اسم التحصيل الدراسي في رسالة التأكيد
    document.getElementById('deleteEducationName').textContent = educationName;

    // تعيين معرف التحصيل الدراسي لزر التأكيد
    document.getElementById('confirmDeleteBtn').setAttribute('data-id', educationId);

    // فتح النافذة
    const deleteModal = document.getElementById('deleteModal');
    openModal(deleteModal);
}

// حفظ التحصيل الدراسي المعدل
function saveEditedEducation() {
    // الحصول على قيم الحقول
    const id = document.getElementById('editEducationId').value;
    const name = document.getElementById('editEducationName').value.trim();

    // التحقق من صحة البيانات
    if (!name) {
        alert('يرجى إدخال اسم التحصيل الدراسي');
        return;
    }

    // الحصول على التحصيلات الدراسية الحالية
    let educationLevels = JSON.parse(localStorage.getItem('educationLevels') || '[]');

    // البحث عن التحصيل الدراسي وتحديثه
    const index = educationLevels.findIndex(edu => edu.id == id);
    if (index !== -1) {
        educationLevels[index] = {
            id: parseInt(id),
            name: name
        };

        // حفظ البيانات المحدثة في التخزين المحلي
        localStorage.setItem('educationLevels', JSON.stringify(educationLevels));

        // تحديث عرض الجدول
        displayEducationLevels(educationLevels);

        // إغلاق النافذة
        closeModal(document.getElementById('editModal'));

        // عرض رسالة نجاح
        showNotification('تم التعديل', 'success');
    }
}

// حذف التحصيل الدراسي
function deleteEducation(educationId) {
    // الحصول على التحصيلات الدراسية الحالية
    let educationLevels = JSON.parse(localStorage.getItem('educationLevels') || '[]');

    // حذف التحصيل الدراسي
    educationLevels = educationLevels.filter(edu => edu.id != educationId);

    // حفظ البيانات المحدثة في التخزين المحلي
    localStorage.setItem('educationLevels', JSON.stringify(educationLevels));

    // تحديث عرض الجدول
    displayEducationLevels(educationLevels);

    // عرض رسالة نجاح
    showNotification('تم الحذف', 'success');
}

// فتح نافذة منبثقة
function openModal(modal) {
    if (modal) {
        modal.style.display = 'block';
    }
}

// إغلاق نافذة منبثقة
function closeModal(modal) {
    if (modal) {
        modal.style.display = 'none';
    }
}

// عرض إشعار
function showNotification(message, type = 'info') {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-info-circle'}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close"><i class="fas fa-times"></i></button>
    `;

    // إضافة الإشعار إلى الصفحة
    document.body.appendChild(notification);

    // إضافة مستمع حدث لزر الإغلاق
    const closeButton = notification.querySelector('.notification-close');
    closeButton.addEventListener('click', function() {
        notification.remove();
    });

    // إزالة الإشعار تلقائيًا بعد 5 ثوانٍ
    setTimeout(() => {
        notification.classList.add('fade-out');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 5000);
}
