<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التقارير - نظام إدارة العلاوات والترفيعات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #667eea;
        }

        .header h1 {
            color: #667eea;
            margin-bottom: 10px;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: #f9f9f9;
        }

        .test-section h2 {
            color: #667eea;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
            font-size: 16px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            background: white;
            border-left: 4px solid #667eea;
        }

        .success {
            border-left-color: #10b981;
            background: #f0fdf4;
            color: #065f46;
        }

        .error {
            border-left-color: #ef4444;
            background: #fef2f2;
            color: #991b1b;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #ddd;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
        }

        .search-box {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-vial"></i> اختبار التقارير</h1>
            <p>اختبار وظائف التقارير والبحث</p>
        </div>

        <!-- اختبار إنشاء البيانات -->
        <div class="test-section">
            <h2><i class="fas fa-database"></i> اختبار البيانات</h2>
            <button class="btn" onclick="testCreateData()">إنشاء بيانات تجريبية</button>
            <button class="btn" onclick="testLoadData()">تحميل البيانات</button>
            <button class="btn" onclick="clearData()">مسح البيانات</button>
            <div id="dataResult" class="result" style="display: none;"></div>
        </div>

        <!-- اختبار البحث -->
        <div class="test-section">
            <h2><i class="fas fa-search"></i> اختبار البحث</h2>
            <input type="text" id="searchInput" class="search-box" placeholder="اكتب للبحث...">
            <div id="searchResult" class="result" style="display: none;"></div>
        </div>

        <!-- عرض البيانات -->
        <div class="test-section">
            <h2><i class="fas fa-table"></i> عرض البيانات</h2>
            <div id="dataDisplay"></div>
        </div>
    </div>

    <script>
        // إنشاء بيانات تجريبية
        function testCreateData() {
            const resultDiv = document.getElementById('dataResult');
            resultDiv.style.display = 'block';
            
            try {
                const employees = createSampleEmployeesForReports();
                localStorage.setItem('employees', JSON.stringify(employees));
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <h4>✅ تم إنشاء البيانات بنجاح!</h4>
                    <p><strong>عدد الموظفين:</strong> ${employees.length}</p>
                    <p><strong>أول موظف:</strong> ${employees[0].name}</p>
                `;
                
                displayData();
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<h4>❌ خطأ: ${error.message}</h4>`;
            }
        }

        // تحميل البيانات
        function testLoadData() {
            const resultDiv = document.getElementById('dataResult');
            resultDiv.style.display = 'block';
            
            try {
                const employees = JSON.parse(localStorage.getItem('employees') || '[]');
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <h4>✅ تم تحميل البيانات!</h4>
                    <p><strong>عدد الموظفين:</strong> ${employees.length}</p>
                `;
                
                displayData();
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<h4>❌ خطأ: ${error.message}</h4>`;
            }
        }

        // مسح البيانات
        function clearData() {
            localStorage.removeItem('employees');
            const resultDiv = document.getElementById('dataResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result success';
            resultDiv.innerHTML = '<h4>✅ تم مسح البيانات!</h4>';
            
            document.getElementById('dataDisplay').innerHTML = '';
        }

        // عرض البيانات
        function displayData() {
            const employees = JSON.parse(localStorage.getItem('employees') || '[]');
            const displayDiv = document.getElementById('dataDisplay');
            
            if (employees.length === 0) {
                displayDiv.innerHTML = '<p>لا توجد بيانات</p>';
                return;
            }
            
            let html = `
                <table class="table">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>الاسم</th>
                            <th>الوظيفة</th>
                            <th>موقع العمل</th>
                            <th>تاريخ العلاوة</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            employees.forEach((emp, index) => {
                html += `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${emp.name}</td>
                        <td>${emp.jobTitle}</td>
                        <td>${emp.workLocation}</td>
                        <td>${emp.nextAllowanceDate}</td>
                    </tr>
                `;
            });
            
            html += '</tbody></table>';
            displayDiv.innerHTML = html;
        }

        // اختبار البحث
        document.getElementById('searchInput').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const resultDiv = document.getElementById('searchResult');
            
            if (!searchTerm) {
                resultDiv.style.display = 'none';
                return;
            }
            
            const employees = JSON.parse(localStorage.getItem('employees') || '[]');
            const filtered = employees.filter(emp => 
                emp.name.toLowerCase().includes(searchTerm) ||
                emp.jobTitle.toLowerCase().includes(searchTerm) ||
                emp.workLocation.toLowerCase().includes(searchTerm)
            );
            
            resultDiv.style.display = 'block';
            resultDiv.className = 'result success';
            resultDiv.innerHTML = `
                <h4>نتائج البحث عن: "${searchTerm}"</h4>
                <p><strong>عدد النتائج:</strong> ${filtered.length}</p>
                ${filtered.map(emp => `<p>• ${emp.name} - ${emp.jobTitle}</p>`).join('')}
            `;
        });

        // إنشاء بيانات تجريبية للموظفين للتقارير
        function createSampleEmployeesForReports() {
            const today = new Date();
            const sampleEmployees = [];
            
            const names = [
                'أحمد محمد علي', 'فاطمة أحمد حسن', 'محمد خالد عبدالله', 'سارة علي محمود', 'عبدالله حسن أحمد',
                'مريم محمد خالد', 'خالد عبدالرحمن', 'نور الدين محمد', 'هدى أحمد علي', 'يوسف محمد حسن',
                'زينب خالد أحمد', 'عمر عبدالله محمد', 'ليلى حسن علي', 'حسام الدين أحمد', 'رنا محمد خالد'
            ];
            
            const jobTitles = [
                'مهندس برمجيات', 'محاسب', 'مدير مبيعات', 'مسؤول موارد بشرية', 'مهندس شبكات',
                'مصمم جرافيك', 'محلل نظم', 'مدير مشروع', 'مطور ويب', 'مدير تسويق',
                'محاسب مالي', 'مهندس مدني', 'طبيب', 'ممرض', 'صيدلي'
            ];
            
            const workLocations = [
                'المقر الرئيسي', 'الفرع الأول', 'الفرع الثاني', 'الفرع الثالث', 'المكتب الإقليمي'
            ];
            
            for (let i = 0; i < 15; i++) {
                const daysToAdd = Math.floor(Math.random() * 120) - 30;
                const nextAllowanceDate = new Date(today);
                nextAllowanceDate.setDate(today.getDate() + daysToAdd);
                
                const lastAllowanceDate = new Date(nextAllowanceDate);
                lastAllowanceDate.setFullYear(lastAllowanceDate.getFullYear() - 1);
                
                const employee = {
                    id: i + 1,
                    name: names[i],
                    jobTitle: jobTitles[i],
                    workLocation: workLocations[i % workLocations.length],
                    currentDegree: Math.floor(Math.random() * 10) + 1,
                    currentStage: Math.floor(Math.random() * 15) + 1,
                    currentSalary: (Math.floor(Math.random() * 500000) + 300000).toString(),
                    lastAllowanceDate: lastAllowanceDate.toISOString().split('T')[0],
                    nextAllowanceDate: nextAllowanceDate.toISOString().split('T')[0],
                    currentDueDate: nextAllowanceDate.toISOString().split('T')[0],
                    seniority: Math.floor(Math.random() * 20) + 1,
                    birthDate: '1980-01-01',
                    hireDate: '2010-01-01'
                };
                
                sampleEmployees.push(employee);
            }
            
            return sampleEmployees;
        }

        // تحميل البيانات عند بدء الصفحة
        window.addEventListener('load', function() {
            displayData();
        });
    </script>
</body>
</html>
