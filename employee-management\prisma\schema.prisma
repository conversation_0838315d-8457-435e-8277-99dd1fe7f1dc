// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

// نموذج المستخدم
model User {
  id        String   @id @default(uuid())
  name      String
  email     String   @unique
  password  String
  role      Role     @default(USER)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

enum Role {
  ADMIN
  USER
}

// نموذج موقع العمل
model WorkLocation {
  id          String      @id @default(uuid())
  name        String
  description String?
  employees   Employee[]
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
}

// نموذج العنوان الوظيفي
model JobTitle {
  id          String      @id @default(uuid())
  name        String
  description String?
  employees   Employee[]
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
}

// نموذج التحصيل الدراسي
model Education {
  id          String      @id @default(uuid())
  name        String
  description String?
  employees   Employee[]
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
}

// نموذج الموظف
model Employee {
  id                String          @id @default(uuid())
  fullName          String
  employeeNumber    String          @unique
  birthDate         DateTime
  hireDate          DateTime
  phoneNumber       String?
  address           String?
  email             String?
  salary            Float
  lastPromotionDate DateTime?
  lastAllowanceDate DateTime?
  retirementDate    DateTime?

  // العلاقات
  workLocationId    String?
  workLocation      WorkLocation?   @relation(fields: [workLocationId], references: [id])
  jobTitleId        String?
  jobTitle          JobTitle?       @relation(fields: [jobTitleId], references: [id])
  educationId       String?
  education         Education?      @relation(fields: [educationId], references: [id])

  // العلاقات مع الجداول الأخرى
  appreciations     Appreciation[]
  penalties         Penalty[]
  leaves            Leave[]
  absences          Absence[]

  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
}

// نموذج كتب الشكر
model Appreciation {
  id          String    @id @default(uuid())
  title       String
  description String
  date        DateTime
  employeeId  String
  employee    Employee  @relation(fields: [employeeId], references: [id])
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

// نموذج العقوبات
model Penalty {
  id          String    @id @default(uuid())
  title       String
  description String
  date        DateTime
  employeeId  String
  employee    Employee  @relation(fields: [employeeId], references: [id])
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

// نموذج الإجازات
model Leave {
  id          String      @id @default(uuid())
  type        LeaveType
  startDate   DateTime
  endDate     DateTime
  status      LeaveStatus @default(PENDING)
  description String?
  employeeId  String
  employee    Employee    @relation(fields: [employeeId], references: [id])
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
}

enum LeaveType {
  ANNUAL
  SICK
  EMERGENCY
  OTHER
}

enum LeaveStatus {
  PENDING
  APPROVED
  REJECTED
}

// نموذج الغيابات
model Absence {
  id          String    @id @default(uuid())
  date        DateTime
  reason      String?
  employeeId  String
  employee    Employee  @relation(fields: [employeeId], references: [id])
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

// نموذج التنبيهات
model Notification {
  id          String             @id @default(uuid())
  title       String
  message     String
  type        NotificationType
  isRead      Boolean            @default(false)
  createdAt   DateTime           @default(now())
  updatedAt   DateTime           @updatedAt
}

enum NotificationType {
  ALLOWANCE
  PROMOTION
  RETIREMENT
  OTHER
}
