/**
 * ملف لعرض تغييرات تواريخ الاستحقاق
 */

// انتظر حتى يتم تحميل المستند بالكامل
document.addEventListener('DOMContentLoaded', function() {
    // إضافة زر لعرض تغييرات تواريخ الاستحقاق
    addAllowanceChangesButton();
});

// إضافة زر لعرض تغييرات تواريخ الاستحقاق
function addAllowanceChangesButton() {
    // التحقق من وجود حاوية الإجراءات في صفحة التشكرات
    const actionsContainer = document.querySelector('.list-actions');
    if (actionsContainer) {
        // إنشاء زر عرض التغييرات
        const changesButton = document.createElement('button');
        changesButton.className = 'btn btn-info';
        changesButton.innerHTML = '<i class="fas fa-history"></i> عرض تغييرات تواريخ الاستحقاق';
        changesButton.addEventListener('click', showAllowanceChangesModal);

        // إضافة الزر إلى حاوية الإجراءات
        actionsContainer.appendChild(changesButton);
    }

    // إنشاء نافذة عرض التغييرات
    createAllowanceChangesModal();
}

// إنشاء نافذة عرض تغييرات تواريخ الاستحقاق
function createAllowanceChangesModal() {
    // التحقق من وجود النافذة مسبقاً
    if (document.getElementById('allowanceChangesModal')) return;

    // إنشاء عنصر النافذة
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.id = 'allowanceChangesModal';

    // إضافة محتوى النافذة
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h2>تغييرات تواريخ الاستحقاق</h2>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <div class="changes-list">
                    <table id="allowanceChangesTable">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>الموظف</th>
                                <th>التاريخ القديم</th>
                                <th>التاريخ الجديد</th>
                                <th>السبب</th>
                                <th>تاريخ التغيير</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- سيتم ملء هذا الجزء ديناميكياً -->
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button id="closeChangesBtn" class="btn btn-secondary"><i class="fas fa-times"></i> إغلاق</button>
            </div>
        </div>
    `;

    // إضافة النافذة إلى الصفحة
    document.body.appendChild(modal);

    // إضافة مستمعي الأحداث للنافذة
    const closeButton = modal.querySelector('.close-modal');
    const closeChangesBtn = modal.querySelector('#closeChangesBtn');

    closeButton.addEventListener('click', function() {
        closeModal(modal);
    });

    closeChangesBtn.addEventListener('click', function() {
        closeModal(modal);
    });

    // إغلاق النافذة عند النقر خارجها
    window.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeModal(modal);
        }
    });
}

// عرض نافذة تغييرات تواريخ الاستحقاق
function showAllowanceChangesModal() {
    // الحصول على تغييرات تواريخ الاستحقاق
    const allowanceChanges = JSON.parse(localStorage.getItem('allowanceChanges') || '[]');

    // الحصول على جدول التغييرات
    const tableBody = document.querySelector('#allowanceChangesTable tbody');
    if (!tableBody) return;

    // مسح محتوى الجدول
    tableBody.innerHTML = '';

    // إذا لم تكن هناك تغييرات، عرض رسالة
    if (allowanceChanges.length === 0) {
        const emptyRow = document.createElement('tr');
        emptyRow.innerHTML = `<td colspan="6" class="text-center">لا توجد تغييرات في تواريخ الاستحقاق.</td>`;
        tableBody.appendChild(emptyRow);
    } else {
        // إضافة الصفوف إلى الجدول
        allowanceChanges.forEach((change, index) => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${index + 1}</td>
                <td>${change.employeeName}</td>
                <td>${formatDate(change.oldDate)}</td>
                <td>${formatDate(change.newDate)}</td>
                <td>${change.reason}</td>
                <td>${formatDate(change.date)}</td>
            `;
            tableBody.appendChild(row);
        });
    }

    // فتح النافذة
    const modal = document.getElementById('allowanceChangesModal');
    if (modal) {
        openModal(modal);
    }
}

// تنسيق التاريخ (ميلادي فقط - إجباري)
function formatDate(dateString) {
    if (!dateString) return '-';

    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;

    // إجبار استخدام التقويم الميلادي مع تنسيق يدوي لضمان عدم ظهور التاريخ الهجري
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();

    const monthNames = [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];

    return `${day} ${monthNames[month - 1]} ${year}`;
}

// فتح نافذة منبثقة
function openModal(modal) {
    if (modal) {
        modal.style.display = 'block';
    }
}

// إغلاق نافذة منبثقة
function closeModal(modal) {
    if (modal) {
        modal.style.display = 'none';
    }
}
