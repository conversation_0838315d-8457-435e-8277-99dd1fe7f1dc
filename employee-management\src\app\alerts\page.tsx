'use client';

import { useState } from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>, 
  Ta<PERSON>, 
  Tab,
  Input,
  Chip
} from '@nextui-org/react';
import { FaS<PERSON>ch, <PERSON>a<PERSON>ell, FaCheck } from 'react-icons/fa';

// نموذج بيانات التنبيه
type Alert = {
  id: string;
  title: string;
  message: string;
  type: 'ALLOWANCE' | 'PROMOTION' | 'RETIREMENT' | 'OTHER';
  date: string;
  isRead: boolean;
};

export default function AlertsPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedType, setSelectedType] = useState('all');
  
  // بيانات تجريبية للتنبيهات
  const alerts: Alert[] = [
    {
      id: '1',
      title: 'علاوة مستحقة',
      message: 'الموظف أحمد محمد مستحق للعلاوة السنوية',
      type: 'AL<PERSON>OWAN<PERSON>',
      date: '2025-05-10',
      isRead: false,
    },
    {
      id: '2',
      title: 'ترفيع مستحق',
      message: 'الموظفة سارة أحمد مستحقة للترفيع الوظيفي',
      type: 'PROMOTION',
      date: '2025-05-15',
      isRead: false,
    },
    {
      id: '3',
      title: 'إحالة للتقاعد',
      message: 'الموظف محمود علي سيتم إحالته للتقاعد قريباً',
      type: 'RETIREMENT',
      date: '2025-06-01',
      isRead: true,
    },
    {
      id: '4',
      title: 'علاوة مستحقة',
      message: 'الموظف خالد عبدالله مستحق للعلاوة السنوية',
      type: 'ALLOWANCE',
      date: '2025-05-20',
      isRead: false,
    },
    {
      id: '5',
      title: 'ترفيع مستحق',
      message: 'الموظف عمر محمد مستحق للترفيع الوظيفي',
      type: 'PROMOTION',
      date: '2025-05-25',
      isRead: true,
    },
    {
      id: '6',
      title: 'إحالة للتقاعد',
      message: 'الموظفة ليلى أحمد سيتم إحالتها للتقاعد قريباً',
      type: 'RETIREMENT',
      date: '2025-06-15',
      isRead: false,
    },
    {
      id: '7',
      title: 'تنبيه عام',
      message: 'يرجى تحديث بيانات الموظفين قبل نهاية الشهر',
      type: 'OTHER',
      date: '2025-05-28',
      isRead: false,
    },
  ];

  // تصفية التنبيهات بناءً على البحث والنوع المحدد
  const filteredAlerts = alerts.filter(
    (alert) => {
      const matchesSearch = 
        alert.title.includes(searchQuery) ||
        alert.message.includes(searchQuery);
      
      const matchesType = 
        selectedType === 'all' || 
        (selectedType === 'unread' && !alert.isRead) ||
        alert.type === selectedType;
      
      return matchesSearch && matchesType;
    }
  );

  // دالة لتنسيق التاريخ
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA');
  };

  // تحديد لون التنبيه بناءً على نوعه
  const getAlertColor = (type: string) => {
    switch (type) {
      case 'ALLOWANCE':
        return 'bg-blue-100 border-blue-500';
      case 'PROMOTION':
        return 'bg-green-100 border-green-500';
      case 'RETIREMENT':
        return 'bg-amber-100 border-amber-500';
      default:
        return 'bg-gray-100 border-gray-500';
    }
  };

  // تحديد عنوان التنبيه بناءً على نوعه
  const getAlertTypeTitle = (type: string) => {
    switch (type) {
      case 'ALLOWANCE':
        return 'علاوة';
      case 'PROMOTION':
        return 'ترفيع';
      case 'RETIREMENT':
        return 'تقاعد';
      default:
        return 'أخرى';
    }
  };

  // تحديد لون شريحة النوع
  const getChipColor = (type: string) => {
    switch (type) {
      case 'ALLOWANCE':
        return 'primary';
      case 'PROMOTION':
        return 'success';
      case 'RETIREMENT':
        return 'warning';
      default:
        return 'default';
    }
  };

  return (
    <div>
      <Card className="shadow-md">
        <CardHeader className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <FaBell className="text-primary" />
            <h1 className="text-2xl font-bold">التنبيهات</h1>
          </div>
          <Button color="primary" startContent={<FaCheck />}>
            تعليم الكل كمقروء
          </Button>
        </CardHeader>
        <CardBody>
          <div className="flex flex-col gap-4">
            <div className="flex flex-col sm:flex-row justify-between gap-4">
              <Input
                placeholder="البحث في التنبيهات..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                startContent={<FaSearch className="text-gray-400" />}
                className="max-w-md"
              />
              <Tabs 
                selectedKey={selectedType}
                onSelectionChange={(key) => setSelectedType(key as string)}
                color="primary"
                variant="light"
              >
                <Tab key="all" title="الكل" />
                <Tab key="unread" title="غير مقروءة" />
                <Tab key="ALLOWANCE" title="العلاوات" />
                <Tab key="PROMOTION" title="الترفيعات" />
                <Tab key="RETIREMENT" title="التقاعد" />
              </Tabs>
            </div>

            <div className="flex flex-col gap-3 mt-4">
              {filteredAlerts.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  لا توجد تنبيهات
                </div>
              ) : (
                filteredAlerts.map((alert) => (
                  <div
                    key={alert.id}
                    className={`p-4 border-r-4 rounded ${getAlertColor(alert.type)} ${!alert.isRead ? 'border-2' : ''}`}
                  >
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <h5 className="font-semibold">{alert.title}</h5>
                        {!alert.isRead && (
                          <span className="w-2 h-2 rounded-full bg-red-500"></span>
                        )}
                      </div>
                      <Chip
                        color={getChipColor(alert.type) as any}
                        size="sm"
                        variant="flat"
                      >
                        {getAlertTypeTitle(alert.type)}
                      </Chip>
                    </div>
                    <p className="text-sm mt-2">{alert.message}</p>
                    <div className="flex justify-between items-center mt-3">
                      <p className="text-xs text-gray-500">
                        {formatDate(alert.date)}
                      </p>
                      <Button size="sm" variant="light" color="primary">
                        تعليم كمقروء
                      </Button>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
}
