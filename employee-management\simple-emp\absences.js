// الكود الخاص بصفحة الغيابات

document.addEventListener('DOMContentLoaded', function() {
    // تهيئة النظام
    initAbsences();
    
    // إضافة مستمعي الأحداث
    setupEventListeners();
    
    // تحميل بيانات الموظفين
    loadEmployees();
    
    // تحميل بيانات الغيابات
    loadAbsences();
});

// تهيئة نظام الغيابات
function initAbsences() {
    // التحقق من وجود بيانات الغيابات في التخزين المحلي
    if (!localStorage.getItem('absences')) {
        localStorage.setItem('absences', JSON.stringify([]));
    }
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // نموذج إضافة غياب جديد
    const addAbsenceForm = document.getElementById('addAbsenceForm');
    if (addAbsenceForm) {
        addAbsenceForm.addEventListener('submit', handleAddAbsence);
    }
    
    // البحث في الغيابات
    const searchAbsence = document.getElementById('searchAbsence');
    if (searchAbsence) {
        searchAbsence.addEventListener('input', handleSearchAbsences);
    }
    
    // تصفية الغيابات حسب النوع
    const filterAbsenceType = document.getElementById('filterAbsenceType');
    if (filterAbsenceType) {
        filterAbsenceType.addEventListener('change', handleFilterAbsences);
    }
    
    // تصفية الغيابات حسب الشهر
    const filterAbsenceMonth = document.getElementById('filterAbsenceMonth');
    if (filterAbsenceMonth) {
        filterAbsenceMonth.addEventListener('change', handleFilterAbsences);
    }
    
    // أزرار النوافذ المنبثقة
    setupModalButtons();
}

// إعداد أزرار النوافذ المنبثقة
function setupModalButtons() {
    // أزرار إغلاق النوافذ المنبثقة
    const closeButtons = document.querySelectorAll('.close-modal');
    closeButtons.forEach(button => {
        button.addEventListener('click', closeAllModals);
    });
    
    // زر إغلاق نافذة عرض التفاصيل
    const closeViewBtn = document.getElementById('closeViewBtn');
    if (closeViewBtn) {
        closeViewBtn.addEventListener('click', closeAllModals);
    }
    
    // زر تعديل الغياب
    const editAbsenceBtn = document.getElementById('editAbsenceBtn');
    if (editAbsenceBtn) {
        editAbsenceBtn.addEventListener('click', openEditModal);
    }
    
    // زر حفظ التعديلات
    const saveEditBtn = document.getElementById('saveEditBtn');
    if (saveEditBtn) {
        saveEditBtn.addEventListener('click', handleEditAbsence);
    }
    
    // زر إلغاء التعديل
    const cancelEditBtn = document.getElementById('cancelEditBtn');
    if (cancelEditBtn) {
        cancelEditBtn.addEventListener('click', closeAllModals);
    }
    
    // زر تأكيد الحذف
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    if (confirmDeleteBtn) {
        confirmDeleteBtn.addEventListener('click', handleDeleteAbsence);
    }
    
    // زر إلغاء الحذف
    const cancelDeleteBtn = document.getElementById('cancelDeleteBtn');
    if (cancelDeleteBtn) {
        cancelDeleteBtn.addEventListener('click', closeAllModals);
    }
}

// تحميل بيانات الموظفين
function loadEmployees() {
    // الحصول على بيانات الموظفين من التخزين المحلي
    const employees = JSON.parse(localStorage.getItem('employees')) || [];
    
    // الحصول على قوائم الموظفين
    const employeeSelect = document.getElementById('employeeId');
    const editEmployeeSelect = document.getElementById('editEmployeeId');
    
    // مسح القوائم
    if (employeeSelect) employeeSelect.innerHTML = '<option value="">اختر الموظف</option>';
    if (editEmployeeSelect) editEmployeeSelect.innerHTML = '<option value="">اختر الموظف</option>';
    
    // إضافة الموظفين إلى القوائم
    employees.forEach(employee => {
        const option = `<option value="${employee.id}">${employee.fullName}</option>`;
        if (employeeSelect) employeeSelect.innerHTML += option;
        if (editEmployeeSelect) editEmployeeSelect.innerHTML += option;
    });
}

// تحميل بيانات الغيابات
function loadAbsences() {
    // الحصول على بيانات الغيابات من التخزين المحلي
    const absences = JSON.parse(localStorage.getItem('absences')) || [];
    
    // الحصول على جدول الغيابات
    const absencesTable = document.querySelector('#absencesTable tbody');
    if (!absencesTable) return;
    
    // مسح الجدول
    absencesTable.innerHTML = '';
    
    // إضافة الغيابات إلى الجدول
    if (absences.length === 0) {
        absencesTable.innerHTML = '<tr><td colspan="6" class="no-data">لا توجد غيابات مسجلة</td></tr>';
    } else {
        absences.forEach((absence, index) => {
            // الحصول على اسم الموظف
            const employees = JSON.parse(localStorage.getItem('employees')) || [];
            const employee = employees.find(emp => emp.id === absence.employeeId) || { fullName: 'غير معروف' };
            
            // تحديد نوع الغياب بالعربية
            let absenceTypeArabic = '';
            let absenceTypeClass = '';
            switch (absence.absenceType) {
                case 'unauthorized':
                    absenceTypeArabic = 'غياب غير مبرر';
                    absenceTypeClass = 'type-unauthorized';
                    break;
                case 'sick':
                    absenceTypeArabic = 'مرضي';
                    absenceTypeClass = 'type-sick';
                    break;
                case 'emergency':
                    absenceTypeArabic = 'طارئ';
                    absenceTypeClass = 'type-emergency';
                    break;
                case 'other':
                    absenceTypeArabic = 'أخرى';
                    absenceTypeClass = 'type-other';
                    break;
                default:
                    absenceTypeArabic = 'غير معروف';
                    absenceTypeClass = 'type-other';
            }
            
            // إضافة صف جديد
            const row = `
                <tr data-id="${absence.id}">
                    <td>${index + 1}</td>
                    <td>${employee.fullName}</td>
                    <td><span class="absence-type ${absenceTypeClass}">${absenceTypeArabic}</span></td>
                    <td>${formatDate(absence.absenceDate)}</td>
                    <td>${absence.absenceReason || 'غير محدد'}</td>
                    <td class="action-buttons">
                        <button class="action-btn view-btn" onclick="openViewModal('${absence.id}')"><i class="fas fa-eye"></i></button>
                        <button class="action-btn edit-btn" onclick="openEditModal('${absence.id}')"><i class="fas fa-edit"></i></button>
                        <button class="action-btn delete-btn" onclick="openDeleteModal('${absence.id}')"><i class="fas fa-trash"></i></button>
                    </td>
                </tr>
            `;
            absencesTable.innerHTML += row;
        });
    }
    
    // تحديث معلومات الترقيم
    updatePagination(absences.length);
}

// معالجة إضافة غياب جديد
function handleAddAbsence(event) {
    event.preventDefault();
    
    // الحصول على بيانات النموذج
    const employeeId = document.getElementById('employeeId').value;
    const absenceType = document.getElementById('absenceType').value;
    const absenceDate = document.getElementById('absenceDate').value;
    const absenceReason = document.getElementById('absenceReason').value;
    const absenceNotes = document.getElementById('absenceNotes').value;
    
    // التحقق من صحة البيانات
    if (!employeeId || !absenceType || !absenceDate) {
        showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }
    
    // إنشاء كائن الغياب
    const absence = {
        id: generateId(),
        employeeId,
        absenceType,
        absenceDate,
        absenceReason,
        absenceNotes
    };
    
    // إضافة الغياب إلى التخزين المحلي
    const absences = JSON.parse(localStorage.getItem('absences')) || [];
    absences.push(absence);
    localStorage.setItem('absences', JSON.stringify(absences));
    
    // إعادة تحميل الغيابات
    loadAbsences();
    
    // إعادة تعيين النموذج
    document.getElementById('addAbsenceForm').reset();
    
    // عرض رسالة نجاح
    showNotification('تمت إضافة الغياب بنجاح', 'success');
}

// فتح نافذة عرض تفاصيل الغياب
function openViewModal(absenceId) {
    // الحصول على بيانات الغياب
    const absences = JSON.parse(localStorage.getItem('absences')) || [];
    const absence = absences.find(a => a.id === absenceId);
    
    if (!absence) {
        showNotification('لم يتم العثور على الغياب', 'error');
        return;
    }
    
    // الحصول على اسم الموظف
    const employees = JSON.parse(localStorage.getItem('employees')) || [];
    const employee = employees.find(emp => emp.id === absence.employeeId) || { fullName: 'غير معروف' };
    
    // تحديد نوع الغياب بالعربية
    let absenceTypeArabic = '';
    switch (absence.absenceType) {
        case 'unauthorized':
            absenceTypeArabic = 'غياب غير مبرر';
            break;
        case 'sick':
            absenceTypeArabic = 'مرضي';
            break;
        case 'emergency':
            absenceTypeArabic = 'طارئ';
            break;
        case 'other':
            absenceTypeArabic = 'أخرى';
            break;
        default:
            absenceTypeArabic = 'غير معروف';
    }
    
    // ملء بيانات النافذة
    document.getElementById('viewEmployeeName').textContent = employee.fullName;
    document.getElementById('viewAbsenceType').textContent = absenceTypeArabic;
    document.getElementById('viewAbsenceDate').textContent = formatDate(absence.absenceDate);
    document.getElementById('viewAbsenceReason').textContent = absence.absenceReason || 'غير محدد';
    document.getElementById('viewAbsenceNotes').textContent = absence.absenceNotes || 'غير محدد';
    
    // تخزين معرف الغياب للاستخدام لاحقاً
    document.getElementById('editAbsenceBtn').setAttribute('data-id', absenceId);
    
    // فتح النافذة
    document.getElementById('viewAbsenceModal').style.display = 'block';
}

// فتح نافذة تعديل الغياب
function openEditModal(absenceId) {
    // إذا تم استدعاء الدالة من زر التعديل في نافذة العرض
    if (typeof absenceId === 'object') {
        absenceId = absenceId.target.getAttribute('data-id');
        // إغلاق نافذة العرض
        document.getElementById('viewAbsenceModal').style.display = 'none';
    }
    
    // الحصول على بيانات الغياب
    const absences = JSON.parse(localStorage.getItem('absences')) || [];
    const absence = absences.find(a => a.id === absenceId);
    
    if (!absence) {
        showNotification('لم يتم العثور على الغياب', 'error');
        return;
    }
    
    // ملء نموذج التعديل
    document.getElementById('editAbsenceId').value = absence.id;
    document.getElementById('editEmployeeId').value = absence.employeeId;
    document.getElementById('editAbsenceType').value = absence.absenceType;
    document.getElementById('editAbsenceDate').value = absence.absenceDate;
    document.getElementById('editAbsenceReason').value = absence.absenceReason || '';
    document.getElementById('editAbsenceNotes').value = absence.absenceNotes || '';
    
    // فتح النافذة
    document.getElementById('editModal').style.display = 'block';
}

// فتح نافذة تأكيد الحذف
function openDeleteModal(absenceId) {
    // تخزين معرف الغياب للاستخدام لاحقاً
    document.getElementById('confirmDeleteBtn').setAttribute('data-id', absenceId);
    
    // فتح النافذة
    document.getElementById('deleteModal').style.display = 'block';
}

// معالجة تعديل الغياب
function handleEditAbsence() {
    // الحصول على بيانات النموذج
    const absenceId = document.getElementById('editAbsenceId').value;
    const employeeId = document.getElementById('editEmployeeId').value;
    const absenceType = document.getElementById('editAbsenceType').value;
    const absenceDate = document.getElementById('editAbsenceDate').value;
    const absenceReason = document.getElementById('editAbsenceReason').value;
    const absenceNotes = document.getElementById('editAbsenceNotes').value;
    
    // التحقق من صحة البيانات
    if (!employeeId || !absenceType || !absenceDate) {
        showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }
    
    // تحديث الغياب في التخزين المحلي
    const absences = JSON.parse(localStorage.getItem('absences')) || [];
    const absenceIndex = absences.findIndex(a => a.id === absenceId);
    
    if (absenceIndex === -1) {
        showNotification('لم يتم العثور على الغياب', 'error');
        return;
    }
    
    absences[absenceIndex] = {
        id: absenceId,
        employeeId,
        absenceType,
        absenceDate,
        absenceReason,
        absenceNotes
    };
    
    localStorage.setItem('absences', JSON.stringify(absences));
    
    // إعادة تحميل الغيابات
    loadAbsences();
    
    // إغلاق النافذة
    closeAllModals();
    
    // عرض رسالة نجاح
    showNotification('تم تحديث الغياب بنجاح', 'success');
}

// معالجة حذف الغياب
function handleDeleteAbsence() {
    // الحصول على معرف الغياب
    const absenceId = document.getElementById('confirmDeleteBtn').getAttribute('data-id');
    
    // حذف الغياب من التخزين المحلي
    const absences = JSON.parse(localStorage.getItem('absences')) || [];
    const updatedAbsences = absences.filter(a => a.id !== absenceId);
    
    localStorage.setItem('absences', JSON.stringify(updatedAbsences));
    
    // إعادة تحميل الغيابات
    loadAbsences();
    
    // إغلاق النافذة
    closeAllModals();
    
    // عرض رسالة نجاح
    showNotification('تم حذف الغياب بنجاح', 'success');
}

// معالجة البحث في الغيابات
function handleSearchAbsences() {
    // الحصول على نص البحث
    const searchText = document.getElementById('searchAbsence').value.toLowerCase();
    
    // الحصول على نوع التصفية
    const filterType = document.getElementById('filterAbsenceType').value;
    
    // الحصول على شهر التصفية
    const filterMonth = document.getElementById('filterAbsenceMonth').value;
    
    // تصفية الغيابات
    filterAbsences(searchText, filterType, filterMonth);
}

// معالجة تصفية الغيابات
function handleFilterAbsences() {
    // الحصول على نص البحث
    const searchText = document.getElementById('searchAbsence').value.toLowerCase();
    
    // الحصول على نوع التصفية
    const filterType = document.getElementById('filterAbsenceType').value;
    
    // الحصول على شهر التصفية
    const filterMonth = document.getElementById('filterAbsenceMonth').value;
    
    // تصفية الغيابات
    filterAbsences(searchText, filterType, filterMonth);
}

// تصفية الغيابات
function filterAbsences(searchText, filterType, filterMonth) {
    // الحصول على بيانات الغيابات
    const absences = JSON.parse(localStorage.getItem('absences')) || [];
    
    // الحصول على بيانات الموظفين
    const employees = JSON.parse(localStorage.getItem('employees')) || [];
    
    // تصفية الغيابات
    const filteredAbsences = absences.filter(absence => {
        // البحث في اسم الموظف
        const employee = employees.find(emp => emp.id === absence.employeeId) || { fullName: '' };
        const nameMatch = employee.fullName.toLowerCase().includes(searchText);
        
        // البحث في سبب الغياب
        const reasonMatch = absence.absenceReason ? absence.absenceReason.toLowerCase().includes(searchText) : false;
        
        // تطبيق تصفية النوع
        const typeMatch = filterType ? absence.absenceType === filterType : true;
        
        // تطبيق تصفية الشهر
        let monthMatch = true;
        if (filterMonth) {
            const absenceDate = new Date(absence.absenceDate);
            monthMatch = (absenceDate.getMonth() + 1).toString() === filterMonth;
        }
        
        return (nameMatch || reasonMatch) && typeMatch && monthMatch;
    });
    
    // عرض الغيابات المصفاة
    displayFilteredAbsences(filteredAbsences);
}

// عرض الغيابات المصفاة
function displayFilteredAbsences(filteredAbsences) {
    // الحصول على جدول الغيابات
    const absencesTable = document.querySelector('#absencesTable tbody');
    if (!absencesTable) return;
    
    // مسح الجدول
    absencesTable.innerHTML = '';
    
    // إضافة الغيابات المصفاة إلى الجدول
    if (filteredAbsences.length === 0) {
        absencesTable.innerHTML = '<tr><td colspan="6" class="no-data">لا توجد غيابات مطابقة</td></tr>';
    } else {
        filteredAbsences.forEach((absence, index) => {
            // الحصول على اسم الموظف
            const employees = JSON.parse(localStorage.getItem('employees')) || [];
            const employee = employees.find(emp => emp.id === absence.employeeId) || { fullName: 'غير معروف' };
            
            // تحديد نوع الغياب بالعربية
            let absenceTypeArabic = '';
            let absenceTypeClass = '';
            switch (absence.absenceType) {
                case 'unauthorized':
                    absenceTypeArabic = 'غياب غير مبرر';
                    absenceTypeClass = 'type-unauthorized';
                    break;
                case 'sick':
                    absenceTypeArabic = 'مرضي';
                    absenceTypeClass = 'type-sick';
                    break;
                case 'emergency':
                    absenceTypeArabic = 'طارئ';
                    absenceTypeClass = 'type-emergency';
                    break;
                case 'other':
                    absenceTypeArabic = 'أخرى';
                    absenceTypeClass = 'type-other';
                    break;
                default:
                    absenceTypeArabic = 'غير معروف';
                    absenceTypeClass = 'type-other';
            }
            
            // إضافة صف جديد
            const row = `
                <tr data-id="${absence.id}">
                    <td>${index + 1}</td>
                    <td>${employee.fullName}</td>
                    <td><span class="absence-type ${absenceTypeClass}">${absenceTypeArabic}</span></td>
                    <td>${formatDate(absence.absenceDate)}</td>
                    <td>${absence.absenceReason || 'غير محدد'}</td>
                    <td class="action-buttons">
                        <button class="action-btn view-btn" onclick="openViewModal('${absence.id}')"><i class="fas fa-eye"></i></button>
                        <button class="action-btn edit-btn" onclick="openEditModal('${absence.id}')"><i class="fas fa-edit"></i></button>
                        <button class="action-btn delete-btn" onclick="openDeleteModal('${absence.id}')"><i class="fas fa-trash"></i></button>
                    </td>
                </tr>
            `;
            absencesTable.innerHTML += row;
        });
    }
    
    // تحديث معلومات الترقيم
    updatePagination(filteredAbsences.length);
}

// تحديث معلومات الترقيم
function updatePagination(totalItems) {
    document.getElementById('currentPage').textContent = '1';
    document.getElementById('totalPages').textContent = Math.ceil(totalItems / 10) || 1;
}

// إغلاق جميع النوافذ المنبثقة
function closeAllModals() {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.style.display = 'none';
    });
}

// عرض إشعار
function showNotification(message, type) {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    
    // تحديد أيقونة الإشعار
    let icon = '';
    switch (type) {
        case 'success':
            icon = '<i class="fas fa-check-circle"></i>';
            break;
        case 'error':
            icon = '<i class="fas fa-times-circle"></i>';
            break;
        case 'info':
            icon = '<i class="fas fa-info-circle"></i>';
            break;
        case 'warning':
            icon = '<i class="fas fa-exclamation-circle"></i>';
            break;
        default:
            icon = '<i class="fas fa-info-circle"></i>';
    }
    
    // إضافة محتوى الإشعار
    notification.innerHTML = `
        <div class="notification-content">
            ${icon}
            <span>${message}</span>
        </div>
        <button class="notification-close">&times;</button>
    `;
    
    // إضافة الإشعار إلى الصفحة
    document.body.appendChild(notification);
    
    // إضافة مستمع حدث لزر الإغلاق
    const closeButton = notification.querySelector('.notification-close');
    closeButton.addEventListener('click', () => {
        notification.classList.add('fade-out');
        setTimeout(() => {
            notification.remove();
        }, 300);
    });
    
    // إغلاق الإشعار تلقائياً بعد 5 ثوانٍ
    setTimeout(() => {
        notification.classList.add('fade-out');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 5000);
}

// تنسيق التاريخ (ميلادي فقط - إجباري)
function formatDate(dateString) {
    if (!dateString) return '-';

    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;

    // إجبار استخدام التقويم الميلادي مع تنسيق يدوي لضمان عدم ظهور التاريخ الهجري
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();

    const monthNames = [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];

    return `${day} ${monthNames[month - 1]} ${year}`;
}

// إنشاء معرف فريد
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}
