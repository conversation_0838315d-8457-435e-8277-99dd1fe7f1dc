/* أنماط خاصة بصفحات التشكرات والعقوبات */

/* حاوية التشكرات والعقوبات */
.thanks-container,
.penalties-container {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 1rem;
}

/* عنوان الصفحة */
.page-title {
    font-size: 1.8rem;
    margin-bottom: 2rem;
    color: var(--primary-color);
    text-align: center;
    font-weight: 700;
    position: relative;
    padding-bottom: 1rem;
}

.page-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: 2px;
}

/* محتوى التشكرات والعقوبات */
.thanks-content,
.penalties-content {
    display: grid;
    grid-template-columns: 350px 1fr;
    gap: 2rem;
}

/* قسم النموذج */
.thanks-form-section,
.penalties-form-section {
    background-color: var(--card-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
    position: relative;
    overflow: hidden;
}

.thanks-form-section::before,
.penalties-form-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: var(--gradient-primary);
}

.thanks-form-section h2,
.penalties-form-section h2 {
    font-size: 1.3rem;
    margin-bottom: 1.5rem;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.thanks-form-section h2 i,
.penalties-form-section h2 i {
    font-size: 1.4rem;
}

/* نموذج التشكرات والعقوبات */
.thanks-form,
.penalties-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.thanks-form .form-group,
.penalties-form .form-group {
    margin-bottom: 1rem;
}

.thanks-form .form-group label,
.penalties-form .form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-color);
}

.required {
    color: var(--danger-color);
}

.thanks-form .form-group input,
.thanks-form .form-group select,
.thanks-form .form-group textarea,
.penalties-form .form-group input,
.penalties-form .form-group select,
.penalties-form .form-group textarea {
    width: 100%;
    padding: 0.8rem 1rem;
    border: 1px solid #e5e7eb;
    border-radius: var(--border-radius);
    font-family: 'Cairo', sans-serif;
    font-size: 1rem;
    transition: var(--transition);
}

.thanks-form .form-group textarea,
.penalties-form .form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.thanks-form .form-group input:focus,
.thanks-form .form-group select:focus,
.thanks-form .form-group textarea:focus,
.penalties-form .form-group input:focus,
.penalties-form .form-group select:focus,
.penalties-form .form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* أزرار النموذج */
.form-actions {
    display: flex;
    justify-content: flex-start;
    gap: 1rem;
    margin-top: 1.5rem;
}

.form-actions .btn {
    padding: 0.7rem 1.5rem;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-actions .btn i {
    font-size: 1.1rem;
}

/* قسم القائمة */
.thanks-list-section,
.penalties-list-section {
    background-color: var(--card-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
    position: relative;
    overflow: hidden;
}

.thanks-list-section::before,
.penalties-list-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: var(--gradient-secondary);
}

/* رأس القائمة */
.list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.list-header h2 {
    font-size: 1.3rem;
    color: var(--secondary-color);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0;
}

.list-header h2 i {
    font-size: 1.4rem;
}

/* إجراءات القائمة */
.list-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

/* حاوية البحث */
.search-container {
    display: flex;
    align-items: center;
    position: relative;
}

.search-input {
    padding: 0.6rem 1rem;
    border: 1px solid #e5e7eb;
    border-radius: 50px;
    font-family: 'Cairo', sans-serif;
    font-size: 0.9rem;
    width: 200px;
    transition: var(--transition);
}

.search-input:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
    width: 250px;
}

.search-btn {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    font-size: 0.9rem;
}

.filter-select {
    padding: 0.6rem 1rem;
    border: 1px solid #e5e7eb;
    border-radius: var(--border-radius);
    font-family: 'Cairo', sans-serif;
    font-size: 0.9rem;
    transition: var(--transition);
}

.filter-select:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
}

/* قائمة التشكرات والعقوبات */
.thanks-list,
.penalties-list {
    overflow-x: auto;
    margin-bottom: 1rem;
}

.thanks-list table,
.penalties-list table {
    width: 100%;
    border-collapse: collapse;
    text-align: right;
}

.thanks-list table th,
.thanks-list table td,
.penalties-list table th,
.penalties-list table td {
    padding: 0.8rem 1rem;
    border-bottom: 1px solid #eee;
}

.thanks-list table th,
.penalties-list table th {
    background-color: rgba(5, 150, 105, 0.05);
    font-weight: 600;
    color: var(--text-color);
}

.thanks-list table tr:hover,
.penalties-list table tr:hover {
    background-color: rgba(5, 150, 105, 0.03);
}

/* أزرار الإجراءات في الجدول */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.action-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.9rem;
}

.view-btn {
    background-color: rgba(5, 150, 105, 0.1);
    color: var(--secondary-color);
}

.view-btn:hover {
    background-color: var(--secondary-color);
    color: white;
}

.edit-btn {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--primary-color);
}

.edit-btn:hover {
    background-color: var(--primary-color);
    color: white;
}

.delete-btn {
    background-color: rgba(220, 38, 38, 0.1);
    color: var(--danger-color);
}

.delete-btn:hover {
    background-color: var(--danger-color);
    color: white;
}

/* ترقيم الصفحات */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-top: 1.5rem;
}

.pagination-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(5, 150, 105, 0.1);
    color: var(--secondary-color);
    border: none;
    cursor: pointer;
    transition: var(--transition);
}

.pagination-btn:hover:not(:disabled) {
    background-color: var(--secondary-color);
    color: white;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-info {
    font-size: 0.9rem;
    color: var(--text-light);
}

/* تفاصيل التشكر والعقوبة */
.thanks-details,
.penalty-details {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
}

.info-group {
    display: flex;
    flex-direction: column;
    gap: 0.3rem;
}

.info-group label {
    font-weight: 600;
    color: var(--text-light);
    font-size: 0.9rem;
}

.info-group span {
    color: var(--text-color);
    font-size: 1rem;
}

/* تصميم متجاوب */
@media (max-width: 992px) {
    .thanks-content,
    .penalties-content {
        grid-template-columns: 1fr;
    }
    
    .thanks-details,
    .penalty-details {
        grid-template-columns: 1fr;
    }
}

/* تأثيرات الوضع المظلم */
.dark-mode .thanks-form-section,
.dark-mode .penalties-form-section,
.dark-mode .thanks-list-section,
.dark-mode .penalties-list-section {
    background-color: var(--card-color);
}

.dark-mode .list-header {
    border-color: #333;
}

.dark-mode .thanks-list table th,
.dark-mode .penalties-list table th {
    background-color: rgba(5, 150, 105, 0.1);
}

.dark-mode .thanks-list table td,
.dark-mode .penalties-list table td {
    border-color: #333;
}

.dark-mode .thanks-form .form-group input,
.dark-mode .thanks-form .form-group select,
.dark-mode .thanks-form .form-group textarea,
.dark-mode .penalties-form .form-group input,
.dark-mode .penalties-form .form-group select,
.dark-mode .penalties-form .form-group textarea,
.dark-mode .search-input,
.dark-mode .filter-select {
    background-color: #2a2a2a;
    border-color: #444;
    color: white;
}

.dark-mode .thanks-form .form-group input::placeholder,
.dark-mode .thanks-form .form-group select::placeholder,
.dark-mode .thanks-form .form-group textarea::placeholder,
.dark-mode .penalties-form .form-group input::placeholder,
.dark-mode .penalties-form .form-group select::placeholder,
.dark-mode .penalties-form .form-group textarea::placeholder,
.dark-mode .search-input::placeholder {
    color: #9ca3af;
}

/* أنواع التشكرات والعقوبات */
.thanks-type,
.penalty-type {
    display: inline-block;
    padding: 0.3rem 0.8rem;
    border-radius: 50px;
    font-size: 0.8rem;
    font-weight: 600;
    text-align: center;
}

.thanks-ministerial {
    background-color: rgba(79, 70, 229, 0.1);
    color: #4f46e5;
}

.thanks-presidential {
    background-color: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
}

.thanks-university {
    background-color: rgba(16, 185, 129, 0.1);
    color: #10b981;
}

.thanks-college {
    background-color: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}

.thanks-department {
    background-color: rgba(107, 114, 128, 0.1);
    color: #6b7280;
}

.penalty-severe {
    background-color: rgba(220, 38, 38, 0.1);
    color: #dc2626;
}

.penalty-medium {
    background-color: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
}

.penalty-light {
    background-color: rgba(107, 114, 128, 0.1);
    color: #6b7280;
}
