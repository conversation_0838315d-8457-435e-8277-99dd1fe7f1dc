'use client';

import { <PERSON>, CardB<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '@nextui-org/react';
import { FaBell, FaArrowRight } from 'react-icons/fa';
import Link from 'next/link';

type Alert = {
  id: string;
  title: string;
  message: string;
  type: 'ALLOWANCE' | 'PROMOTION' | 'RETIREMENT' | 'OTHER';
  date: string;
};

export default function AlertsSection() {
  // بيانات تجريبية للتنبيهات
  const alerts: Alert[] = [
    {
      id: '1',
      title: 'علاوة مستحقة',
      message: 'الموظف أحمد محمد مستحق للعلاوة السنوية',
      type: 'ALLOWANCE',
      date: '2025-05-10',
    },
    {
      id: '2',
      title: 'ترفيع مستحق',
      message: 'الموظفة سارة أحمد مستحقة للترفيع الوظيفي',
      type: 'PROMOTION',
      date: '2025-05-15',
    },
    {
      id: '3',
      title: 'إحالة للتقاعد',
      message: 'الموظف محمود علي سيتم إحالته للتقاعد قريباً',
      type: 'RETIREMENT',
      date: '2025-06-01',
    },
    {
      id: '4',
      title: 'علاوة مستحقة',
      message: 'الموظف خالد عبدالله مستحق للعلاوة السنوية',
      type: 'ALLOWANCE',
      date: '2025-05-20',
    },
  ];

  // تحديد لون التنبيه بناءً على نوعه
  const getAlertColor = (type: string) => {
    switch (type) {
      case 'ALLOWANCE':
        return 'bg-blue-100 border-blue-500';
      case 'PROMOTION':
        return 'bg-green-100 border-green-500';
      case 'RETIREMENT':
        return 'bg-amber-100 border-amber-500';
      default:
        return 'bg-gray-100 border-gray-500';
    }
  };

  // تحديد عنوان التنبيه بناءً على نوعه
  const getAlertTypeTitle = (type: string) => {
    switch (type) {
      case 'ALLOWANCE':
        return 'علاوة';
      case 'PROMOTION':
        return 'ترفيع';
      case 'RETIREMENT':
        return 'تقاعد';
      default:
        return 'أخرى';
    }
  };

  return (
    <Card className="shadow-md mt-6">
      <CardHeader className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <FaBell className="text-primary" />
          <h4 className="font-bold text-large">التنبيهات الأخيرة</h4>
        </div>
        <Link href="/alerts">
          <Button
            variant="light"
            color="primary"
            endContent={<FaArrowRight />}
            size="sm"
          >
            عرض الكل
          </Button>
        </Link>
      </CardHeader>
      <CardBody>
        <div className="flex flex-col gap-3">
          {alerts.map((alert) => (
            <div
              key={alert.id}
              className={`p-3 border-r-4 rounded ${getAlertColor(alert.type)}`}
            >
              <div className="flex justify-between">
                <h5 className="font-semibold">{alert.title}</h5>
                <span className="text-xs bg-primary/10 px-2 py-1 rounded-full">
                  {getAlertTypeTitle(alert.type)}
                </span>
              </div>
              <p className="text-sm mt-1">{alert.message}</p>
              <p className="text-xs text-gray-500 mt-2">
                {new Date(alert.date).toLocaleDateString('ar-SA')}
              </p>
            </div>
          ))}
        </div>
      </CardBody>
    </Card>
  );
}
