/**
 * ملف لاحتساب كتب الشكر والتقدير وتأثيرها على القدم
 */

// احتساب كتب الشكر والتقدير للموظف
function calculateThanksEffect(employeeId) {
    console.log('بدء احتساب تأثير كتب الشكر والتقدير للموظف:', employeeId);

    // الحصول على بيانات الموظف
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');
    const employee = employees.find(emp => emp.id == employeeId);

    if (!employee) {
        console.error('لم يتم العثور على الموظف:', employeeId);
        return null;
    }

    // الحصول على تاريخ آخر ترفيع (بداية السنة الوظيفية)
    const lastPromotionDate = employee.lastPromotionDate ? new Date(employee.lastPromotionDate) : new Date(employee.hireDate);
    console.log('تاريخ آخر ترفيع (بداية السنة الوظيفية):', lastPromotionDate.toISOString().split('T')[0]);

    // الحصول على تاريخ آخر استحقاق (للعلاوة)
    const currentDueDate = employee.currentDueDate ? new Date(employee.currentDueDate) : new Date(employee.hireDate);
    console.log('تاريخ آخر استحقاق (للعلاوة):', currentDueDate.toISOString().split('T')[0]);

    // الحصول على كتب الشكر والتقدير للموظف
    const allThanks = JSON.parse(localStorage.getItem('thanks') || '[]');
    const employeeThanks = allThanks.filter(thank => thank.employeeId == employeeId);
    console.log('عدد كتب الشكر والتقدير للموظف:', employeeThanks.length);

    // تصفية كتب الشكر للحصول على تلك التي تؤثر على العلاوة (بعد تاريخ آخر استحقاق)
    const allowanceThanks = employeeThanks.filter(thank => new Date(thank.date) >= currentDueDate);
    console.log('عدد كتب الشكر التي تؤثر على العلاوة:', allowanceThanks.length);

    // جميع كتب الشكر تؤثر على الترفيع بغض النظر عن تاريخها
    const promotionThanks = employeeThanks;
    console.log('عدد كتب الشكر التي تؤثر على الترفيع:', promotionThanks.length);

    // تنظيم كتب الشكر حسب السنوات الوظيفية للعلاوة
    const allowanceThanksByJobYear = organizeThanksByJobYear(allowanceThanks, currentDueDate);
    console.log('كتب الشكر المؤثرة على العلاوة منظمة حسب السنوات الوظيفية:', allowanceThanksByJobYear);

    // تنظيم كتب الشكر حسب السنوات الوظيفية للترفيع
    // استخدام تاريخ التعيين بدلاً من تاريخ آخر ترفيع لتضمين جميع كتب الشكر
    const hireDate = employee.hireDate ? new Date(employee.hireDate) : new Date(lastPromotionDate);
    const promotionThanksByJobYear = organizeThanksByJobYear(promotionThanks, hireDate);
    console.log('كتب الشكر المؤثرة على الترفيع منظمة حسب السنوات الوظيفية:', promotionThanksByJobYear);

    // احتساب القدم المكتسب من كتب الشكر للعلاوة
    const allowanceSeniority = calculateTotalSeniority(allowanceThanksByJobYear);
    console.log('إجمالي القدم المكتسب للعلاوة من كتب الشكر (بالأشهر):', allowanceSeniority);

    // احتساب القدم المكتسب من كتب الشكر للترفيع
    const promotionSeniority = calculateTotalSeniority(promotionThanksByJobYear);
    console.log('إجمالي القدم المكتسب للترفيع من كتب الشكر (بالأشهر):', promotionSeniority);

    return {
        lastPromotionDate: lastPromotionDate,
        currentDueDate: currentDueDate,
        allowanceThanksByJobYear: allowanceThanksByJobYear,
        promotionThanksByJobYear: promotionThanksByJobYear,
        allowanceSeniority: allowanceSeniority,
        promotionSeniority: promotionSeniority,
        totalSeniority: Math.max(allowanceSeniority, promotionSeniority) // للتوافق مع الكود القديم
    };
}

// تنظيم كتب الشكر حسب السنوات الوظيفية
function organizeThanksByJobYear(thanks, startDate) {
    // ترتيب كتب الشكر حسب التاريخ (الأقدم أولاً)
    thanks.sort((a, b) => new Date(a.date) - new Date(b.date));

    const thanksByJobYear = {};
    const currentDate = new Date();

    // حساب عدد السنوات الوظيفية منذ تاريخ البداية
    const yearsSinceStart = Math.floor((currentDate - startDate) / (365.25 * 24 * 60 * 60 * 1000));
    console.log('عدد السنوات الوظيفية منذ تاريخ البداية:', yearsSinceStart);

    // إنشاء مصفوفة فارغة لكل سنة وظيفية
    for (let i = 0; i <= yearsSinceStart; i++) {
        thanksByJobYear[i] = [];
    }

    // توزيع كتب الشكر على السنوات الوظيفية
    thanks.forEach(thank => {
        const thankDate = new Date(thank.date);

        // التحقق من أن تاريخ كتاب الشكر بعد تاريخ البداية
        if (thankDate >= startDate) {
            // حساب السنة الوظيفية التي ينتمي إليها كتاب الشكر
            const jobYear = Math.floor((thankDate - startDate) / (365.25 * 24 * 60 * 60 * 1000));

            // إضافة كتاب الشكر إلى السنة الوظيفية المناسبة
            if (thanksByJobYear[jobYear]) {
                thanksByJobYear[jobYear].push(thank);
            }
        } else {
            // إذا كان تاريخ كتاب الشكر قبل تاريخ البداية، أضفه إلى السنة الوظيفية الأولى
            if (thanksByJobYear[0]) {
                thanksByJobYear[0].push(thank);
            }
        }
    });

    return thanksByJobYear;
}

// احتساب إجمالي القدم المكتسب من كتب الشكر
function calculateTotalSeniority(thanksByJobYear) {
    let totalSeniority = 0;

    // حساب القدم لكل سنة وظيفية
    for (const jobYear in thanksByJobYear) {
        // الحد الأقصى لعدد كتب الشكر في السنة الوظيفية هو 3
        const thanksCount = Math.min(thanksByJobYear[jobYear].length, 3);

        // كل كتاب شكر يمنح شهر واحد من القدم
        const yearSeniority = thanksCount;

        totalSeniority += yearSeniority;

        console.log(`السنة الوظيفية ${jobYear}: عدد كتب الشكر = ${thanksCount}, القدم المكتسب = ${yearSeniority} شهر`);
    }

    return totalSeniority;
}

// تحديث تاريخ الاستحقاق الجديد بناءً على القدم المكتسب من كتب الشكر
function updateNewDueDateBasedOnThanks(employeeId) {
    console.log('تحديث تاريخ الاستحقاق الجديد بناءً على القدم المكتسب من كتب الشكر للموظف:', employeeId);

    // احتساب تأثير كتب الشكر
    const thanksEffect = calculateThanksEffect(employeeId);

    if (!thanksEffect) {
        console.error('فشل في احتساب تأثير كتب الشكر');
        return false;
    }

    // الحصول على بيانات الموظف
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');
    const employeeIndex = employees.findIndex(emp => emp.id == employeeId);

    if (employeeIndex === -1) {
        console.error('لم يتم العثور على الموظف:', employeeId);
        return false;
    }

    const employee = employees[employeeIndex];

    // الحصول على تاريخ الاستحقاق الحالي
    const currentDueDate = new Date(employee.currentDueDate || employee.hireDate);
    console.log('تاريخ الاستحقاق الحالي:', currentDueDate.toISOString().split('T')[0]);

    // الحصول على تاريخ الاستحقاق الجديد الأصلي (قبل احتساب القدم)
    let originalNewDueDate;
    if (employee.newDueDate) {
        // إذا كان هناك تاريخ استحقاق جديد محدد مسبقاً
        originalNewDueDate = new Date(employee.newDueDate);
    } else {
        // إذا لم يكن هناك تاريخ استحقاق جديد، نحسبه بإضافة سنة إلى تاريخ الاستحقاق الحالي
        originalNewDueDate = new Date(currentDueDate);
        originalNewDueDate.setFullYear(originalNewDueDate.getFullYear() + 1);
    }
    console.log('تاريخ الاستحقاق الجديد الأصلي:', originalNewDueDate.toISOString().split('T')[0]);

    // حساب تاريخ الاستحقاق الجديد بناءً على القدم المكتسب
    const newDueDate = new Date(originalNewDueDate);
    newDueDate.setMonth(newDueDate.getMonth() - thanksEffect.allowanceSeniority);
    console.log('تاريخ الاستحقاق الجديد بعد احتساب القدم:', newDueDate.toISOString().split('T')[0]);

    // تحديث بيانات الموظف
    employee.allowanceSeniority = thanksEffect.allowanceSeniority;
    employee.newDueDate = newDueDate.toISOString().split('T')[0];

    // حفظ البيانات المحدثة
    localStorage.setItem('employees', JSON.stringify(employees));
    console.log('تم تحديث بيانات الموظف بنجاح');

    return true;
}

// تحديث تاريخ الترفيع القادم بناءً على القدم المكتسب من كتب الشكر
function updateNextPromotionDateBasedOnThanks(employeeId) {
    console.log('تحديث تاريخ الترفيع القادم بناءً على القدم المكتسب من كتب الشكر للموظف:', employeeId);

    // احتساب تأثير كتب الشكر
    const thanksEffect = calculateThanksEffect(employeeId);

    if (!thanksEffect) {
        console.error('فشل في احتساب تأثير كتب الشكر');
        return false;
    }

    // الحصول على بيانات الموظف
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');
    const employeeIndex = employees.findIndex(emp => emp.id == employeeId);

    if (employeeIndex === -1) {
        console.error('لم يتم العثور على الموظف:', employeeId);
        return false;
    }

    const employee = employees[employeeIndex];

    // الحصول على تاريخ آخر ترفيع
    const lastPromotionDate = new Date(employee.lastPromotionDate || employee.hireDate);
    console.log('تاريخ آخر ترفيع:', lastPromotionDate.toISOString().split('T')[0]);

    // الحصول على تاريخ الترفيع القادم الأصلي (قبل احتساب القدم)
    let originalNextPromotionDate;
    if (employee.nextPromotionDate) {
        // إذا كان هناك تاريخ ترفيع قادم محدد مسبقاً
        originalNextPromotionDate = new Date(employee.nextPromotionDate);
    } else {
        // إذا لم يكن هناك تاريخ ترفيع قادم، نحسبه بإضافة 4 سنوات إلى تاريخ آخر ترفيع
        originalNextPromotionDate = new Date(lastPromotionDate);
        originalNextPromotionDate.setFullYear(originalNextPromotionDate.getFullYear() + 4);
    }
    console.log('تاريخ الترفيع القادم الأصلي:', originalNextPromotionDate.toISOString().split('T')[0]);

    // حساب تاريخ الترفيع الجديد بناءً على القدم المكتسب
    const newNextPromotionDate = new Date(originalNextPromotionDate);
    newNextPromotionDate.setMonth(newNextPromotionDate.getMonth() - thanksEffect.promotionSeniority);
    console.log('تاريخ الترفيع الجديد بعد احتساب القدم:', newNextPromotionDate.toISOString().split('T')[0]);

    // تحديث بيانات الموظف
    employee.promotionSeniority = thanksEffect.promotionSeniority;
    employee.nextPromotionDate = newNextPromotionDate.toISOString().split('T')[0];

    // حفظ البيانات المحدثة
    localStorage.setItem('employees', JSON.stringify(employees));
    console.log('تم تحديث بيانات الموظف بنجاح');

    return true;
}

// تحديث تواريخ الاستحقاق والترفيع لجميع الموظفين
function updateAllEmployeesDatesBasedOnThanks() {
    console.log('تحديث تواريخ الاستحقاق والترفيع لجميع الموظفين بناءً على كتب الشكر');

    // الحصول على بيانات الموظفين
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');

    // تحديث كل موظف
    let updatedCount = 0;

    employees.forEach(employee => {
        const allowanceUpdated = updateNewDueDateBasedOnThanks(employee.id);
        const promotionUpdated = updateNextPromotionDateBasedOnThanks(employee.id);

        if (allowanceUpdated || promotionUpdated) {
            updatedCount++;
        }
    });

    console.log(`تم تحديث بيانات ${updatedCount} موظف من أصل ${employees.length}`);

    return updatedCount;
}

// تصدير الدوال للاستخدام في ملفات أخرى
window.thanksCalculator = {
    calculateThanksEffect,
    updateNewDueDateBasedOnThanks,
    updateNextPromotionDateBasedOnThanks,
    updateAllEmployeesDatesBasedOnThanks
};
