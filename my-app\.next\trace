[{"name": "hot-reloader", "duration": 80, "timestamp": 18019394147, "id": 3, "tags": {"version": "15.3.1"}, "startTime": 1754494760752, "traceId": "baa3cd6d16734a3d"}, {"name": "setup-dev-bundler", "duration": 697577, "timestamp": 18019360811, "id": 2, "parentId": 1, "tags": {}, "startTime": 1754494760719, "traceId": "baa3cd6d16734a3d"}, {"name": "run-instrumentation-hook", "duration": 27, "timestamp": 18020125393, "id": 4, "parentId": 1, "tags": {}, "startTime": 1754494761484, "traceId": "baa3cd6d16734a3d"}, {"name": "start-dev-server", "duration": 1491917, "timestamp": 18018650670, "id": 1, "tags": {"cpus": "4", "platform": "win32", "memory.freeMem": "7926534144", "memory.totalMem": "17093861376", "memory.heapSizeLimit": "8596226048", "memory.rss": "152080384", "memory.heapTotal": "81051648", "memory.heapUsed": "48870776"}, "startTime": 1754494760009, "traceId": "baa3cd6d16734a3d"}, {"name": "compile-path", "duration": 2822940, "timestamp": 18056472642, "id": 7, "tags": {"trigger": "/"}, "startTime": 1754494797831, "traceId": "baa3cd6d16734a3d"}, {"name": "ensure-page", "duration": 2829843, "timestamp": 18056471901, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1754494797830, "traceId": "baa3cd6d16734a3d"}]