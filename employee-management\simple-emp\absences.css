/* أنماط خاصة بصفحة الغيابات */

/* حاوية الغيابات */
.absences-container {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 1rem;
}

/* عنوان الصفحة */
.page-title {
    font-size: 1.8rem;
    margin-bottom: 2rem;
    color: var(--primary-color);
    text-align: center;
    font-weight: 700;
    position: relative;
    padding-bottom: 1rem;
}

.page-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: 2px;
}

/* محتوى الغيابات */
.absences-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 2rem;
}

/* قسم نموذج الغيابات */
.absences-form-section {
    background-color: var(--card-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
}

.absences-form-section h2 {
    font-size: 1.3rem;
    margin-bottom: 1.5rem;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.absences-form-section h2 i {
    font-size: 1.1rem;
}

/* نموذج الغيابات */
.absences-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.form-group {
    margin-bottom: 0.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-color);
}

.required {
    color: var(--danger-color);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.8rem 1rem;
    border: 1px solid #e5e7eb;
    border-radius: var(--border-radius);
    font-family: 'Cairo', sans-serif;
    font-size: 1rem;
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group input[readonly] {
    background-color: #f9fafb;
    cursor: not-allowed;
}

.form-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 1.5rem;
}

.form-actions .btn {
    padding: 0.8rem 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* قسم قائمة الغيابات */
.absences-list-section {
    background-color: var(--card-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
}

.list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.list-header h2 {
    font-size: 1.3rem;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.list-header h2 i {
    font-size: 1.1rem;
}

.list-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* حاوية البحث */
.search-container {
    position: relative;
    min-width: 200px;
}

.search-input {
    width: 100%;
    padding: 0.8rem 1rem;
    padding-left: 2.5rem;
    border: 1px solid #e5e7eb;
    border-radius: 50px;
    font-family: 'Cairo', sans-serif;
    font-size: 1rem;
    transition: var(--transition);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-btn {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    font-size: 1rem;
}

/* قائمة التصفية */
.filter-select {
    padding: 0.6rem 1rem;
    border: 1px solid #e5e7eb;
    border-radius: var(--border-radius);
    font-family: 'Cairo', sans-serif;
    font-size: 0.9rem;
    min-width: 120px;
    transition: var(--transition);
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* جدول الغيابات */
.absences-list {
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
    text-align: right;
}

th, td {
    padding: 1rem;
    border-bottom: 1px solid #eee;
}

th {
    background-color: rgba(59, 130, 246, 0.05);
    font-weight: 600;
    color: var(--text-color);
    white-space: nowrap;
}

tbody tr {
    transition: var(--transition);
}

tbody tr:hover {
    background-color: rgba(59, 130, 246, 0.03);
}

/* أزرار الإجراءات في الجدول */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.action-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.9rem;
}

.view-btn {
    background-color: rgba(5, 150, 105, 0.1);
    color: var(--secondary-color);
}

.view-btn:hover {
    background-color: var(--secondary-color);
    color: white;
}

.edit-btn {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--primary-color);
}

.edit-btn:hover {
    background-color: var(--primary-color);
    color: white;
}

.delete-btn {
    background-color: rgba(220, 38, 38, 0.1);
    color: var(--danger-color);
}

.delete-btn:hover {
    background-color: var(--danger-color);
    color: white;
}

/* ترقيم الصفحات */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-top: 1.5rem;
}

.pagination-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--primary-color);
    border: none;
    cursor: pointer;
    transition: var(--transition);
}

.pagination-btn:hover:not(:disabled) {
    background-color: var(--primary-color);
    color: white;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-info {
    font-size: 0.9rem;
    color: var(--text-light);
}

/* أنواع الغياب */
.absence-type {
    display: inline-block;
    padding: 0.3rem 0.8rem;
    border-radius: 50px;
    font-size: 0.8rem;
    font-weight: 600;
    text-align: center;
}

.type-unauthorized {
    background-color: rgba(220, 38, 38, 0.1);
    color: var(--danger-color);
}

.type-sick {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--primary-color);
}

.type-emergency {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.type-other {
    background-color: rgba(107, 114, 128, 0.1);
    color: #6b7280;
}

/* تفاصيل الغياب في النافذة المنبثقة */
.absence-details {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
}

.info-group {
    display: flex;
    flex-direction: column;
    gap: 0.3rem;
}

.info-group label {
    font-weight: 600;
    color: var(--text-light);
    font-size: 0.9rem;
}

.info-group span {
    color: var(--text-color);
    font-size: 1rem;
}

/* تصميم متجاوب */
@media (max-width: 992px) {
    .absences-content {
        grid-template-columns: 1fr;
    }
    
    .absence-details {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .list-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .list-actions {
        width: 100%;
        justify-content: space-between;
    }
    
    .search-container {
        width: 100%;
    }
    
    .filter-select {
        flex: 1;
    }
}

/* تأثيرات الوضع المظلم */
.dark-mode .absences-form-section,
.dark-mode .absences-list-section {
    background-color: var(--card-color);
}

.dark-mode th {
    background-color: rgba(59, 130, 246, 0.1);
}

.dark-mode td {
    border-color: #333;
}

.dark-mode .search-input,
.dark-mode .filter-select,
.dark-mode .form-group input,
.dark-mode .form-group select,
.dark-mode .form-group textarea {
    background-color: #2a2a2a;
    border-color: #444;
    color: white;
}

.dark-mode .search-input::placeholder,
.dark-mode .filter-select::placeholder,
.dark-mode .form-group input::placeholder,
.dark-mode .form-group select::placeholder,
.dark-mode .form-group textarea::placeholder {
    color: #9ca3af;
}

.dark-mode .form-group input[readonly] {
    background-color: #1e1e1e;
}

/* رسالة عدم وجود بيانات */
.no-data {
    text-align: center;
    color: var(--text-light);
    padding: 2rem;
    font-style: italic;
}
