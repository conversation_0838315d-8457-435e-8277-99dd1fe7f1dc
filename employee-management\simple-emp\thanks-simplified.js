/**
 * ملف لتبسيط إدارة كتب الشكر
 */

// تبسيط نموذج إضافة كتاب الشكر
function simplifyThanksForm() {
    // إضافة مستمع حدث لتغيير نوع التشكر
    const thanksTypeSelect = document.getElementById('thanksType');
    const thanksEffectSelect = document.getElementById('thanksEffect');
    const thanksIssuerInput = document.getElementById('thanksIssuer');

    if (thanksTypeSelect && thanksEffectSelect && thanksIssuerInput) {
        thanksTypeSelect.addEventListener('change', function() {
            const selectedType = this.value;

            // تحديث التأثير والجهة المانحة تلقائياً بناءً على نوع التشكر
            switch (selectedType) {
                case 'appreciation':
                    thanksEffectSelect.value = '1month_university';
                    thanksIssuerInput.value = 'رئاسة الجامعة';
                    break;
                case 'ministerial':
                    thanksEffectSelect.value = '1month_minister';
                    thanksIssuerInput.value = 'وزارة التعليم العالي';
                    break;
                case 'presidential':
                    thanksEffectSelect.value = '6months_pm';
                    thanksIssuerInput.value = 'رئاسة الوزراء';
                    break;
                default:
                    thanksEffectSelect.value = 'none';
                    thanksIssuerInput.value = '';
            }
        });
    }
}

// إضافة كتاب شكر سريع
function addQuickThanks(employeeId, type = 'appreciation') {
    const currentDate = new Date().toISOString().split('T')[0];

    let issuer, effect;
    switch (type) {
        case 'appreciation':
            issuer = 'رئاسة الجامعة';
            effect = '1month_university';
            break;
        case 'ministerial':
            issuer = 'وزارة التعليم العالي';
            effect = '1month_minister';
            break;
        case 'presidential':
            issuer = 'رئاسة الوزراء';
            effect = '6months_pm';
            break;
        default:
            issuer = 'رئاسة الجامعة';
            effect = '1month_university';
    }

    const thank = {
        id: Date.now(),
        employeeId: parseInt(employeeId),
        type: type,
        date: currentDate,
        number: '',
        issuer: issuer,
        reason: '',
        effect: effect
    };

    // حفظ كتاب الشكر
    const thanks = JSON.parse(localStorage.getItem('thanks') || '[]');
    thanks.push(thank);
    localStorage.setItem('thanks', JSON.stringify(thanks));

    // تحديث تواريخ الموظف
    if (window.thanksCalculator) {
        window.thanksCalculator.updateNewDueDateBasedOnThanks(employeeId);
        window.thanksCalculator.updateNextPromotionDateBasedOnThanks(employeeId);
    }

    return thank;
}

// إضافة أزرار سريعة لكل موظف
function addQuickThanksButtons() {
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');

    // إنشاء قسم الإضافة السريعة
    const quickAddSection = document.createElement('div');
    quickAddSection.className = 'quick-add-section';
    quickAddSection.innerHTML = `
        <div class="section-header">
            <h3><i class="fas fa-bolt"></i> إضافة سريعة لكتب الشكر</h3>
            <p class="section-description">اختر موظف ونوع كتاب الشكر للإضافة السريعة</p>
        </div>
        <div class="quick-add-form">
            <div class="form-row">
                <div class="form-group">
                    <label for="quickEmployeeSelect">الموظف:</label>
                    <select id="quickEmployeeSelect" class="form-control">
                        <option value="">اختر الموظف</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="quickThanksType">نوع التشكر:</label>
                    <select id="quickThanksType" class="form-control">
                        <option value="appreciation">كتاب شكر جامعي</option>
                        <option value="ministerial">كتاب شكر وزاري</option>
                        <option value="presidential">كتاب شكر رئاسي</option>
                    </select>
                </div>
                <div class="form-group">
                    <button id="quickAddBtn" class="btn btn-success">
                        <i class="fas fa-plus"></i> إضافة سريعة
                    </button>
                </div>
            </div>
        </div>
    `;

    // إضافة القسم إلى الصفحة
    const thanksContainer = document.querySelector('.thanks-content');
    if (thanksContainer) {
        thanksContainer.insertBefore(quickAddSection, thanksContainer.firstChild);
    }

    // ملء قائمة الموظفين
    const quickEmployeeSelect = document.getElementById('quickEmployeeSelect');
    if (quickEmployeeSelect) {
        employees.forEach(employee => {
            const option = document.createElement('option');
            option.value = employee.id;
            option.textContent = employee.name;
            quickEmployeeSelect.appendChild(option);
        });
    }

    // إضافة مستمع حدث لزر الإضافة السريعة
    const quickAddBtn = document.getElementById('quickAddBtn');
    if (quickAddBtn) {
        quickAddBtn.addEventListener('click', function() {
            const employeeId = quickEmployeeSelect.value;
            const thanksType = document.getElementById('quickThanksType').value;

            if (!employeeId) {
                showNotification('يرجى اختيار موظف', 'error');
                return;
            }

            // إضافة كتاب الشكر
            const thank = addQuickThanks(employeeId, thanksType);

            // تحديث عرض الجدول
            if (typeof loadThanks === 'function') {
                loadThanks();
            }

            // عرض رسالة نجاح
            const employee = employees.find(emp => emp.id == employeeId);
            const employeeName = employee ? employee.name : 'الموظف';
            showNotification(`تم إضافة كتاب شكر للموظف ${employeeName} بنجاح`, 'success');
        });
    }
}

// تهيئة التبسيطات
function initThanksSimplifications() {
    // تبسيط النموذج
    simplifyThanksForm();

    // إضافة الأزرار السريعة
    addQuickThanksButtons();
}

// تشغيل التبسيطات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تأخير قليل للتأكد من تحميل جميع العناصر
    setTimeout(initThanksSimplifications, 1000);
});
