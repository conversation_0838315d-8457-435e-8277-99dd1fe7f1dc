'use client';

import Link from 'next/link';
import { useState } from 'react';

export default function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <nav className="bg-gradient-to-r from-blue-600 to-blue-800 text-white shadow-lg">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center py-4">
          {/* Logo */}
          <div className="flex items-center space-x-2 space-x-reverse">
            <div className="bg-white text-blue-600 p-2 rounded-lg">
              <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <span className="text-xl font-bold">برنامج إدارة العلاوات والترفيعات</span>
          </div>

          {/* Desktop Menu */}
          <div className="hidden md:flex items-center space-x-6 space-x-reverse">
            <Link href="/" className="hover:text-blue-200 transition-colors">
              الرئيسية
            </Link>
            
            <div className="relative group">
              <button className="hover:text-blue-200 transition-colors flex items-center">
                الملفات التعريفية
                <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all">
                <Link href="/work-locations" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  مواقع العمل
                </Link>
                <Link href="/job-titles" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  المسميات الوظيفية
                </Link>
                <Link href="/education-levels" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  المستويات التعليمية
                </Link>
              </div>
            </div>

            <div className="relative group">
              <button className="hover:text-blue-200 transition-colors flex items-center">
                إدارة الموظفين
                <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all">
                <Link href="/employees" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  قائمة الموظفين
                </Link>
                <Link href="/employees/new" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  إضافة موظف جديد
                </Link>
              </div>
            </div>

            <div className="relative group">
              <button className="hover:text-blue-200 transition-colors flex items-center">
                العلاوات والترفيعات
                <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all">
                <Link href="/allowances" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  إدارة العلاوات
                </Link>
                <Link href="/promotions" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  إدارة الترفيعات
                </Link>
                <Link href="/alerts" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  التنبيهات
                </Link>
              </div>
            </div>

            <Link href="/reports" className="hover:text-blue-200 transition-colors">
              التقارير
            </Link>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-white hover:text-blue-200 focus:outline-none"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden pb-4">
            <div className="flex flex-col space-y-2">
              <Link href="/" className="block py-2 px-4 hover:bg-blue-700 rounded">
                الرئيسية
              </Link>
              <Link href="/employees" className="block py-2 px-4 hover:bg-blue-700 rounded">
                إدارة الموظفين
              </Link>
              <Link href="/allowances" className="block py-2 px-4 hover:bg-blue-700 rounded">
                العلاوات والترفيعات
              </Link>
              <Link href="/reports" className="block py-2 px-4 hover:bg-blue-700 rounded">
                التقارير
              </Link>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}