'use client';

import { <PERSON>, CardB<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Avatar } from '@nextui-org/react';
import { FaUsers, FaArrowRight } from 'react-icons/fa';
import Link from 'next/link';

type Employee = {
  id: string;
  fullName: string;
  employeeNumber: string;
  jobTitle: string;
  workLocation: string;
  hireDate: string;
};

export default function RecentEmployees() {
  // بيانات تجريبية للموظفين
  const employees: Employee[] = [
    {
      id: '1',
      fullName: 'أحمد محمد علي',
      employeeNumber: 'EMP001',
      jobTitle: 'مهندس برمجيات',
      workLocation: 'المقر الرئيسي',
      hireDate: '2025-01-15',
    },
    {
      id: '2',
      fullName: 'سارة أحمد محمود',
      employeeNumber: 'EMP002',
      jobTitle: 'محاسب',
      workLocation: 'الفرع الأول',
      hireDate: '2025-02-10',
    },
    {
      id: '3',
      fullName: 'محمد خالد عبدالله',
      employeeNumber: 'EMP003',
      jobTitle: 'مدير مبيعات',
      workLocation: 'الفرع الثاني',
      hireDate: '2025-03-05',
    },
    {
      id: '4',
      fullName: 'فاطمة علي حسن',
      employeeNumber: 'EMP004',
      jobTitle: 'مسؤول موارد بشرية',
      workLocation: 'المقر الرئيسي',
      hireDate: '2025-04-20',
    },
  ];

  // دالة لإنشاء الأحرف الأولى من الاسم للصورة الرمزية
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .substring(0, 2)
      .toUpperCase();
  };

  // دالة لتنسيق التاريخ
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA');
  };

  return (
    <Card className="shadow-md mt-6">
      <CardHeader className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <FaUsers className="text-primary" />
          <h4 className="font-bold text-large">الموظفين الجدد</h4>
        </div>
        <Link href="/employees">
          <Button
            variant="light"
            color="primary"
            endContent={<FaArrowRight />}
            size="sm"
          >
            عرض الكل
          </Button>
        </Link>
      </CardHeader>
      <CardBody>
        <div className="flex flex-col gap-4">
          {employees.map((employee) => (
            <div
              key={employee.id}
              className="flex items-center justify-between border-b pb-3"
            >
              <div className="flex items-center gap-3">
                <Avatar
                  name={getInitials(employee.fullName)}
                  color="primary"
                  size="sm"
                />
                <div>
                  <p className="font-semibold">{employee.fullName}</p>
                  <p className="text-xs text-gray-500">{employee.jobTitle}</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-sm">{employee.workLocation}</p>
                <p className="text-xs text-gray-500">
                  تاريخ التعيين: {formatDate(employee.hireDate)}
                </p>
              </div>
            </div>
          ))}
        </div>
      </CardBody>
    </Card>
  );
}
