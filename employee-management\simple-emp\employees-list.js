// انتظر حتى يتم تحميل المستند بالكامل
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة صفحة عرض الموظفين
    initEmployeesListPage();

    // إضافة أزرار الحذف الجماعي
    addBulkDeleteButtons();
});

// تهيئة صفحة عرض الموظفين
function initEmployeesListPage() {
    // تحميل بيانات الموظفين
    loadEmployees();

    // تهيئة فلاتر البحث
    initFilters();

    // تهيئة أزرار التصدير والطباعة
    initExportAndPrint();

    // تهيئة ترقيم الصفحات
    initPagination();

    // تهيئة النوافذ المنبثقة
    initModals();
}

// تحميل بيانات الموظفين
function loadEmployees() {
    // في الإصدار النهائي، سيتم استبدال هذا بطلب API لجلب البيانات من الخادم
    // هنا نستخدم بيانات تجريبية للعرض

    // التحقق من وجود بيانات في التخزين المحلي
    let employees = localStorage.getItem('employees');
    console.log("بيانات الموظفين من التخزين المحلي:", employees);

    if (!employees) {
        // إذا لم تكن هناك بيانات، استخدم بيانات افتراضية
        const defaultEmployees = [
            {
                id: 1001,
                name: 'أحمد محمد علي حسين',
                jobDescription: 'teaching',
                jobTitle: 'مدرس',
                workLocation: 'كلية الهندسة',
                education: 'دكتوراه',
                birthDate: '1980-05-15',
                hireDate: '2010-09-01',
                currentDegree: 5,
                currentStage: 3,
                currentSalary: 850000,
                lastPromotionDate: '2018-09-01',
                nextPromotionDate: '2023-09-01',
                retirementDate: '2040-05-15',
                status: 'active'
            },
            {
                id: 1002,
                name: 'فاطمة علي حسن محمود',
                jobDescription: 'teaching',
                jobTitle: 'أستاذ مساعد',
                workLocation: 'كلية العلوم',
                education: 'دكتوراه',
                birthDate: '1975-08-20',
                hireDate: '2005-10-15',
                currentDegree: 3,
                currentStage: 4,
                currentSalary: 1250000,
                lastPromotionDate: '2015-10-15',
                nextPromotionDate: '2020-10-15',
                retirementDate: '2035-08-20',
                status: 'active'
            },
            {
                id: 1003,
                name: 'محمد جاسم محمود كريم',
                jobDescription: 'technical',
                jobTitle: 'مهندس',
                workLocation: 'كلية الهندسة',
                education: 'بكالوريوس',
                birthDate: '1985-03-10',
                hireDate: '2015-02-01',
                currentDegree: 7,
                currentStage: 2,
                currentSalary: 550000,
                lastPromotionDate: '2019-02-01',
                nextPromotionDate: '2023-02-01',
                retirementDate: '2045-03-10',
                status: 'active'
            },
            {
                id: 1004,
                name: 'زينب خالد عبد الله محمد',
                jobDescription: 'administrative',
                jobTitle: 'رئيس قسم',
                workLocation: 'رئاسة الجامعة',
                education: 'ماجستير',
                birthDate: '1978-11-25',
                hireDate: '2008-07-01',
                currentDegree: 6,
                currentStage: 3,
                currentSalary: 700000,
                lastPromotionDate: '2016-07-01',
                nextPromotionDate: '2020-07-01',
                retirementDate: '2038-11-25',
                status: 'active'
            },
            {
                id: 1005,
                name: 'علي حسين جاسم محمد',
                jobDescription: 'teaching',
                jobTitle: 'أستاذ',
                workLocation: 'كلية الطب',
                education: 'دكتوراه',
                birthDate: '1965-04-05',
                hireDate: '1995-09-01',
                currentDegree: 1,
                currentStage: 6,
                currentSalary: 1750000,
                lastPromotionDate: '2015-09-01',
                nextPromotionDate: '2020-09-01',
                retirementDate: '2025-04-05',
                status: 'active'
            }
        ];

        // حفظ البيانات في التخزين المحلي
        localStorage.setItem('employees', JSON.stringify(defaultEmployees));
        employees = defaultEmployees;
        console.log("تم تخزين البيانات الافتراضية:", defaultEmployees);
    } else {
        // تحويل البيانات من JSON إلى كائن JavaScript
        employees = JSON.parse(employees);
        console.log("تم تحميل البيانات من التخزين المحلي:", employees);
    }

    // تحميل مواقع العمل للفلتر
    loadWorkLocationsFilter();

    // عرض البيانات في الجدول
    displayEmployees(employees);
}

// تحميل مواقع العمل للفلتر
function loadWorkLocationsFilter() {
    const filterWorkLocation = document.getElementById('filterWorkLocation');
    if (!filterWorkLocation) return;

    // الحصول على مواقع العمل من التخزين المحلي
    const workLocations = JSON.parse(localStorage.getItem('workLocations') || '[]');

    // إضافة الخيارات إلى القائمة المنسدلة
    workLocations.forEach(location => {
        const option = document.createElement('option');
        option.value = location.name;
        option.textContent = location.name;
        filterWorkLocation.appendChild(option);
    });
}

// عرض الموظفين في الجدول
function displayEmployees(employees) {
    const tableBody = document.querySelector('#employeesTable tbody');
    if (!tableBody) return;

    // مسح محتوى الجدول
    tableBody.innerHTML = '';

    // إذا لم تكن هناك موظفين، عرض رسالة
    if (employees.length === 0) {
        const emptyRow = document.createElement('tr');
        emptyRow.innerHTML = `<td colspan="9" class="text-center">لا يوجد موظفين. أضف موظف جديد.</td>`;
        tableBody.appendChild(emptyRow);
        return;
    }

    // إضافة الصفوف إلى الجدول
    employees.forEach(employee => {
        const row = document.createElement('tr');

        // تحويل الوصف الوظيفي إلى نص عربي
        let jobDescriptionText = '';
        switch (employee.jobDescription) {
            case 'teaching':
                jobDescriptionText = 'تدريسي';
                break;
            case 'technical':
                jobDescriptionText = 'فني';
                break;
            case 'administrative':
                jobDescriptionText = 'اداري';
                break;
            default:
                jobDescriptionText = employee.jobDescription;
        }

        // التحقق مما إذا كان تاريخ الترفيع القادم قد حان أو اقترب
        const nextPromotionDate = new Date(employee.nextPromotionDate);
        const today = new Date();
        const diffTime = nextPromotionDate - today;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        let promotionClass = '';
        if (diffDays <= 0) {
            promotionClass = 'promotion-due';
        } else if (diffDays <= 30) {
            promotionClass = 'promotion-upcoming';
        }

        row.innerHTML = `
            <td>
                <input type="checkbox" class="employee-checkbox" data-id="${employee.id}" data-name="${employee.name}">
            </td>
            <td>${employee.id}</td>
            <td>${employee.name}</td>
            <td>${jobDescriptionText}</td>
            <td>${employee.jobTitle}</td>
            <td>${employee.workLocation}</td>
            <td>${employee.currentDegree}</td>
            <td>${employee.currentStage}</td>
            <td>${employee.seniority || '0'}</td>
            <td class="${promotionClass}">${formatDate(employee.nextPromotionDate)}</td>
            <td>
                <div class="action-buttons">
                    <button class="action-btn view-btn" data-id="${employee.id}" title="عرض">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="action-btn details-btn" data-id="${employee.id}" title="تفاصيل">
                        <i class="fas fa-info-circle"></i>
                    </button>
                    <button class="action-btn edit-btn" data-id="${employee.id}" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn delete-btn" data-id="${employee.id}" data-name="${employee.name}" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;
        tableBody.appendChild(row);
    });

    // إضافة مستمعي الأحداث لأزرار الإجراءات
    addActionButtonsEventListeners();
}

// إضافة مستمعي الأحداث لأزرار الإجراءات
function addActionButtonsEventListeners() {
    // أزرار العرض
    const viewButtons = document.querySelectorAll('.view-btn');
    viewButtons.forEach(button => {
        button.addEventListener('click', function() {
            const employeeId = this.getAttribute('data-id');
            openViewModal(employeeId);
        });
    });

    // أزرار التفاصيل
    const detailsButtons = document.querySelectorAll('.details-btn');
    detailsButtons.forEach(button => {
        button.addEventListener('click', function() {
            const employeeId = this.getAttribute('data-id');
            window.location.href = `employee-details.html?id=${employeeId}`;
        });
    });

    // أزرار التعديل
    const editButtons = document.querySelectorAll('.edit-btn');
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const employeeId = this.getAttribute('data-id');
            console.log("تم النقر على زر التعديل للموظف بالمعرف:", employeeId);

            // طباعة بيانات الموظف للتأكد من وجوده
            const employees = JSON.parse(localStorage.getItem('employees') || '[]');
            const employee = employees.find(emp => emp.id == employeeId);
            console.log("بيانات الموظف:", employee);

            // الانتقال إلى صفحة التعديل
            window.location.href = `employee-form.html?id=${employeeId}`;
        });
    });

    // أزرار الحذف
    const deleteButtons = document.querySelectorAll('.delete-btn');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const employeeId = this.getAttribute('data-id');
            const employeeName = this.getAttribute('data-name');
            openDeleteModal(employeeId, employeeName);
        });
    });
}

// تهيئة فلاتر البحث
function initFilters() {
    const searchInput = document.getElementById('searchEmployee');
    const filterJobDescription = document.getElementById('filterJobDescription');
    const filterWorkLocation = document.getElementById('filterWorkLocation');
    const filterStatus = document.getElementById('filterStatus');

    // إضافة مستمعي الأحداث للفلاتر
    if (searchInput) {
        searchInput.addEventListener('input', applyFilters);
    }

    if (filterJobDescription) {
        filterJobDescription.addEventListener('change', applyFilters);
    }

    if (filterWorkLocation) {
        filterWorkLocation.addEventListener('change', applyFilters);
    }

    if (filterStatus) {
        filterStatus.addEventListener('change', applyFilters);
    }
}

// تطبيق الفلاتر
function applyFilters() {
    const searchInput = document.getElementById('searchEmployee');
    const filterJobDescription = document.getElementById('filterJobDescription');
    const filterWorkLocation = document.getElementById('filterWorkLocation');
    const filterStatus = document.getElementById('filterStatus');

    if (!searchInput || !filterJobDescription || !filterWorkLocation || !filterStatus) return;

    const searchTerm = searchInput.value.trim().toLowerCase();
    const jobDescription = filterJobDescription.value;
    const workLocation = filterWorkLocation.value;
    const status = filterStatus.value;

    // الحصول على الموظفين
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');

    // تصفية الموظفين بناءً على الفلاتر
    const filteredEmployees = employees.filter(employee => {
        const matchesSearch = employee.name.toLowerCase().includes(searchTerm) ||
                             employee.id.toString().includes(searchTerm) ||
                             employee.jobTitle.toLowerCase().includes(searchTerm);

        const matchesJobDescription = !jobDescription || employee.jobDescription === jobDescription;
        const matchesWorkLocation = !workLocation || employee.workLocation === workLocation;
        const matchesStatus = !status || employee.status === status;

        return matchesSearch && matchesJobDescription && matchesWorkLocation && matchesStatus;
    });

    // عرض النتائج المصفاة
    displayEmployees(filteredEmployees);
}

// تهيئة أزرار التصدير والطباعة
function initExportAndPrint() {
    const exportBtn = document.getElementById('exportBtn');
    const printBtn = document.getElementById('printBtn');

    if (exportBtn) {
        exportBtn.addEventListener('click', exportEmployees);
    }

    if (printBtn) {
        printBtn.addEventListener('click', printEmployees);
    }
}

// تصدير بيانات الموظفين
function exportEmployees() {
    alert('سيتم تنفيذ وظيفة تصدير بيانات الموظفين في الإصدار النهائي.');
}

// طباعة بيانات الموظفين
function printEmployees() {
    window.print();
}

// تهيئة ترقيم الصفحات
function initPagination() {
    // في هذا المثال البسيط، لن نقوم بتنفيذ ترقيم الصفحات بشكل كامل
    // سيتم تنفيذه في الإصدار النهائي
}

// تهيئة النوافذ المنبثقة
function initModals() {
    // الحصول على عناصر النوافذ المنبثقة
    const viewModal = document.getElementById('viewEmployeeModal');
    const deleteModal = document.getElementById('deleteModal');
    const closeButtons = document.querySelectorAll('.close-modal');

    // أزرار الإغلاق
    closeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const modal = this.closest('.modal');
            closeModal(modal);
        });
    });

    // إغلاق النافذة عند النقر خارجها
    window.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal')) {
            closeModal(e.target);
        }
    });

    // زر إغلاق نافذة العرض
    const closeViewBtn = document.getElementById('closeViewBtn');
    if (closeViewBtn) {
        closeViewBtn.addEventListener('click', function() {
            closeModal(viewModal);
        });
    }

    // زر إلغاء الحذف
    const cancelDeleteBtn = document.getElementById('cancelDeleteBtn');
    if (cancelDeleteBtn) {
        cancelDeleteBtn.addEventListener('click', function() {
            closeModal(deleteModal);
        });
    }

    // زر تأكيد الحذف
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    if (confirmDeleteBtn) {
        confirmDeleteBtn.addEventListener('click', function() {
            const employeeId = this.getAttribute('data-id');
            deleteEmployee(employeeId);
            closeModal(deleteModal);
        });
    }
}

// فتح نافذة عرض تفاصيل الموظف
function openViewModal(employeeId) {
    // الحصول على بيانات الموظف
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');
    const employee = employees.find(emp => emp.id == employeeId);

    if (!employee) return;

    // ملء بيانات الموظف في النافذة
    document.getElementById('viewEmployeeName').textContent = employee.name;
    document.getElementById('viewEmployeeTitle').textContent = employee.jobTitle;
    document.getElementById('viewEmployeeId').textContent = `رقم الموظف: ${employee.id}`;

    // تحويل الوصف الوظيفي إلى نص عربي
    let jobDescriptionText = '';
    switch (employee.jobDescription) {
        case 'teaching':
            jobDescriptionText = 'تدريسي';
            break;
        case 'technical':
            jobDescriptionText = 'فني';
            break;
        case 'administrative':
            jobDescriptionText = 'اداري';
            break;
        default:
            jobDescriptionText = employee.jobDescription;
    }

    document.getElementById('viewJobDescription').textContent = jobDescriptionText;
    document.getElementById('viewWorkLocation').textContent = employee.workLocation;
    document.getElementById('viewEducation').textContent = employee.education;
    document.getElementById('viewBirthDate').textContent = formatDate(employee.birthDate);
    document.getElementById('viewHireDate').textContent = formatDate(employee.hireDate);
    document.getElementById('viewCurrentDegree').textContent = employee.currentDegree;
    document.getElementById('viewCurrentStage').textContent = employee.currentStage;
    document.getElementById('viewSeniority').textContent = employee.seniority || '0';
    document.getElementById('viewCurrentSalary').textContent = employee.currentSalary;
    document.getElementById('viewLastPromotionDate').textContent = formatDate(employee.lastPromotionDate);
    document.getElementById('viewNextPromotionDate').textContent = formatDate(employee.nextPromotionDate);
    document.getElementById('viewRetirementDate').textContent = formatDate(employee.retirementDate);

    // تعيين رابط التعديل
    document.getElementById('editEmployeeBtn').href = `employee-form.html?id=${employee.id}`;

    // فتح النافذة
    const viewModal = document.getElementById('viewEmployeeModal');
    openModal(viewModal);
}

// فتح نافذة تأكيد الحذف
function openDeleteModal(employeeId, employeeName) {
    // عرض اسم الموظف في رسالة التأكيد
    document.getElementById('deleteEmployeeName').textContent = employeeName;

    // تعيين معرف الموظف لزر التأكيد
    document.getElementById('confirmDeleteBtn').setAttribute('data-id', employeeId);

    // فتح النافذة
    const deleteModal = document.getElementById('deleteModal');
    openModal(deleteModal);
}

// حذف موظف
function deleteEmployee(employeeId) {
    try {
        // الحصول على الموظفين الحاليين
        let employees = JSON.parse(localStorage.getItem('employees') || '[]');

        // البحث عن الموظف قبل حذفه للتأكد من وجوده
        const employeeToDelete = employees.find(emp => emp.id == employeeId);
        if (!employeeToDelete) {
            showNotification('لم يتم العثور على الموظف', 'error');
            return;
        }

        // حذف البيانات المرتبطة بالموظف

        // 1. حذف التشكرات المرتبطة بالموظف
        let thanks = JSON.parse(localStorage.getItem('thanks') || '[]');
        thanks = thanks.filter(thank => thank.employeeId != employeeId);
        localStorage.setItem('thanks', JSON.stringify(thanks));

        // 2. حذف العقوبات المرتبطة بالموظف
        let penalties = JSON.parse(localStorage.getItem('penalties') || '[]');
        penalties = penalties.filter(penalty => penalty.employeeId != employeeId);
        localStorage.setItem('penalties', JSON.stringify(penalties));

        // 3. حذف الإجازات المرتبطة بالموظف
        let leaves = JSON.parse(localStorage.getItem('employeeLeaves') || '[]');
        leaves = leaves.filter(leave => leave.employeeId != employeeId);
        localStorage.setItem('employeeLeaves', JSON.stringify(leaves));

        // 4. حذف الغيابات المرتبطة بالموظف
        let absences = JSON.parse(localStorage.getItem('absences') || '[]');
        absences = absences.filter(absence => absence.employeeId != employeeId);
        localStorage.setItem('absences', JSON.stringify(absences));

        // حذف الموظف
        employees = employees.filter(emp => emp.id != employeeId);

        // حفظ البيانات المحدثة في التخزين المحلي
        localStorage.setItem('employees', JSON.stringify(employees));

        // تحديث عرض الجدول
        displayEmployees(employees);

        // عرض رسالة نجاح
        showNotification('تم حذف الموظف وجميع البيانات المرتبطة به بنجاح', 'success');
    } catch (error) {
        console.error('خطأ في حذف الموظف:', error);
        showNotification('حدث خطأ أثناء حذف الموظف', 'error');
    }
}

// فتح نافذة منبثقة
function openModal(modal) {
    if (modal) {
        modal.style.display = 'block';
    }
}

// إغلاق نافذة منبثقة
function closeModal(modal) {
    if (modal) {
        modal.style.display = 'none';
    }
}

// تنسيق التاريخ (ميلادي فقط - إجباري)
function formatDate(dateString) {
    if (!dateString) return '-';

    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;

    // إجبار استخدام التقويم الميلادي مع تنسيق يدوي لضمان عدم ظهور التاريخ الهجري
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();

    const monthNames = [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];

    return `${day} ${monthNames[month - 1]} ${year}`;
}

// عرض إشعار
function showNotification(message, type = 'info') {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;

    // تحديد الأيقونة المناسبة حسب نوع الإشعار
    let icon = 'fa-info-circle';
    if (type === 'success') {
        icon = 'fa-check-circle';
    } else if (type === 'error') {
        icon = 'fa-exclamation-circle';
    }

    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas ${icon}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close"><i class="fas fa-times"></i></button>
    `;

    // إضافة الإشعار إلى الصفحة
    document.body.appendChild(notification);

    // إضافة مستمع حدث لزر الإغلاق
    const closeButton = notification.querySelector('.notification-close');
    closeButton.addEventListener('click', function() {
        notification.remove();
    });

    // إزالة الإشعار تلقائيًا بعد 5 ثوانٍ
    setTimeout(() => {
        notification.classList.add('fade-out');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 5000);
}

// إضافة أزرار الحذف الجماعي
function addBulkDeleteButtons() {
    const actionsDiv = document.createElement('div');
    actionsDiv.className = 'bulk-actions';
    actionsDiv.innerHTML = `
        <div class="select-all-container">
            <input type="checkbox" id="selectAll">
            <label for="selectAll">تحديد الكل</label>
        </div>
        <button id="deleteSelected" class="btn btn-danger" disabled>
            <i class="fas fa-trash"></i> حذف المحدد
        </button>
    `;

    // إضافة الأزرار قبل الجدول
    const table = document.querySelector('#employeesTable');
    table.parentNode.insertBefore(actionsDiv, table);

    // إضافة مستمعي الأحداث
    const selectAllCheckbox = document.getElementById('selectAll');
    const deleteSelectedButton = document.getElementById('deleteSelected');

    // تحديد الكل
    selectAllCheckbox.addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.employee-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateDeleteButtonState();
    });

    // تحديث حالة زر الحذف
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('employee-checkbox')) {
            updateDeleteButtonState();
        }
    });

    // حذف المحدد
    deleteSelectedButton.addEventListener('click', function() {
        const selectedCheckboxes = document.querySelectorAll('.employee-checkbox:checked');
        const selectedCount = selectedCheckboxes.length;

        if (selectedCount === 0) return;

        const confirmMessage = selectedCount === 1 
            ? 'هل أنت متأكد من حذف الموظف المحدد؟'
            : `هل أنت متأكد من حذف ${selectedCount} موظفين؟`;

        if (confirm(confirmMessage)) {
            const selectedIds = Array.from(selectedCheckboxes).map(cb => cb.dataset.id);
            bulkDeleteEmployees(selectedIds);
        }
    });
}

// تحديث حالة زر الحذف
function updateDeleteButtonState() {
    const deleteButton = document.getElementById('deleteSelected');
    const selectedCount = document.querySelectorAll('.employee-checkbox:checked').length;
    deleteButton.disabled = selectedCount === 0;
}

// حذف مجموعة من الموظفين
function bulkDeleteEmployees(employeeIds) {
    try {
        // الحصول على الموظفين الحاليين
        let employees = JSON.parse(localStorage.getItem('employees') || '[]');
        let deletedCount = 0;

        // حذف كل موظف وبياناته المرتبطة
        employeeIds.forEach(id => {
            // التحقق من وجود الموظف
            const employeeToDelete = employees.find(emp => emp.id == id);
            if (employeeToDelete) {
                // حذف البيانات المرتبطة
                ['thanks', 'penalties', 'employeeLeaves', 'absences'].forEach(key => {
                    let items = JSON.parse(localStorage.getItem(key) || '[]');
                    items = items.filter(item => item.employeeId != id);
                    localStorage.setItem(key, JSON.stringify(items));
                });

                deletedCount++;
            }
        });

        // حذف الموظفين من المصفوفة الرئيسية
        employees = employees.filter(emp => !employeeIds.includes(emp.id.toString()));
        localStorage.setItem('employees', JSON.stringify(employees));

        // تحديث عرض الجدول
        displayEmployees(employees);

        // إعادة تعيين حالة تحديد الكل
        const selectAllCheckbox = document.getElementById('selectAll');
        if (selectAllCheckbox) selectAllCheckbox.checked = false;

        // عرض رسالة نجاح
        showNotification(`تم حذف ${deletedCount} موظف/موظفين بنجاح`, 'success');
    } catch (error) {
        console.error('خطأ في حذف الموظفين:', error);
        showNotification('حدث خطأ أثناء حذف الموظفين', 'error');
    }
}
