/**
 * ملف لتشخيص مشاكل كتب الشكر
 */

// إضافة زر التشخيص إلى صفحة التشكرات
document.addEventListener('DOMContentLoaded', function() {
    addDebugButton();
});

// إضافة زر التشخيص
function addDebugButton() {
    const actionsContainer = document.querySelector('.list-actions');
    if (actionsContainer) {
        // إنشاء زر التشخيص
        const debugButton = document.createElement('button');
        debugButton.className = 'btn btn-warning';
        debugButton.innerHTML = '<i class="fas fa-bug"></i> تشخيص كتب الشكر';
        debugButton.addEventListener('click', runDiagnostics);
        
        // إضافة الزر إلى حاوية الإجراءات
        actionsContainer.appendChild(debugButton);
    }
}

// تشغيل تشخيص كتب الشكر
function runDiagnostics() {
    // إنشاء نافذة التشخيص
    const diagnosticsModal = createDiagnosticsModal();
    
    // عرض النافذة
    openModal(diagnosticsModal);
    
    // الحصول على بيانات الموظفين والتشكرات
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');
    const thanks = JSON.parse(localStorage.getItem('thanks') || '[]');
    
    // إنشاء تقرير التشخيص
    const diagnosticsReport = document.getElementById('diagnosticsReport');
    if (!diagnosticsReport) return;
    
    // مسح التقرير السابق
    diagnosticsReport.innerHTML = '';
    
    // إضافة معلومات عامة
    addReportSection(diagnosticsReport, 'معلومات عامة', [
        `عدد الموظفين: ${employees.length}`,
        `عدد كتب الشكر: ${thanks.length}`
    ]);
    
    // فحص بيانات الموظفين
    const employeeIssues = [];
    employees.forEach(employee => {
        const issues = [];
        
        // التحقق من وجود تاريخ الاستحقاق الحالي
        if (!employee.currentDueDate) {
            issues.push(`لا يوجد تاريخ استحقاق حالي`);
        }
        
        // التحقق من وجود تاريخ الاستحقاق الجديد
        if (!employee.newDueDate) {
            issues.push(`لا يوجد تاريخ استحقاق جديد`);
        }
        
        // إذا كان هناك مشاكل، أضفها إلى قائمة المشاكل
        if (issues.length > 0) {
            employeeIssues.push({
                name: employee.name || 'بدون اسم',
                id: employee.id,
                issues: issues
            });
        }
    });
    
    // إضافة قسم مشاكل الموظفين
    if (employeeIssues.length > 0) {
        const issuesContent = employeeIssues.map(issue => 
            `<div class="issue-item">
                <strong>${issue.name} (${issue.id})</strong>
                <ul>${issue.issues.map(i => `<li>${i}</li>`).join('')}</ul>
            </div>`
        ).join('');
        
        addReportSection(diagnosticsReport, 'مشاكل بيانات الموظفين', [issuesContent]);
    } else {
        addReportSection(diagnosticsReport, 'مشاكل بيانات الموظفين', ['لا توجد مشاكل في بيانات الموظفين']);
    }
    
    // فحص كتب الشكر
    const thanksIssues = [];
    thanks.forEach(thank => {
        const issues = [];
        
        // البحث عن الموظف
        const employee = employees.find(emp => emp.id == thank.employeeId);
        
        if (!employee) {
            issues.push(`الموظف غير موجود (معرف: ${thank.employeeId})`);
        } else {
            // التحقق من تواريخ الاستحقاق
            if (!employee.currentDueDate || !employee.newDueDate) {
                issues.push(`الموظف ليس لديه تواريخ استحقاق كاملة`);
            } else {
                // التحقق من تاريخ كتاب الشكر
                const currentDueDate = new Date(employee.currentDueDate);
                const newDueDate = new Date(employee.newDueDate);
                const thankDate = new Date(thank.date);
                
                if (thankDate <= currentDueDate) {
                    issues.push(`تاريخ كتاب الشكر (${thank.date}) قبل تاريخ الاستحقاق الحالي (${employee.currentDueDate})`);
                } else if (thankDate >= newDueDate) {
                    issues.push(`تاريخ كتاب الشكر (${thank.date}) بعد تاريخ الاستحقاق الجديد (${employee.newDueDate})`);
                }
            }
        }
        
        // إذا كان هناك مشاكل، أضفها إلى قائمة المشاكل
        if (issues.length > 0) {
            thanksIssues.push({
                id: thank.id,
                employeeName: employee ? employee.name : 'غير معروف',
                date: thank.date,
                effect: getEffectText(thank.effect),
                issues: issues
            });
        }
    });
    
    // إضافة قسم مشاكل كتب الشكر
    if (thanksIssues.length > 0) {
        const issuesContent = thanksIssues.map(issue => 
            `<div class="issue-item">
                <strong>كتاب شكر #${issue.id} - ${issue.employeeName} - ${issue.date}</strong>
                <p>التأثير: ${issue.effect}</p>
                <ul>${issue.issues.map(i => `<li>${i}</li>`).join('')}</ul>
            </div>`
        ).join('');
        
        addReportSection(diagnosticsReport, 'مشاكل كتب الشكر', [issuesContent]);
    } else {
        addReportSection(diagnosticsReport, 'مشاكل كتب الشكر', ['لا توجد مشاكل في كتب الشكر']);
    }
    
    // إضافة قسم الحلول المقترحة
    addReportSection(diagnosticsReport, 'الحلول المقترحة', [
        '1. تأكد من إدخال تاريخ الاستحقاق الحالي لجميع الموظفين',
        '2. تأكد من حساب تاريخ الاستحقاق الجديد بشكل صحيح',
        '3. تأكد من أن تاريخ كتاب الشكر يقع بين تاريخ الاستحقاق الحالي وتاريخ الاستحقاق الجديد',
        '4. يمكنك استخدام زر "إصلاح تلقائي" أدناه لمحاولة إصلاح المشاكل تلقائيًا'
    ]);
    
    // إضافة زر الإصلاح التلقائي
    const fixButton = document.createElement('button');
    fixButton.className = 'btn btn-primary';
    fixButton.innerHTML = '<i class="fas fa-wrench"></i> إصلاح تلقائي';
    fixButton.addEventListener('click', autoFix);
    
    const buttonContainer = document.createElement('div');
    buttonContainer.className = 'text-center mt-3';
    buttonContainer.appendChild(fixButton);
    
    diagnosticsReport.appendChild(buttonContainer);
}

// إنشاء نافذة التشخيص
function createDiagnosticsModal() {
    // التحقق من وجود النافذة مسبقاً
    let modal = document.getElementById('diagnosticsModal');
    if (modal) return modal;
    
    // إنشاء عنصر النافذة
    modal = document.createElement('div');
    modal.className = 'modal';
    modal.id = 'diagnosticsModal';
    
    // إضافة محتوى النافذة
    modal.innerHTML = `
        <div class="modal-content" style="width: 80%; max-width: 800px;">
            <div class="modal-header">
                <h2>تشخيص كتب الشكر</h2>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <div id="diagnosticsReport" class="diagnostics-report">
                    <!-- سيتم ملء هذا الجزء ديناميكياً -->
                </div>
            </div>
            <div class="modal-footer">
                <button id="closeDiagnosticsBtn" class="btn btn-secondary"><i class="fas fa-times"></i> إغلاق</button>
            </div>
        </div>
    `;
    
    // إضافة النافذة إلى الصفحة
    document.body.appendChild(modal);
    
    // إضافة مستمعي الأحداث للنافذة
    const closeButton = modal.querySelector('.close-modal');
    const closeDiagnosticsBtn = modal.querySelector('#closeDiagnosticsBtn');
    
    closeButton.addEventListener('click', function() {
        closeModal(modal);
    });
    
    closeDiagnosticsBtn.addEventListener('click', function() {
        closeModal(modal);
    });
    
    // إغلاق النافذة عند النقر خارجها
    window.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeModal(modal);
        }
    });
    
    // إضافة أنماط CSS للتقرير
    const style = document.createElement('style');
    style.textContent = `
        .diagnostics-report {
            max-height: 500px;
            overflow-y: auto;
            padding: 10px;
        }
        .report-section {
            margin-bottom: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
        }
        .report-section-title {
            background-color: #f5f5f5;
            padding: 10px;
            font-weight: bold;
            border-bottom: 1px solid #ddd;
        }
        .report-section-content {
            padding: 10px;
        }
        .issue-item {
            margin-bottom: 10px;
            padding: 10px;
            background-color: #fff9f9;
            border-left: 3px solid #f44336;
            border-radius: 3px;
        }
    `;
    document.head.appendChild(style);
    
    return modal;
}

// إضافة قسم إلى تقرير التشخيص
function addReportSection(container, title, contentItems) {
    const section = document.createElement('div');
    section.className = 'report-section';
    
    const sectionTitle = document.createElement('div');
    sectionTitle.className = 'report-section-title';
    sectionTitle.textContent = title;
    
    const sectionContent = document.createElement('div');
    sectionContent.className = 'report-section-content';
    
    contentItems.forEach(item => {
        const paragraph = document.createElement('div');
        paragraph.innerHTML = item;
        sectionContent.appendChild(paragraph);
    });
    
    section.appendChild(sectionTitle);
    section.appendChild(sectionContent);
    container.appendChild(section);
}

// الإصلاح التلقائي للمشاكل
function autoFix() {
    // الحصول على بيانات الموظفين
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');
    let fixed = 0;
    
    // إصلاح تواريخ الاستحقاق للموظفين
    employees.forEach((employee, index) => {
        let updated = false;
        
        // إذا كان تاريخ التعيين موجودًا ولكن تاريخ الاستحقاق الحالي غير موجود
        if (employee.hireDate && !employee.currentDueDate) {
            employee.currentDueDate = employee.hireDate;
            updated = true;
        }
        
        // إذا كان تاريخ الاستحقاق الحالي موجودًا ولكن تاريخ الاستحقاق الجديد غير موجود
        if (employee.currentDueDate && !employee.newDueDate) {
            const currentDueDate = new Date(employee.currentDueDate);
            const newDueDate = new Date(currentDueDate);
            newDueDate.setFullYear(newDueDate.getFullYear() + 1);
            employee.newDueDate = newDueDate.toISOString().split('T')[0];
            updated = true;
        }
        
        if (updated) {
            fixed++;
        }
    });
    
    // حفظ البيانات المحدثة
    localStorage.setItem('employees', JSON.stringify(employees));
    
    // إعادة تشغيل التشخيص
    runDiagnostics();
    
    // عرض رسالة نجاح
    showNotification(`تم إصلاح ${fixed} موظف بنجاح`, 'success');
}
