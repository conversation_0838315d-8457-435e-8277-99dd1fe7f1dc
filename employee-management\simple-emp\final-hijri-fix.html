<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الحل النهائي للتواريخ الهجرية - نظام إدارة العلاوات والترفيعات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #dc2626 0%, #991b1b 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #dc2626;
        }

        .header h1 {
            color: #dc2626;
            margin-bottom: 10px;
            font-size: 2.5em;
        }

        .critical {
            background: #fef2f2;
            border: 3px solid #dc2626;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 30px;
            text-align: center;
        }

        .critical h3 {
            color: #dc2626;
            margin-bottom: 15px;
            font-size: 1.5em;
        }

        .critical p {
            color: #991b1b;
            font-weight: 600;
            font-size: 1.1em;
        }

        .fix-btn {
            background: linear-gradient(135deg, #dc2626 0%, #991b1b 100%);
            color: white;
            border: none;
            padding: 20px 40px;
            border-radius: 10px;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
            font-size: 18px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .fix-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.3);
        }

        .result {
            margin-top: 20px;
            padding: 20px;
            border-radius: 10px;
            background: white;
            border-left: 5px solid #dc2626;
            font-size: 16px;
        }

        .success {
            border-left-color: #10b981;
            background: #f0fdf4;
            color: #065f46;
        }

        .error {
            border-left-color: #ef4444;
            background: #fef2f2;
            color: #991b1b;
        }

        .progress {
            background: #e5e7eb;
            border-radius: 15px;
            overflow: hidden;
            margin: 20px 0;
            height: 30px;
        }

        .progress-bar {
            background: linear-gradient(135deg, #dc2626 0%, #991b1b 100%);
            height: 100%;
            transition: width 0.5s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 14px;
        }

        .step-list {
            background: #f9f9f9;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .step {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
        }

        .step.completed {
            border-left: 5px solid #10b981;
            background: #f0fdf4;
        }

        .step.failed {
            border-left: 5px solid #ef4444;
            background: #fef2f2;
        }

        .step-status {
            font-weight: bold;
            font-size: 16px;
        }

        .completed .step-status {
            color: #10b981;
        }

        .failed .step-status {
            color: #ef4444;
        }

        .reload-btn {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
            font-size: 16px;
            font-weight: 600;
        }

        .reload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-exclamation-triangle"></i> الحل النهائي للتواريخ الهجرية</h1>
            <p>إصلاح جذري ونهائي لمشكلة التواريخ الهجرية في النظام</p>
        </div>

        <div class="critical">
            <h3><i class="fas fa-fire"></i> مشكلة حرجة تتطلب حل فوري!</h3>
            <p>تم اكتشاف تواريخ هجرية في النظام - سيتم حلها نهائياً الآن</p>
        </div>

        <!-- الحل النهائي -->
        <div style="text-align: center; margin: 30px 0;">
            <button class="fix-btn" onclick="executeUltimateFix()">
                <i class="fas fa-magic"></i> تنفيذ الحل النهائي
            </button>
        </div>

        <!-- شريط التقدم -->
        <div id="progressContainer" style="display: none;">
            <div class="progress">
                <div class="progress-bar" id="progressBar" style="width: 0%">0%</div>
            </div>
        </div>

        <!-- قائمة الخطوات -->
        <div id="stepsList" class="step-list" style="display: none;"></div>

        <!-- النتيجة -->
        <div id="finalResult" class="result" style="display: none;"></div>

        <!-- إعادة تحميل النظام -->
        <div id="reloadSection" style="display: none; text-align: center; margin-top: 30px;">
            <button class="reload-btn" onclick="reloadSystem()">
                <i class="fas fa-sync-alt"></i> إعادة تحميل النظام
            </button>
        </div>
    </div>

    <script>
        const ultimateSteps = [
            { name: 'مسح جميع البيانات الهجرية', completed: false },
            { name: 'إعادة تعيين وظائف التنسيق', completed: false },
            { name: 'تنظيف التخزين المحلي', completed: false },
            { name: 'إعادة تنسيق جميع التواريخ', completed: false },
            { name: 'حفظ البيانات المحدثة', completed: false },
            { name: 'التحقق النهائي', completed: false },
            { name: 'تطبيق الحماية الدائمة', completed: false }
        ];

        // تنفيذ الحل النهائي
        async function executeUltimateFix() {
            const progressContainer = document.getElementById('progressContainer');
            const stepsList = document.getElementById('stepsList');
            const finalResult = document.getElementById('finalResult');
            const reloadSection = document.getElementById('reloadSection');
            
            // إظهار شريط التقدم والخطوات
            progressContainer.style.display = 'block';
            stepsList.style.display = 'block';
            finalResult.style.display = 'none';
            reloadSection.style.display = 'none';
            
            // إعادة تعيين الخطوات
            ultimateSteps.forEach(step => {
                step.completed = false;
                step.failed = false;
            });
            
            updateStepsDisplay();
            
            // تنفيذ الخطوات
            for (let i = 0; i < ultimateSteps.length; i++) {
                try {
                    await executeUltimateStep(i);
                    ultimateSteps[i].completed = true;
                } catch (error) {
                    ultimateSteps[i].failed = true;
                    console.error(`فشل في الخطوة ${i}:`, error);
                }
                
                updateProgress((i + 1) / ultimateSteps.length * 100);
                updateStepsDisplay();
                await sleep(800); // توقف أطول للتأكيد
            }
            
            showUltimateFinalResult();
        }

        // تنفيذ خطوة من الحل النهائي
        async function executeUltimateStep(stepIndex) {
            switch (stepIndex) {
                case 0:
                    await clearAllHijriData();
                    break;
                case 1:
                    await resetFormatFunctions();
                    break;
                case 2:
                    await cleanLocalStorage();
                    break;
                case 3:
                    await reformatAllDatesUltimate();
                    break;
                case 4:
                    await saveUpdatedData();
                    break;
                case 5:
                    await ultimateVerification();
                    break;
                case 6:
                    await applyPermanentProtection();
                    break;
            }
        }

        // مسح جميع البيانات الهجرية
        async function clearAllHijriData() {
            let employees = JSON.parse(localStorage.getItem('employees') || '[]');
            let thanks = JSON.parse(localStorage.getItem('thanks') || '[]');
            
            // مسح أي تواريخ تحتوي على كلمات هجرية
            const hijriWords = ['هـ', 'محرم', 'صفر', 'ربيع', 'جمادى', 'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'];
            
            employees.forEach(emp => {
                const dateFields = ['birthDate', 'hireDate', 'lastPromotionDate', 'nextPromotionDate', 'currentDueDate', 'newDueDate', 'retirementDate'];
                dateFields.forEach(field => {
                    if (emp[field]) {
                        const hasHijri = hijriWords.some(word => emp[field].includes(word));
                        if (hasHijri) {
                            emp[field] = new Date().toISOString().split('T')[0];
                        }
                    }
                });
            });
            
            thanks.forEach(thank => {
                if (thank.date) {
                    const hasHijri = hijriWords.some(word => thank.date.includes(word));
                    if (hasHijri) {
                        thank.date = new Date().toISOString().split('T')[0];
                    }
                }
            });
            
            localStorage.setItem('employees', JSON.stringify(employees));
            localStorage.setItem('thanks', JSON.stringify(thanks));
        }

        // إعادة تعيين وظائف التنسيق
        async function resetFormatFunctions() {
            // إنشاء وظيفة تنسيق عالمية محصنة
            window.formatDate = function(dateString) {
                if (!dateString) return '-';
                
                const date = new Date(dateString);
                if (isNaN(date.getTime())) return dateString;
                
                const year = date.getFullYear();
                const month = date.getMonth() + 1;
                const day = date.getDate();
                
                const monthNames = [
                    'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
                ];
                
                return `${day} ${monthNames[month - 1]} ${year}`;
            };
            
            // حماية الوظيفة من التعديل
            Object.freeze(window.formatDate);
        }

        // تنظيف التخزين المحلي
        async function cleanLocalStorage() {
            // إزالة أي بيانات قديمة قد تحتوي على تواريخ هجرية
            const keysToClean = ['hijri_dates', 'old_dates', 'temp_dates'];
            keysToClean.forEach(key => {
                localStorage.removeItem(key);
            });
        }

        // إعادة تنسيق جميع التواريخ (نهائي)
        async function reformatAllDatesUltimate() {
            let employees = JSON.parse(localStorage.getItem('employees') || '[]');
            let thanks = JSON.parse(localStorage.getItem('thanks') || '[]');
            
            // التأكد من أن جميع التواريخ بتنسيق ISO صحيح
            employees.forEach(emp => {
                const dateFields = ['birthDate', 'hireDate', 'lastPromotionDate', 'nextPromotionDate', 'currentDueDate', 'newDueDate', 'retirementDate'];
                dateFields.forEach(field => {
                    if (emp[field] && !emp[field].match(/^\d{4}-\d{2}-\d{2}$/)) {
                        const date = new Date(emp[field]);
                        if (!isNaN(date.getTime())) {
                            emp[field] = date.toISOString().split('T')[0];
                        } else {
                            emp[field] = new Date().toISOString().split('T')[0];
                        }
                    }
                });
            });
            
            thanks.forEach(thank => {
                if (thank.date && !thank.date.match(/^\d{4}-\d{2}-\d{2}$/)) {
                    const date = new Date(thank.date);
                    if (!isNaN(date.getTime())) {
                        thank.date = date.toISOString().split('T')[0];
                    } else {
                        thank.date = new Date().toISOString().split('T')[0];
                    }
                }
            });
            
            localStorage.setItem('employees', JSON.stringify(employees));
            localStorage.setItem('thanks', JSON.stringify(thanks));
        }

        // حفظ البيانات المحدثة
        async function saveUpdatedData() {
            const employees = JSON.parse(localStorage.getItem('employees') || '[]');
            const thanks = JSON.parse(localStorage.getItem('thanks') || '[]');
            
            // إنشاء نسخة احتياطية نهائية
            localStorage.setItem('employees_final_backup', JSON.stringify(employees));
            localStorage.setItem('thanks_final_backup', JSON.stringify(thanks));
            
            // وضع علامة على أن النظام تم إصلاحه
            localStorage.setItem('hijri_fix_completed', 'true');
            localStorage.setItem('hijri_fix_date', new Date().toISOString());
        }

        // التحقق النهائي
        async function ultimateVerification() {
            const employees = JSON.parse(localStorage.getItem('employees') || '[]');
            const thanks = JSON.parse(localStorage.getItem('thanks') || '[]');
            
            let issues = 0;
            const hijriWords = ['هـ', 'محرم', 'صفر', 'ربيع', 'جمادى', 'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'];
            
            // فحص شامل
            employees.forEach(emp => {
                const dateFields = ['birthDate', 'hireDate', 'lastPromotionDate', 'nextPromotionDate', 'currentDueDate', 'newDueDate', 'retirementDate'];
                dateFields.forEach(field => {
                    if (emp[field]) {
                        const hasHijri = hijriWords.some(word => emp[field].includes(word));
                        const isValidISO = /^\d{4}-\d{2}-\d{2}$/.test(emp[field]);
                        if (hasHijri || !isValidISO) {
                            issues++;
                        }
                    }
                });
            });
            
            thanks.forEach(thank => {
                if (thank.date) {
                    const hasHijri = hijriWords.some(word => thank.date.includes(word));
                    const isValidISO = /^\d{4}-\d{2}-\d{2}$/.test(thank.date);
                    if (hasHijri || !isValidISO) {
                        issues++;
                    }
                }
            });
            
            if (issues > 0) {
                throw new Error(`لا تزال هناك ${issues} مشكلة في التواريخ`);
            }
        }

        // تطبيق الحماية الدائمة
        async function applyPermanentProtection() {
            // إنشاء حماية ضد استخدام toLocaleDateString مع التقويم الهجري
            const originalToLocaleDateString = Date.prototype.toLocaleDateString;
            Date.prototype.toLocaleDateString = function(locales, options) {
                // إجبار استخدام التقويم الميلادي
                if (options) {
                    options.calendar = 'gregory';
                }
                return originalToLocaleDateString.call(this, locales, options);
            };
            
            // وضع علامة الحماية
            localStorage.setItem('hijri_protection_active', 'true');
        }

        // تحديث شريط التقدم
        function updateProgress(percentage) {
            const progressBar = document.getElementById('progressBar');
            progressBar.style.width = percentage + '%';
            progressBar.textContent = Math.round(percentage) + '%';
        }

        // تحديث عرض الخطوات
        function updateStepsDisplay() {
            const stepsList = document.getElementById('stepsList');
            
            let html = '';
            ultimateSteps.forEach((step, index) => {
                let className = '';
                let status = '⏳ في الانتظار';
                
                if (step.completed) {
                    className = 'completed';
                    status = '✅ مكتمل';
                } else if (step.failed) {
                    className = 'failed';
                    status = '❌ فشل';
                }
                
                html += `
                    <div class="step ${className}">
                        <span><strong>${index + 1}.</strong> ${step.name}</span>
                        <span class="step-status">${status}</span>
                    </div>
                `;
            });
            
            stepsList.innerHTML = html;
        }

        // عرض النتيجة النهائية
        function showUltimateFinalResult() {
            const finalResult = document.getElementById('finalResult');
            const reloadSection = document.getElementById('reloadSection');
            const allCompleted = ultimateSteps.every(step => step.completed);
            
            finalResult.style.display = 'block';
            
            if (allCompleted) {
                finalResult.className = 'result success';
                finalResult.innerHTML = `
                    <h3>🎉 تم الحل النهائي بنجاح!</h3>
                    <p><strong>✅ تم القضاء على جميع التواريخ الهجرية نهائياً</strong></p>
                    <p><strong>✅ تم تطبيق حماية دائمة ضد التواريخ الهجرية</strong></p>
                    <p><strong>✅ جميع التواريخ الآن تستخدم التقويم الميلادي حصرياً</strong></p>
                    <p><strong>✅ تم حفظ نسخة احتياطية نهائية</strong></p>
                    <p><strong>🛡️ النظام محصن ضد عودة المشكلة</strong></p>
                `;
                reloadSection.style.display = 'block';
            } else {
                finalResult.className = 'result error';
                finalResult.innerHTML = `
                    <h3>❌ فشل في الحل النهائي!</h3>
                    <p>حدثت مشاكل أثناء عملية الإصلاح. يرجى المحاولة مرة أخرى.</p>
                `;
            }
        }

        // إعادة تحميل النظام
        function reloadSystem() {
            alert('سيتم إعادة تحميل النظام الآن. جميع التواريخ ستظهر بالتقويم الميلادي فقط!');
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 1000);
        }

        // وظيفة مساعدة للتوقف
        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        // تشغيل عند تحميل الصفحة
        window.addEventListener('load', function() {
            updateStepsDisplay();
        });
    </script>
</body>
</html>
