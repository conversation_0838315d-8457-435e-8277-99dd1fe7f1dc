/* تنسيقات خاصة لإصلاح مشكلة النموذج */

/* تنسيق عام للنموذج */
.employee-form-container {
    background-color: #ffffff;
    border-radius: 12px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    padding: 2rem;
    margin: 1rem auto;
    max-width: 95%;
    width: 1400px;
    position: relative;
    overflow: hidden;
    min-height: 85vh;
}

.employee-form-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(to right, #2563eb, #3b82f6, #60a5fa);
}

.form-title {
    text-align: center;
    color: #2563eb;
    margin-bottom: 2rem;
    font-size: 2.2rem;
    position: relative;
    padding-bottom: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.8rem;
}

.form-title i {
    font-size: 2.5rem;
    color: #2563eb;
}

.form-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 150px;
    height: 4px;
    background: linear-gradient(to right, #2563eb, #3b82f6);
    border-radius: 4px;
}

/* تنسيق أقسام النموذج */
.form-section {
    margin-bottom: 2.5rem;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    background-color: #fff;
    border: 1px solid #e0e0e0;
    position: relative;
}

.section-title {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    color: white;
    padding: 1.3rem 1.8rem;
    font-size: 1.4rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.section-title i {
    font-size: 1.8rem;
    margin-left: 0.5rem;
    background: rgba(255, 255, 255, 0.2);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

/* تنسيق شبكة النموذج */
.form-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    padding: 2.5rem;
    background: linear-gradient(to bottom, #f8fafc, #f1f5f9);
}

/* تنسيق مجموعات الحقول */
.form-group {
    margin-bottom: 1.8rem;
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 0.8rem;
    font-weight: 600;
    color: #333;
    font-size: 1.05rem;
    border-right: 3px solid #2563eb;
    padding-right: 0.8rem;
}

.required {
    color: #e53e3e;
    margin-right: 0.25rem;
    font-weight: bold;
}

/* تنسيق الحقول */
.form-group input,
.form-group select {
    width: 100%;
    height: 50px;
    padding: 0.75rem 1.2rem;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-family: 'Cairo', sans-serif;
    font-size: 1rem;
    transition: all 0.3s ease;
    box-sizing: border-box;
    background-color: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.form-group input:focus,
.form-group select:focus {
    border-color: #2563eb;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.25);
    outline: none;
}

.form-group input[readonly] {
    background-color: #f1f5f9;
    cursor: not-allowed;
    border-color: #cbd5e1;
    color: #64748b;
}

/* تنسيق النص المساعد */
.form-text {
    display: block;
    margin-top: 0.5rem;
    font-size: 0.85rem;
    color: #6b7280;
}

/* تنسيق أزرار النموذج */
.form-actions {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    margin-top: 2.5rem;
    margin-bottom: 1.5rem;
    padding: 1.5rem;
    background: linear-gradient(to left, rgba(59, 130, 246, 0.05), rgba(16, 185, 129, 0.05));
    border-radius: 10px;
}

.form-actions .btn {
    padding: 1rem 3rem;
    font-size: 1.2rem;
    border-radius: 50px;
    min-width: 180px;
    font-weight: 700;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.form-actions .btn:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.form-actions .btn-primary {
    background: linear-gradient(to right, #2563eb, #3b82f6);
    color: white;
    border: none;
}

.form-actions .btn-secondary {
    background: linear-gradient(to right, #059669, #10b981);
    color: white;
    border: none;
}

/* تنسيق متجاوب */
@media (max-width: 992px) {
    .form-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .form-actions .btn {
        width: 100%;
        max-width: 300px;
    }
}

/* تنسيق الوضع المظلم */
.dark-mode .form-section {
    background-color: #1e1e1e;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
    border-color: #333;
}

.dark-mode .section-title {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
}

.dark-mode .form-grid {
    background: linear-gradient(to bottom, #111827, #0f172a);
}

.dark-mode .form-group label {
    color: #e2e8f0;
    border-right-color: #3b82f6;
}

.dark-mode .form-group input,
.dark-mode .form-group select {
    background-color: #1f2937;
    border-color: #374151;
    color: #f3f4f6;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.dark-mode .form-group input[readonly] {
    background-color: #111827;
    border-color: #1f2937;
    color: #9ca3af;
}

.dark-mode .form-text {
    color: #9ca3af;
}

.dark-mode .form-actions {
    background: linear-gradient(to left, rgba(59, 130, 246, 0.1), rgba(16, 185, 129, 0.1));
}
