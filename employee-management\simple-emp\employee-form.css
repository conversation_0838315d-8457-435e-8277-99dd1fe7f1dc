/* أنماط خاصة بصفحة ملف الموظف */

/* حاوية النموذج */
.employee-form-container {
    background-color: var(--card-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 2rem;
    margin: 1rem auto;
    max-width: 95%;
    width: 1400px;
    position: relative;
    overflow: hidden;
    min-height: 85vh;
    display: flex;
    flex-direction: column;
}

.employee-form-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: var(--gradient-primary);
}

/* عنوان النموذج */
.form-title {
    font-size: 1.8rem;
    margin-bottom: 2rem;
    color: var(--primary-color);
    text-align: center;
    font-weight: 700;
    position: relative;
    padding-bottom: 1rem;
}

.form-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: 2px;
}

/* شبكة النموذج */
.form-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    margin: 1rem;
    padding: 0.5rem;
}

/* أقسام النموذج */
.form-section {
    margin-bottom: 2rem;
    border: 1px solid #eee;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.section-title {
    background-color: var(--primary-color);
    padding: 1rem;
    font-weight: 600;
    color: white;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.section-title i {
    color: white;
}

/* تأثيرات الوضع المظلم لأقسام النموذج */
.dark-mode .form-section {
    border-color: #333;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.dark-mode .section-title {
    background-color: var(--primary-dark);
    border-color: #333;
}

/* مجموعة الحقول */
.form-group {
    margin-bottom: 1.5rem;
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-color);
}

.required {
    color: var(--danger-color);
}

.form-group input,
.form-group select {
    width: 100%;
    height: 45px;
    padding: 0.8rem 1rem;
    border: 1px solid #e5e7eb;
    border-radius: var(--border-radius);
    font-family: 'Cairo', sans-serif;
    font-size: 1rem;
    transition: var(--transition);
    box-sizing: border-box;
}

/* حقل مع زر */
.input-with-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.input-with-button select {
    flex: 1;
}

.input-with-button .btn {
    padding: 0.8rem;
    border-radius: var(--border-radius);
    background-color: var(--secondary-color);
    color: white;
    border: none;
    cursor: pointer;
    transition: var(--transition);
}

.input-with-button .btn:hover {
    background-color: var(--secondary-color-dark);
    transform: translateY(-2px);
}

.input-with-button .btn-sm {
    padding: 0.5rem;
    font-size: 0.9rem;
}

.form-text {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.85rem;
    color: #6b7280;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group input[readonly] {
    background-color: #f9fafb;
    cursor: not-allowed;
}

/* أزرار النموذج */
.form-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
}

.form-actions .btn {
    padding: 0.8rem 2rem;
    font-size: 1.1rem;
    border-radius: 50px;
    min-width: 150px;
}

/* تصميم متجاوب */
@media (max-width: 992px) {
    .form-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr;
    }

    .employee-form-container {
        padding: 1.5rem;
        margin: 1rem;
    }
}

/* تأثيرات إضافية */
.form-group input:hover,
.form-group select:hover {
    border-color: #d1d5db;
}

.form-group input:focus,
.form-group select:focus {
    transform: translateY(-2px);
}

/* تأثيرات الوضع المظلم */
.dark-mode .employee-form-container {
    background-color: var(--card-color);
}

.dark-mode .form-group input,
.dark-mode .form-group select {
    background-color: #2a2a2a;
    border-color: #444;
    color: white;
}

.dark-mode .form-group input::placeholder,
.dark-mode .form-group select::placeholder {
    color: #9ca3af;
}

.dark-mode .form-group input[readonly] {
    background-color: #1e1e1e;
}

/* تنسيق Select2 */
.select2-container {
    width: 100% !important;
    z-index: 9999 !important;
}

.select2-container--default .select2-selection--single {
    height: 45px !important;
    padding: 8px !important;
    border: 1px solid #e5e7eb !important;
    border-radius: var(--border-radius) !important;
    font-family: 'Cairo', sans-serif !important;
    font-size: 1rem !important;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 45px !important;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 28px !important;
    color: var(--text-color) !important;
}

.select2-dropdown {
    border: 1px solid #e5e7eb !important;
    border-radius: var(--border-radius) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: var(--primary-color) !important;
}

.select2-container--default .select2-search--dropdown .select2-search__field {
    border: 1px solid #e5e7eb !important;
    border-radius: var(--border-radius) !important;
    padding: 8px !important;
    font-family: 'Cairo', sans-serif !important;
}

.select2-container--default .select2-results__option {
    padding: 8px 12px !important;
    font-family: 'Cairo', sans-serif !important;
}

/* تنسيق Select2 في الوضع المظلم */
.dark-mode .select2-container--default .select2-selection--single {
    background-color: #2a2a2a !important;
    border-color: #444 !important;
}

.dark-mode .select2-container--default .select2-selection--single .select2-selection__rendered {
    color: white !important;
}

.dark-mode .select2-dropdown {
    background-color: #2a2a2a !important;
    border-color: #444 !important;
}

.dark-mode .select2-container--default .select2-results__option {
    color: white !important;
}

.dark-mode .select2-container--default .select2-search--dropdown .select2-search__field {
    background-color: #1e1e1e !important;
    border-color: #444 !important;
    color: white !important;
}

/* تأثيرات التحقق من الصحة */
.form-group input:invalid:not(:placeholder-shown),
.form-group select:invalid:not(:placeholder-shown) {
    border-color: var(--danger-color);
}

.form-group input:valid:not(:placeholder-shown),
.form-group select:valid:not(:placeholder-shown) {
    border-color: var(--secondary-color);
}

/* تأثيرات الحركة */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.employee-form-container {
    animation: fadeIn 0.5s ease-out;
}

.form-group {
    opacity: 0;
    animation: fadeIn 0.5s ease-out forwards;
}

.form-group:nth-child(1) { animation-delay: 0.1s; }
.form-group:nth-child(2) { animation-delay: 0.15s; }
.form-group:nth-child(3) { animation-delay: 0.2s; }
.form-group:nth-child(4) { animation-delay: 0.25s; }
.form-group:nth-child(5) { animation-delay: 0.3s; }
.form-group:nth-child(6) { animation-delay: 0.35s; }
.form-group:nth-child(7) { animation-delay: 0.4s; }
.form-group:nth-child(8) { animation-delay: 0.45s; }
.form-group:nth-child(9) { animation-delay: 0.5s; }
.form-group:nth-child(10) { animation-delay: 0.55s; }
.form-group:nth-child(11) { animation-delay: 0.6s; }
.form-group:nth-child(12) { animation-delay: 0.65s; }
.form-group:nth-child(13) { animation-delay: 0.7s; }
.form-group:nth-child(14) { animation-delay: 0.75s; }
.form-group:nth-child(15) { animation-delay: 0.8s; }
.form-group:nth-child(16) { animation-delay: 0.85s; }
.form-group:nth-child(17) { animation-delay: 0.9s; }
.form-group:nth-child(18) { animation-delay: 0.95s; }
.form-group:nth-child(19) { animation-delay: 1s; }
.form-group:nth-child(20) { animation-delay: 1.05s; }
.form-group:nth-child(21) { animation-delay: 1.1s; }

.form-actions {
    opacity: 0;
    animation: fadeIn 0.5s ease-out 1.2s forwards;
}

/* تنسيق الإشعارات */
.notification {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 1rem;
    z-index: 1000;
    min-width: 300px;
    max-width: 500px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    animation: slideDown 0.3s ease-out;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

.notification-content i {
    font-size: 1.5rem;
}

.notification.success .notification-content i {
    color: var(--success-color, #10b981);
}

.notification.info .notification-content i {
    color: var(--info-color, #3b82f6);
}

.notification.warning .notification-content i {
    color: var(--warning-color, #f59e0b);
}

.notification.error .notification-content i {
    color: var(--danger-color, #ef4444);
}

.notification-close {
    background: none;
    border: none;
    cursor: pointer;
    color: #999;
    font-size: 1rem;
    padding: 0;
    margin: 0;
    transition: var(--transition);
}

.notification-close:hover {
    color: #333;
}

.notification.fade-out {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
    transition: opacity 0.3s, transform 0.3s;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}
