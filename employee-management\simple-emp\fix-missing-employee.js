/**
 * ملف لإصلاح مشكلة الموظفين غير الموجودين في كتب الشكر
 */

// إصلاح موظف غير موجود
function fixMissingEmployee(employeeId) {
    console.log('محاولة إصلاح موظف غير موجود:', employeeId);
    
    // الحصول على كتب الشكر للموظف
    const thanks = JSON.parse(localStorage.getItem('thanks') || '[]');
    const employeeThanks = thanks.filter(thank => thank.employeeId == employeeId);
    
    if (employeeThanks.length === 0) {
        showNotification('لم يتم العثور على كتب شكر للموظف', 'error');
        return;
    }
    
    // عرض نافذة منبثقة للإصلاح
    const modalContent = `
        <div class="modal-header">
            <h2>إصلاح موظف غير موجود (ID: ${employeeId})</h2>
            <span class="close-modal">&times;</span>
        </div>
        <div class="modal-body">
            <div class="thanks-alert warning">
                <h3><i class="fas fa-exclamation-triangle"></i> تنبيه</h3>
                <p>تم العثور على ${employeeThanks.length} كتاب شكر لموظف غير موجود في قاعدة البيانات.</p>
            </div>
            
            <p>يرجى اختيار أحد الخيارات التالية:</p>
            
            <div class="form-group">
                <label for="fixOption">خيار الإصلاح:</label>
                <select id="fixOption" class="form-control">
                    <option value="assign">تعيين كتب الشكر لموظف موجود</option>
                    <option value="create">إنشاء موظف جديد</option>
                    <option value="delete">حذف كتب الشكر</option>
                </select>
            </div>
            
            <div id="assignEmployeeSection">
                <div class="form-group">
                    <label for="assignEmployee">اختر الموظف:</label>
                    <select id="assignEmployee" class="form-control">
                        <option value="">-- اختر موظف --</option>
                    </select>
                </div>
            </div>
            
            <div id="createEmployeeSection" style="display: none;">
                <div class="form-group">
                    <label for="newEmployeeName">اسم الموظف الجديد:</label>
                    <input type="text" id="newEmployeeName" class="form-control" placeholder="أدخل اسم الموظف">
                </div>
                
                <div class="form-group">
                    <label for="newEmployeeJobDescription">الوصف الوظيفي:</label>
                    <select id="newEmployeeJobDescription" class="form-control">
                        <option value="teaching">تدريسي</option>
                        <option value="technical">فني</option>
                        <option value="administrative">إداري</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button id="cancelFixBtn" class="btn btn-secondary">إلغاء</button>
            <button id="confirmFixBtn" class="btn btn-primary">تأكيد</button>
        </div>
    `;
    
    showModal(modalContent);
    
    // تحميل قائمة الموظفين
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');
    const assignEmployeeSelect = document.getElementById('assignEmployee');
    
    if (assignEmployeeSelect) {
        employees.forEach(employee => {
            const option = document.createElement('option');
            option.value = employee.id;
            option.textContent = employee.name;
            assignEmployeeSelect.appendChild(option);
        });
    }
    
    // تبديل عرض الأقسام بناءً على الخيار المحدد
    const fixOption = document.getElementById('fixOption');
    if (fixOption) {
        fixOption.addEventListener('change', function() {
            const assignSection = document.getElementById('assignEmployeeSection');
            const createSection = document.getElementById('createEmployeeSection');
            
            if (this.value === 'assign') {
                assignSection.style.display = 'block';
                createSection.style.display = 'none';
            } else if (this.value === 'create') {
                assignSection.style.display = 'none';
                createSection.style.display = 'block';
            } else {
                assignSection.style.display = 'none';
                createSection.style.display = 'none';
            }
        });
    }
    
    // إضافة مستمع حدث لزر الإلغاء
    const cancelFixBtn = document.getElementById('cancelFixBtn');
    if (cancelFixBtn) {
        cancelFixBtn.addEventListener('click', function() {
            closeModal();
        });
    }
    
    // إضافة مستمع حدث لزر التأكيد
    const confirmFixBtn = document.getElementById('confirmFixBtn');
    if (confirmFixBtn) {
        confirmFixBtn.addEventListener('click', function() {
            const selectedOption = fixOption.value;
            
            if (selectedOption === 'assign') {
                const selectedEmployeeId = assignEmployeeSelect.value;
                if (!selectedEmployeeId) {
                    showNotification('يرجى اختيار موظف', 'error');
                    return;
                }
                
                // تعيين كتب الشكر للموظف المحدد
                assignThanksToEmployee(employeeId, selectedEmployeeId);
            } else if (selectedOption === 'create') {
                const newEmployeeName = document.getElementById('newEmployeeName').value;
                const newEmployeeJobDescription = document.getElementById('newEmployeeJobDescription').value;
                
                if (!newEmployeeName) {
                    showNotification('يرجى إدخال اسم الموظف', 'error');
                    return;
                }
                
                // إنشاء موظف جديد وتعيين كتب الشكر له
                createEmployeeAndAssignThanks(employeeId, newEmployeeName, newEmployeeJobDescription);
            } else if (selectedOption === 'delete') {
                // حذف كتب الشكر
                deleteThanksForEmployee(employeeId);
            }
            
            closeModal();
        });
    }
}

// تعيين كتب الشكر لموظف موجود
function assignThanksToEmployee(oldEmployeeId, newEmployeeId) {
    console.log('تعيين كتب الشكر من الموظف', oldEmployeeId, 'إلى الموظف', newEmployeeId);
    
    // الحصول على كتب الشكر
    const thanks = JSON.parse(localStorage.getItem('thanks') || '[]');
    
    // تحديث معرف الموظف في كتب الشكر
    let updatedCount = 0;
    thanks.forEach(thank => {
        if (thank.employeeId == oldEmployeeId) {
            thank.employeeId = parseInt(newEmployeeId);
            updatedCount++;
        }
    });
    
    // حفظ التغييرات
    localStorage.setItem('thanks', JSON.stringify(thanks));
    
    // تحديث عرض الجدول
    loadThanks();
    
    // تحديث تواريخ الموظف
    if (window.thanksCalculator) {
        window.thanksCalculator.updateNewDueDateBasedOnThanks(newEmployeeId);
        window.thanksCalculator.updateNextPromotionDateBasedOnThanks(newEmployeeId);
    }
    
    showNotification(`تم تعيين ${updatedCount} كتاب شكر للموظف بنجاح`, 'success');
}

// إنشاء موظف جديد وتعيين كتب الشكر له
function createEmployeeAndAssignThanks(oldEmployeeId, employeeName, jobDescription) {
    console.log('إنشاء موظف جديد وتعيين كتب الشكر له:', employeeName);
    
    // الحصول على الموظفين
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');
    
    // إنشاء معرف فريد جديد
    const newId = employees.length > 0 ? Math.max(...employees.map(emp => emp.id)) + 1 : 1;
    
    // إنشاء موظف جديد
    const currentDate = new Date().toISOString().split('T')[0];
    const newEmployee = {
        id: newId,
        name: employeeName,
        jobDescription: jobDescription,
        hireDate: currentDate,
        currentDueDate: currentDate,
        lastPromotionDate: currentDate
    };
    
    // إضافة الموظف الجديد
    employees.push(newEmployee);
    localStorage.setItem('employees', JSON.stringify(employees));
    
    // تعيين كتب الشكر للموظف الجديد
    assignThanksToEmployee(oldEmployeeId, newId);
    
    showNotification(`تم إنشاء موظف جديد وتعيين كتب الشكر له بنجاح`, 'success');
}

// حذف كتب الشكر لموظف
function deleteThanksForEmployee(employeeId) {
    console.log('حذف كتب الشكر للموظف:', employeeId);
    
    // الحصول على كتب الشكر
    const thanks = JSON.parse(localStorage.getItem('thanks') || '[]');
    
    // حساب عدد كتب الشكر التي سيتم حذفها
    const thanksToDelete = thanks.filter(thank => thank.employeeId == employeeId);
    const deleteCount = thanksToDelete.length;
    
    // حذف كتب الشكر للموظف
    const updatedThanks = thanks.filter(thank => thank.employeeId != employeeId);
    
    // حفظ التغييرات
    localStorage.setItem('thanks', JSON.stringify(updatedThanks));
    
    // تحديث عرض الجدول
    loadThanks();
    
    showNotification(`تم حذف ${deleteCount} كتاب شكر بنجاح`, 'success');
}
