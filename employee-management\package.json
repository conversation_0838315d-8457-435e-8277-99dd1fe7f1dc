{"name": "employee-management", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@nextui-org/react": "^2.6.11", "@prisma/client": "^6.7.0", "autoprefixer": "^10.4.21", "framer-motion": "^12.9.7", "next": "15.3.1", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "prisma": "^6.7.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "recharts": "^2.15.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4", "typescript": "^5"}}