<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام كتب الشكر المنظم - إدارة العلاوات والترفيعات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 30px;
        }

        .sidebar {
            background: white;
            border-radius: 10px;
            padding: 20px;
            height: fit-content;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .sidebar h3 {
            color: #667eea;
            margin-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 10px;
        }

        .employee-list {
            list-style: none;
        }

        .employee-item {
            padding: 10px;
            margin-bottom: 5px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s;
            border-right: 3px solid transparent;
        }

        .employee-item:hover {
            background: #f8f9ff;
            border-right-color: #667eea;
        }

        .employee-item.active {
            background: #667eea;
            color: white;
            border-right-color: #4c63d2;
        }

        .employee-name {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .employee-stats {
            font-size: 0.8rem;
            opacity: 0.7;
        }

        .content-area {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .welcome-message {
            text-align: center;
            color: #666;
            padding: 50px 20px;
        }

        .welcome-message i {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 20px;
        }

        .employee-details {
            display: none;
        }

        .employee-details.active {
            display: block;
        }

        .employee-header {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .employee-header h2 {
            margin-bottom: 10px;
        }

        .quick-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }

        .stat-card {
            background: rgba(255,255,255,0.2);
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }

        .add-thanks-form {
            background: #f8f9ff;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-right: 4px solid #667eea;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr auto;
            gap: 15px;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }

        .form-group select,
        .form-group input {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a67d8;
        }

        .btn-danger {
            background: #e53e3e;
            color: white;
            padding: 5px 10px;
            font-size: 12px;
        }

        .btn-danger:hover {
            background: #c53030;
        }

        .thanks-list {
            margin-top: 20px;
        }

        .thanks-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .thanks-table th,
        .thanks-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #e2e8f0;
        }

        .thanks-table th {
            background: #f7fafc;
            font-weight: bold;
            color: #4a5568;
        }

        .thanks-table tr:hover {
            background: #f7fafc;
        }

        .thanks-type {
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }

        .type-appreciation {
            background: #c6f6d5;
            color: #22543d;
        }

        .type-ministerial {
            background: #fef5e7;
            color: #744210;
        }

        .type-presidential {
            background: #bee3f8;
            color: #2a4365;
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }

        .empty-state i {
            font-size: 3rem;
            color: #cbd5e0;
            margin-bottom: 15px;
        }

        .message {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
        }

        .message.success {
            background: #c6f6d5;
            color: #22543d;
            border: 1px solid #9ae6b4;
        }

        .message.error {
            background: #fed7d7;
            color: #742a2a;
            border: 1px solid #feb2b2;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }

            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-award"></i> نظام كتب الشكر المنظم</h1>
            <p>إدارة كتب الشكر والتقدير بشكل منظم لكل موظف على حدة</p>
        </div>

        <div class="main-content">
            <!-- الشريط الجانبي - قائمة الموظفين -->
            <div class="sidebar">
                <h3><i class="fas fa-users"></i> الموظفين</h3>
                <ul class="employee-list" id="employeeList">
                    <!-- سيتم ملء قائمة الموظفين هنا -->
                </ul>
            </div>

            <!-- المنطقة الرئيسية -->
            <div class="content-area">
                <!-- رسالة الترحيب -->
                <div class="welcome-message" id="welcomeMessage">
                    <i class="fas fa-hand-point-right"></i>
                    <h2>اختر موظف من القائمة الجانبية</h2>
                    <p>لعرض وإدارة كتب الشكر الخاصة به</p>
                </div>

                <!-- تفاصيل الموظف -->
                <div class="employee-details" id="employeeDetails">
                    <!-- رسائل النظام -->
                    <div id="messageArea"></div>

                    <!-- معلومات الموظف -->
                    <div class="employee-header" id="employeeHeader">
                        <!-- سيتم ملء معلومات الموظف هنا -->
                    </div>

                    <!-- نموذج إضافة كتاب شكر -->
                    <div class="add-thanks-form">
                        <h3><i class="fas fa-plus"></i> إضافة كتاب شكر جديد</h3>
                        <form id="addThanksForm">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="thanksType">نوع كتاب الشكر</label>
                                    <select id="thanksType" required>
                                        <option value="">اختر النوع</option>
                                        <option value="appreciation">شكر جامعي (شهر واحد)</option>
                                        <option value="ministerial">شكر وزاري (شهر واحد)</option>
                                        <option value="presidential">شكر رئاسي (6 أشهر)</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="thanksDate">التاريخ</label>
                                    <input type="date" id="thanksDate" required>
                                </div>

                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-plus"></i> إضافة
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- قائمة كتب الشكر -->
                    <div class="thanks-list">
                        <h3><i class="fas fa-list"></i> كتب الشكر</h3>
                        <div id="thanksTableContainer">
                            <!-- سيتم ملء جدول كتب الشكر هنا -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="employees-data.js"></script>
    <script src="thanks-calculator.js"></script>
    <script src="employee-file-manager.js"></script>
    <script>
        let currentEmployeeId = null;
        let employees = [];

        // تهيئة النظام
        document.addEventListener('DOMContentLoaded', function() {
            loadEmployees();
            displayEmployeeList();
            setupEventListeners();

            // نقل البيانات من النظام القديم إذا لزم الأمر
            migrateOldData();
        });

        // تحميل بيانات الموظفين
        function loadEmployees() {
            employees = JSON.parse(localStorage.getItem('employees') || '[]');
        }

        // عرض قائمة الموظفين
        function displayEmployeeList() {
            const employeeList = document.getElementById('employeeList');
            employeeList.innerHTML = '';

            if (employees.length === 0) {
                employeeList.innerHTML = '<li style="text-align: center; color: #666;">لا يوجد موظفين</li>';
                return;
            }

            employees.forEach(employee => {
                const stats = window.employeeFileManager.getEmployeeThanksStats(employee.id);

                const li = document.createElement('li');
                li.className = 'employee-item';
                li.dataset.employeeId = employee.id;
                li.innerHTML = `
                    <div class="employee-name">${employee.name}</div>
                    <div class="employee-stats">
                        ${stats.total} كتاب شكر | ${stats.thisYear} هذا العام
                    </div>
                `;

                li.addEventListener('click', () => selectEmployee(employee.id));
                employeeList.appendChild(li);
            });
        }

        // اختيار موظف
        function selectEmployee(employeeId) {
            currentEmployeeId = employeeId;

            // تحديث الشريط الجانبي
            document.querySelectorAll('.employee-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector(`[data-employee-id="${employeeId}"]`).classList.add('active');

            // إخفاء رسالة الترحيب وإظهار تفاصيل الموظف
            document.getElementById('welcomeMessage').style.display = 'none';
            document.getElementById('employeeDetails').classList.add('active');

            // عرض معلومات الموظف
            displayEmployeeInfo(employeeId);
            displayEmployeeThanks(employeeId);

            // تعيين تاريخ اليوم
            document.getElementById('thanksDate').value = new Date().toISOString().split('T')[0];
        }

        // عرض معلومات الموظف
        function displayEmployeeInfo(employeeId) {
            const employee = employees.find(emp => emp.id == employeeId);
            const stats = window.employeeFileManager.getEmployeeThanksStats(employeeId);

            const employeeHeader = document.getElementById('employeeHeader');
            employeeHeader.innerHTML = `
                <h2>${employee.name}</h2>
                <p>الرقم الوظيفي: ${employee.id} | ${employee.jobDescription || 'غير محدد'}</p>
                <div class="quick-stats">
                    <div class="stat-card">
                        <div class="stat-number">${stats.total}</div>
                        <div class="stat-label">إجمالي كتب الشكر</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${stats.appreciation}</div>
                        <div class="stat-label">شكر جامعي</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${stats.ministerial}</div>
                        <div class="stat-label">شكر وزاري</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${stats.presidential}</div>
                        <div class="stat-label">شكر رئاسي</div>
                    </div>
                </div>
            `;
        }

        // عرض كتب الشكر للموظف
        function displayEmployeeThanks(employeeId) {
            const thanks = window.employeeFileManager.loadEmployeeThanks(employeeId);
            const container = document.getElementById('thanksTableContainer');

            if (thanks.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-inbox"></i>
                        <h3>لا توجد كتب شكر</h3>
                        <p>لم يتم إضافة أي كتب شكر لهذا الموظف بعد</p>
                    </div>
                `;
                return;
            }

            // ترتيب كتب الشكر حسب التاريخ (الأحدث أولاً)
            thanks.sort((a, b) => new Date(b.date) - new Date(a.date));

            let tableHTML = `
                <table class="thanks-table">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>نوع الشكر</th>
                            <th>التاريخ</th>
                            <th>التأثير</th>
                            <th>حذف</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            thanks.forEach((thank, index) => {
                let typeText = '';
                let typeClass = '';
                let effect = '';

                switch (thank.type) {
                    case 'appreciation':
                        typeText = 'شكر جامعي';
                        typeClass = 'type-appreciation';
                        effect = 'شهر واحد';
                        break;
                    case 'ministerial':
                        typeText = 'شكر وزاري';
                        typeClass = 'type-ministerial';
                        effect = 'شهر واحد';
                        break;
                    case 'presidential':
                        typeText = 'شكر رئاسي';
                        typeClass = 'type-presidential';
                        effect = '6 أشهر';
                        break;
                }

                tableHTML += `
                    <tr>
                        <td>${index + 1}</td>
                        <td><span class="thanks-type ${typeClass}">${typeText}</span></td>
                        <td>${formatDate(thank.date)}</td>
                        <td>${effect}</td>
                        <td>
                            <button class="btn btn-danger" onclick="deleteThank(${thank.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });

            tableHTML += '</tbody></table>';
            container.innerHTML = tableHTML;
        }

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            document.getElementById('addThanksForm').addEventListener('submit', addThank);
        }

        // إضافة كتاب شكر
        function addThank(event) {
            event.preventDefault();

            if (!currentEmployeeId) {
                showMessage('يرجى اختيار موظف أولاً', 'error');
                return;
            }

            const type = document.getElementById('thanksType').value;
            const date = document.getElementById('thanksDate').value;

            if (!type || !date) {
                showMessage('يرجى ملء جميع الحقول', 'error');
                return;
            }

            const thank = {
                type: type,
                date: date,
                createdAt: new Date().toISOString()
            };

            // إضافة كتاب الشكر للموظف
            window.employeeFileManager.addThankToEmployee(currentEmployeeId, thank);

            // تحديث تواريخ الموظف
            if (window.thanksCalculator) {
                window.thanksCalculator.updateNewDueDateBasedOnThanks(currentEmployeeId);
                window.thanksCalculator.updateNextPromotionDateBasedOnThanks(currentEmployeeId);
            }

            // تحديث العرض
            displayEmployeeInfo(currentEmployeeId);
            displayEmployeeThanks(currentEmployeeId);
            displayEmployeeList(); // تحديث الإحصائيات في الشريط الجانبي

            // إعادة تعيين النموذج
            document.getElementById('addThanksForm').reset();
            document.getElementById('thanksDate').value = new Date().toISOString().split('T')[0];

            const employee = employees.find(emp => emp.id == currentEmployeeId);
            showMessage(`تم إضافة كتاب شكر للموظف ${employee.name} بنجاح`, 'success');
        }

        // حذف كتاب شكر
        function deleteThank(thankId) {
            if (!confirm('هل أنت متأكد من حذف كتاب الشكر؟')) {
                return;
            }

            const success = window.employeeFileManager.removeThankFromEmployee(currentEmployeeId, thankId);

            if (success) {
                // تحديث تواريخ الموظف
                if (window.thanksCalculator) {
                    window.thanksCalculator.updateNewDueDateBasedOnThanks(currentEmployeeId);
                    window.thanksCalculator.updateNextPromotionDateBasedOnThanks(currentEmployeeId);
                }

                // تحديث العرض
                displayEmployeeInfo(currentEmployeeId);
                displayEmployeeThanks(currentEmployeeId);
                displayEmployeeList();

                showMessage('تم حذف كتاب الشكر بنجاح', 'success');
            } else {
                showMessage('فشل في حذف كتاب الشكر', 'error');
            }
        }

        // نقل البيانات من النظام القديم
        function migrateOldData() {
            const oldThanks = JSON.parse(localStorage.getItem('thanks') || '[]');
            if (oldThanks.length > 0) {
                const migratedCount = window.employeeFileManager.migrateFromOldSystem();
                if (migratedCount > 0) {
                    console.log(`تم نقل ${migratedCount} كتاب شكر من النظام القديم`);
                    displayEmployeeList(); // تحديث العرض
                }
            }
        }

        // تنسيق التاريخ (ميلادي فقط - إجباري)
        function formatDate(dateString) {
            if (!dateString) return '-';

            const date = new Date(dateString);
            if (isNaN(date.getTime())) return dateString;

            // إجبار استخدام التقويم الميلادي مع تنسيق يدوي لضمان عدم ظهور التاريخ الهجري
            const year = date.getFullYear();
            const month = date.getMonth() + 1;
            const day = date.getDate();

            const monthNames = [
                'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
            ];

            return `${day} ${monthNames[month - 1]} ${year}`;
        }

        // عرض رسالة
        function showMessage(text, type) {
            const messageArea = document.getElementById('messageArea');
            messageArea.innerHTML = `<div class="message ${type}">${text}</div>`;

            setTimeout(() => {
                messageArea.innerHTML = '';
            }, 3000);
        }
    </script>
</body>
</html>
