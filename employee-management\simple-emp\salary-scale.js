// انتظر حتى يتم تحميل المستند بالكامل
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة صفحة سلم الرواتب
    initSalaryScalePage();
});

// تهيئة صفحة سلم الرواتب
function initSalaryScalePage() {
    // تحميل بيانات سلم الرواتب
    loadSalaryScale();
    
    // تهيئة أزرار الإجراءات
    initActionButtons();
    
    // تهيئة النوافذ المنبثقة
    initModals();
}

// تحميل بيانات سلم الرواتب
function loadSalaryScale() {
    // في الإصدار النهائي، سيتم استبدال هذا بطلب API لجلب البيانات من الخادم
    // هنا نستخدم بيانات تجريبية للعرض
    
    // التحقق من وجود بيانات في التخزين المحلي
    let salaryScale = localStorage.getItem('salaryScale');
    
    if (!salaryScale) {
        // إذا لم تكن هناك بيانات، استخدم بيانات افتراضية
        salaryScale = {
            lastUpdate: '2025/05/01',
            grades: [
                {
                    grade: 1,
                    annualAllowance: 20,
                    promotionYears: 5,
                    salaries: [910, 930, 950, 970, 990, 1010, 1030, 1050, 1070, 1090, 1110]
                },
                {
                    grade: 2,
                    annualAllowance: 17,
                    promotionYears: 5,
                    salaries: [773, 790, 807, 824, 841, 858, 875, 892, 909, 926, 943]
                },
                {
                    grade: 3,
                    annualAllowance: 10,
                    promotionYears: 5,
                    salaries: [600, 610, 620, 630, 640, 650, 660, 670, 680, 690, 700]
                },
                {
                    grade: 4,
                    annualAllowance: 8,
                    promotionYears: 5,
                    salaries: [509, 517, 525, 533, 541, 549, 557, 565, 573, 581, 589]
                },
                {
                    grade: 5,
                    annualAllowance: 6,
                    promotionYears: 5,
                    salaries: [429, 435, 441, 447, 453, 459, 465, 471, 477, 483, 489]
                },
                {
                    grade: 6,
                    annualAllowance: 6,
                    promotionYears: 4,
                    salaries: [362, 368, 374, 380, 386, 392, 398, 404, 410, 416, 422]
                },
                {
                    grade: 7,
                    annualAllowance: 6,
                    promotionYears: 4,
                    salaries: [296, 302, 308, 314, 320, 326, 332, 338, 344, 350, 356]
                },
                {
                    grade: 8,
                    annualAllowance: 3,
                    promotionYears: 4,
                    salaries: [260, 263, 266, 269, 272, 275, 278, 281, 284, 287, 290]
                },
                {
                    grade: 9,
                    annualAllowance: 3,
                    promotionYears: 4,
                    salaries: [210, 213, 216, 219, 222, 225, 228, 231, 234, 237, 240]
                },
                {
                    grade: 10,
                    annualAllowance: 3,
                    promotionYears: 4,
                    salaries: [170, 173, 176, 179, 182, 185, 188, 191, 194, 197, 200]
                }
            ]
        };
        
        // حفظ البيانات في التخزين المحلي
        localStorage.setItem('salaryScale', JSON.stringify(salaryScale));
    } else {
        // تحويل البيانات من JSON إلى كائن JavaScript
        salaryScale = JSON.parse(salaryScale);
    }
    
    // عرض تاريخ آخر تحديث
    document.getElementById('lastUpdate').textContent = salaryScale.lastUpdate;
    
    // عرض البيانات في الجدول
    displaySalaryScale(salaryScale);
}

// عرض بيانات سلم الرواتب في الجدول
function displaySalaryScale(salaryScale) {
    const tableBody = document.querySelector('#salaryScaleTable tbody');
    if (!tableBody) return;
    
    // مسح محتوى الجدول
    tableBody.innerHTML = '';
    
    // إضافة الصفوف إلى الجدول
    salaryScale.grades.forEach(grade => {
        const row = document.createElement('tr');
        
        // إضافة خلية الدرجة
        const gradeCell = document.createElement('td');
        gradeCell.className = 'grade-cell';
        gradeCell.textContent = grade.grade;
        row.appendChild(gradeCell);
        
        // إضافة خلية العلاوة السنوية
        const allowanceCell = document.createElement('td');
        allowanceCell.className = 'annual-allowance-cell';
        allowanceCell.textContent = grade.annualAllowance;
        row.appendChild(allowanceCell);
        
        // إضافة خلية عدد سنوات الترفيع
        const promotionYearsCell = document.createElement('td');
        promotionYearsCell.className = 'promotion-years-cell';
        promotionYearsCell.textContent = grade.promotionYears;
        row.appendChild(promotionYearsCell);
        
        // إضافة خلايا الرواتب
        grade.salaries.forEach((salary, index) => {
            const salaryCell = document.createElement('td');
            salaryCell.className = 'editable-cell';
            salaryCell.textContent = salary;
            salaryCell.setAttribute('data-grade', grade.grade);
            salaryCell.setAttribute('data-year', index + 1);
            salaryCell.addEventListener('click', function() {
                openEditSalaryModal(grade.grade, index + 1, salary);
            });
            row.appendChild(salaryCell);
        });
        
        tableBody.appendChild(row);
    });
}

// تهيئة أزرار الإجراءات
function initActionButtons() {
    // زر طباعة سلم الرواتب
    const printButton = document.getElementById('printSalaryScale');
    if (printButton) {
        printButton.addEventListener('click', function() {
            window.print();
        });
    }
    
    // زر تصدير سلم الرواتب إلى Excel
    const exportButton = document.getElementById('exportSalaryScale');
    if (exportButton) {
        exportButton.addEventListener('click', function() {
            exportToExcel();
        });
    }
    
    // زر تعديل سلم الرواتب
    const editButton = document.getElementById('editSalaryScale');
    if (editButton) {
        editButton.addEventListener('click', function() {
            toggleEditMode();
        });
    }
}

// تصدير سلم الرواتب إلى Excel
function exportToExcel() {
    // في الإصدار النهائي، سيتم استبدال هذا بوظيفة تصدير حقيقية
    alert('سيتم تنفيذ وظيفة التصدير إلى Excel في الإصدار النهائي');
}

// تبديل وضع التعديل
function toggleEditMode() {
    const editableCells = document.querySelectorAll('.editable-cell');
    
    editableCells.forEach(cell => {
        cell.classList.toggle('highlight-editable');
    });
    
    // تغيير نص الزر
    const editButton = document.getElementById('editSalaryScale');
    if (editButton.innerHTML.includes('تعديل')) {
        editButton.innerHTML = '<i class="fas fa-check"></i> إنهاء التعديل';
        showNotification('انقر على أي خلية لتعديل قيمتها', 'info');
    } else {
        editButton.innerHTML = '<i class="fas fa-edit"></i> تعديل سلم الرواتب';
        showNotification('تم الخروج من وضع التعديل', 'info');
    }
}

// تهيئة النوافذ المنبثقة
function initModals() {
    // الحصول على عناصر النوافذ المنبثقة
    const editSalaryModal = document.getElementById('editSalaryModal');
    const closeButtons = document.querySelectorAll('.close-modal');
    
    // أزرار الإغلاق
    closeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const modal = this.closest('.modal');
            closeModal(modal);
        });
    });
    
    // إغلاق النافذة عند النقر خارجها
    window.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal')) {
            closeModal(e.target);
        }
    });
    
    // زر إلغاء تعديل الراتب
    const cancelSalaryBtn = document.getElementById('cancelSalaryBtn');
    if (cancelSalaryBtn) {
        cancelSalaryBtn.addEventListener('click', function() {
            closeModal(editSalaryModal);
        });
    }
    
    // زر حفظ تعديل الراتب
    const saveSalaryBtn = document.getElementById('saveSalaryBtn');
    if (saveSalaryBtn) {
        saveSalaryBtn.addEventListener('click', function() {
            saveEditedSalary();
        });
    }
}

// فتح نافذة تعديل قيمة الراتب
function openEditSalaryModal(grade, year, salary) {
    // ملء حقول النموذج بالبيانات الحالية
    document.getElementById('editGrade').value = grade;
    document.getElementById('editYear').value = year;
    document.getElementById('editSalaryValue').value = salary;
    
    // فتح النافذة
    const editSalaryModal = document.getElementById('editSalaryModal');
    openModal(editSalaryModal);
}

// حفظ قيمة الراتب المعدلة
function saveEditedSalary() {
    // الحصول على قيم الحقول
    const grade = parseInt(document.getElementById('editGrade').value);
    const year = parseInt(document.getElementById('editYear').value);
    const salary = parseInt(document.getElementById('editSalaryValue').value);
    
    // التحقق من صحة البيانات
    if (isNaN(salary) || salary < 0) {
        alert('يرجى إدخال قيمة صحيحة للراتب');
        return;
    }
    
    // الحصول على بيانات سلم الرواتب
    let salaryScale = JSON.parse(localStorage.getItem('salaryScale') || '{}');
    
    // البحث عن الدرجة وتحديث قيمة الراتب
    const gradeIndex = salaryScale.grades.findIndex(g => g.grade === grade);
    if (gradeIndex !== -1) {
        salaryScale.grades[gradeIndex].salaries[year - 1] = salary;
        
        // تحديث تاريخ آخر تعديل
        const today = new Date();
        const formattedDate = `${today.getFullYear()}/${String(today.getMonth() + 1).padStart(2, '0')}/${String(today.getDate()).padStart(2, '0')}`;
        salaryScale.lastUpdate = formattedDate;
        
        // حفظ البيانات المحدثة في التخزين المحلي
        localStorage.setItem('salaryScale', JSON.stringify(salaryScale));
        
        // تحديث عرض الجدول
        displaySalaryScale(salaryScale);
        
        // تحديث تاريخ آخر تحديث في الواجهة
        document.getElementById('lastUpdate').textContent = formattedDate;
        
        // إغلاق النافذة
        closeModal(document.getElementById('editSalaryModal'));
        
        // عرض رسالة نجاح
        showNotification('تم تحديث قيمة الراتب بنجاح', 'success');
    }
}

// فتح نافذة منبثقة
function openModal(modal) {
    if (modal) {
        modal.style.display = 'block';
    }
}

// إغلاق نافذة منبثقة
function closeModal(modal) {
    if (modal) {
        modal.style.display = 'none';
    }
}

// عرض إشعار
function showNotification(message, type = 'info') {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-info-circle'}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close"><i class="fas fa-times"></i></button>
    `;
    
    // إضافة الإشعار إلى الصفحة
    document.body.appendChild(notification);
    
    // إضافة مستمع حدث لزر الإغلاق
    const closeButton = notification.querySelector('.notification-close');
    closeButton.addEventListener('click', function() {
        notification.remove();
    });
    
    // إزالة الإشعار تلقائيًا بعد 5 ثوانٍ
    setTimeout(() => {
        notification.classList.add('fade-out');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 5000);
}
