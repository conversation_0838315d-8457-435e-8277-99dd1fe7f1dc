<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقارير العلاوات - نظام إدارة العلاوات والترفيعات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #667eea;
        }

        .header h1 {
            color: #667eea;
            margin-bottom: 10px;
            font-size: 2.2em;
        }

        .header p {
            color: #666;
            font-size: 1.1em;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .stat-card h3 {
            font-size: 2em;
            margin-bottom: 5px;
        }

        .stat-card p {
            font-size: 1em;
            opacity: 0.9;
        }

        .search-section {
            margin-bottom: 30px;
        }

        .search-box {
            width: 100%;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .search-box:focus {
            outline: none;
            border-color: #667eea;
        }

        .employees-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
        }

        .section-title {
            color: #667eea;
            font-size: 1.5em;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .employee-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .employee-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.15);
        }

        .employee-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .employee-name {
            font-size: 1.3em;
            font-weight: 600;
            color: #333;
        }

        .employee-id {
            background: #667eea;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9em;
        }

        .employee-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .detail-item {
            display: flex;
            flex-direction: column;
        }

        .detail-label {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 5px;
        }

        .detail-value {
            font-weight: 600;
            color: #333;
        }

        .allowance-status {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9em;
            font-weight: 600;
            text-align: center;
        }

        .status-due {
            background: #fef3c7;
            color: #92400e;
        }

        .status-overdue {
            background: #fecaca;
            color: #991b1b;
        }

        .status-upcoming {
            background: #dbeafe;
            color: #1e40af;
        }

        .no-results {
            text-align: center;
            padding: 40px;
            color: #666;
            font-size: 1.1em;
        }

        .back-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            font-size: 16px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- زر العودة -->
        <a href="index.html" class="back-btn">
            <i class="fas fa-arrow-right"></i>
            العودة للرئيسية
        </a>

        <!-- العنوان -->
        <div class="header">
            <h1><i class="fas fa-chart-bar"></i> تقارير العلاوات</h1>
            <p>عرض جميع الموظفين وتواريخ استحقاق العلاوات</p>
        </div>

        <!-- الإحصائيات -->
        <div class="stats">
            <div class="stat-card">
                <h3 id="totalEmployees">0</h3>
                <p>إجمالي الموظفين</p>
            </div>
            <div class="stat-card">
                <h3 id="eligibleEmployees">0</h3>
                <p>مستحقين للعلاوة</p>
            </div>
            <div class="stat-card">
                <h3 id="overdueEmployees">0</h3>
                <p>متأخرين عن الموعد</p>
            </div>
        </div>

        <!-- البحث -->
        <div class="search-section">
            <input type="text" id="searchInput" class="search-box" placeholder="🔍 البحث بالاسم أو الوظيفة أو موقع العمل...">
        </div>

        <!-- قائمة الموظفين -->
        <div class="employees-section">
            <h2 class="section-title">
                <i class="fas fa-users"></i>
                قائمة الموظفين والعلاوات المستحقة
            </h2>
            <div id="employeesList">
                <!-- سيتم ملء البيانات هنا -->
            </div>
        </div>
    </div>

    <script>
        let allEmployees = [];
        let filteredEmployees = [];

        // تحميل البيانات عند بدء الصفحة
        window.addEventListener('load', function() {
            loadEmployees();
            setupSearch();
        });

        // تحميل بيانات الموظفين
        function loadEmployees() {
            let employees = JSON.parse(localStorage.getItem('employees') || '[]');
            
            // إذا لم توجد بيانات، إنشاء بيانات تجريبية
            if (employees.length === 0) {
                employees = createSampleEmployees();
                localStorage.setItem('employees', JSON.stringify(employees));
            }
            
            allEmployees = employees;
            filteredEmployees = employees;
            
            updateStats();
            displayEmployees();
        }

        // إنشاء بيانات تجريبية
        function createSampleEmployees() {
            const today = new Date();
            const employees = [];
            
            const employeeData = [
                { name: 'أحمد محمد علي', job: 'مهندس برمجيات', location: 'المقر الرئيسي', salary: '850000' },
                { name: 'فاطمة أحمد حسن', job: 'محاسب', location: 'الفرع الأول', salary: '720000' },
                { name: 'محمد خالد عبدالله', job: 'مدير مبيعات', location: 'الفرع الثاني', salary: '950000' },
                { name: 'سارة علي محمود', job: 'مسؤول موارد بشرية', location: 'المقر الرئيسي', salary: '680000' },
                { name: 'عبدالله حسن أحمد', job: 'مهندس شبكات', location: 'الفرع الثالث', salary: '780000' },
                { name: 'مريم محمد خالد', job: 'مصمم جرافيك', location: 'المقر الرئيسي', salary: '650000' },
                { name: 'خالد عبدالرحمن', job: 'محلل نظم', location: 'الفرع الأول', salary: '820000' },
                { name: 'نور الدين محمد', job: 'مدير مشروع', location: 'الفرع الثاني', salary: '1200000' },
                { name: 'هدى أحمد علي', job: 'مطور ويب', location: 'المقر الرئيسي', salary: '750000' },
                { name: 'يوسف محمد حسن', job: 'مدير تسويق', location: 'الفرع الثالث', salary: '900000' }
            ];
            
            employeeData.forEach((data, index) => {
                // تواريخ مختلفة للعلاوات
                const daysToAdd = Math.floor(Math.random() * 120) - 30; // من -30 إلى +90 يوم
                const nextAllowanceDate = new Date(today);
                nextAllowanceDate.setDate(today.getDate() + daysToAdd);
                
                const lastAllowanceDate = new Date(nextAllowanceDate);
                lastAllowanceDate.setFullYear(lastAllowanceDate.getFullYear() - 1);
                
                const employee = {
                    id: index + 1,
                    name: data.name,
                    jobTitle: data.job,
                    workLocation: data.location,
                    currentSalary: data.salary,
                    currentDegree: Math.floor(Math.random() * 10) + 1,
                    currentStage: Math.floor(Math.random() * 15) + 1,
                    lastAllowanceDate: lastAllowanceDate.toISOString().split('T')[0],
                    nextAllowanceDate: nextAllowanceDate.toISOString().split('T')[0],
                    currentDueDate: nextAllowanceDate.toISOString().split('T')[0],
                    seniority: Math.floor(Math.random() * 20) + 1,
                    birthDate: '1980-01-01',
                    hireDate: '2010-01-01'
                };
                
                employees.push(employee);
            });
            
            return employees;
        }

        // تحديث الإحصائيات
        function updateStats() {
            const today = new Date();
            let eligible = 0;
            let overdue = 0;
            
            allEmployees.forEach(emp => {
                if (emp.nextAllowanceDate) {
                    const dueDate = new Date(emp.nextAllowanceDate);
                    const diffDays = Math.ceil((dueDate - today) / (1000 * 60 * 60 * 24));
                    
                    if (diffDays <= 90) eligible++;
                    if (diffDays < 0) overdue++;
                }
            });
            
            document.getElementById('totalEmployees').textContent = allEmployees.length;
            document.getElementById('eligibleEmployees').textContent = eligible;
            document.getElementById('overdueEmployees').textContent = overdue;
        }

        // عرض الموظفين
        function displayEmployees() {
            const container = document.getElementById('employeesList');
            
            if (filteredEmployees.length === 0) {
                container.innerHTML = `
                    <div class="no-results">
                        <i class="fas fa-search" style="font-size: 3em; margin-bottom: 20px; color: #ddd;"></i>
                        <p>لا توجد نتائج للبحث</p>
                    </div>
                `;
                return;
            }
            
            let html = '';
            
            filteredEmployees.forEach(emp => {
                const status = getAllowanceStatus(emp.nextAllowanceDate);
                const allowanceAmount = calculateAllowance(emp.currentSalary, emp.currentDegree);
                
                html += `
                    <div class="employee-card">
                        <div class="employee-header">
                            <div class="employee-name">${emp.name}</div>
                            <div class="employee-id">#${emp.id}</div>
                        </div>
                        <div class="employee-details">
                            <div class="detail-item">
                                <div class="detail-label">الوظيفة</div>
                                <div class="detail-value">${emp.jobTitle}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">موقع العمل</div>
                                <div class="detail-value">${emp.workLocation}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">الراتب الحالي</div>
                                <div class="detail-value">${formatSalary(emp.currentSalary)}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">الدرجة/المرحلة</div>
                                <div class="detail-value">${emp.currentDegree}/${emp.currentStage}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">تاريخ استحقاق العلاوة</div>
                                <div class="detail-value">${formatDate(emp.nextAllowanceDate)}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">مبلغ العلاوة المستحقة</div>
                                <div class="detail-value">${formatSalary(allowanceAmount)}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">حالة الاستحقاق</div>
                                <div class="allowance-status ${status.class}">${status.text}</div>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }

        // تحديد حالة العلاوة
        function getAllowanceStatus(dateString) {
            if (!dateString) return { text: 'غير محدد', class: 'status-upcoming' };
            
            const today = new Date();
            const dueDate = new Date(dateString);
            const diffDays = Math.ceil((dueDate - today) / (1000 * 60 * 60 * 24));
            
            if (diffDays < 0) {
                return { text: `متأخر ${Math.abs(diffDays)} يوم`, class: 'status-overdue' };
            } else if (diffDays <= 30) {
                return { text: `مستحق خلال ${diffDays} يوم`, class: 'status-due' };
            } else {
                return { text: `مستحق بعد ${diffDays} يوم`, class: 'status-upcoming' };
            }
        }

        // حساب مبلغ العلاوة
        function calculateAllowance(salary, degree) {
            const baseSalary = parseInt(salary) || 0;
            const allowancePercentage = 0.05; // 5% علاوة
            return Math.round(baseSalary * allowancePercentage);
        }

        // تنسيق الراتب
        function formatSalary(amount) {
            const num = parseInt(amount) || 0;
            return num.toLocaleString('ar-IQ') + ' دينار';
        }

        // تنسيق التاريخ
        function formatDate(dateString) {
            if (!dateString) return '-';
            
            const date = new Date(dateString);
            if (isNaN(date.getTime())) return dateString;
            
            const year = date.getFullYear();
            const month = date.getMonth() + 1;
            const day = date.getDate();
            
            const monthNames = [
                'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
            ];
            
            return `${day} ${monthNames[month - 1]} ${year}`;
        }

        // إعداد البحث
        function setupSearch() {
            const searchInput = document.getElementById('searchInput');
            
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase().trim();
                
                if (searchTerm === '') {
                    filteredEmployees = allEmployees;
                } else {
                    filteredEmployees = allEmployees.filter(emp => {
                        return emp.name.toLowerCase().includes(searchTerm) ||
                               emp.jobTitle.toLowerCase().includes(searchTerm) ||
                               emp.workLocation.toLowerCase().includes(searchTerm) ||
                               emp.id.toString().includes(searchTerm);
                    });
                }
                
                displayEmployees();
            });
        }
    </script>
</body>
</html>
