/* أنماط شريط التنقل المحسن */
@import url('variables.css');

/* الهيدر وشريط التنقل */
header {
    background: var(--navbar-bg);
    color: var(--navbar-text);
    padding: 0.8rem 0;
    box-shadow: var(--navbar-shadow);
    position: sticky;
    top: 0;
    z-index: 1000;
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    position: relative;
}

/* شعار التطبيق */
.logo {
    font-size: 1.5rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    color: var(--navbar-text);
    letter-spacing: 0.5px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.logo-icon {
    margin-left: 0.8rem;
    font-size: 1.8rem;
    background-color: var(--icon-bg);
    padding: 0.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    transition: var(--transition);
}

.logo:hover .logo-icon {
    transform: rotate(-15deg);
}

/* روابط التنقل */
.nav-links {
    display: flex;
    align-items: center;
    gap: 0.8rem;
}

.nav-links a {
    padding: 0.7rem 1rem;
    border-radius: 10px;
    transition: var(--transition);
    position: relative;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.95rem;
    color: rgba(255, 255, 255, 0.9);
}

.nav-links a i {
    font-size: 1.1rem;
}

.nav-links a:hover {
    background-color: var(--navbar-hover);
    color: var(--navbar-active);
    transform: translateY(-2px);
}

.nav-links a.active {
    background-color: var(--navbar-active-bg);
    color: var(--navbar-active);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    font-weight: 600;
}

/* القوائم المنسدلة */
.dropdown {
    position: relative;
}

.dropdown-toggle {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.dropdown-toggle .fa-chevron-down {
    font-size: 0.8rem;
    margin-right: 0.3rem;
    transition: transform 0.3s ease;
}

.dropdown:hover .fa-chevron-down {
    transform: rotate(180deg);
}

.dropdown-menu {
    position: absolute;
    top: 120%;
    right: 0;
    background-color: var(--dropdown-bg);
    border-radius: 12px;
    box-shadow: var(--dropdown-shadow);
    min-width: 240px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.3s ease;
    z-index: 100;
    padding: 0.8rem 0;
    border: 1px solid var(--dropdown-border);
    overflow: hidden;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
    top: 100%;
}

.dropdown-menu a {
    color: var(--dropdown-text) !important;
    padding: 0.8rem 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.8rem;
    border-radius: 0;
    transition: all 0.2s ease;
    border-right: 3px solid transparent;
}

.dropdown-menu a:hover {
    background-color: var(--dropdown-hover);
    color: var(--dropdown-hover-text) !important;
    border-right-color: var(--dropdown-hover-text);
    padding-right: 2rem;
}

.dropdown-menu a i {
    color: var(--dropdown-hover-text);
    font-size: 1.1rem;
    transition: var(--transition);
}

.dropdown-menu a:hover i {
    transform: translateX(-5px);
}

/* تأثير السهم للقائمة المنسدلة */
.dropdown-menu::before {
    content: '';
    position: absolute;
    top: -8px;
    right: 20px;
    width: 16px;
    height: 16px;
    background-color: var(--dropdown-bg);
    transform: rotate(45deg);
    border-radius: 2px;
    border-top: 1px solid var(--dropdown-border);
    border-right: 1px solid var(--dropdown-border);
}

/* زر القائمة للأجهزة المحمولة */
.menu-toggle {
    display: none;
    font-size: 1.4rem;
    cursor: pointer;
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background-color: var(--icon-bg);
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.menu-toggle:hover {
    background-color: rgba(255, 255, 255, 0.25);
}

/* تصميم متجاوب للقائمة */
@media (max-width: 1200px) {
    .nav-links {
        gap: 0.2rem;
    }

    .nav-links a {
        padding: 0.7rem 0.8rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 992px) {
    .logo {
        font-size: 1.3rem;
    }

    .nav-links {
        display: none;
        position: absolute;
        top: 100%;
        right: 0;
        left: 0;
        flex-direction: column;
        background-color: var(--navbar-bg);
        border-radius: 0 0 15px 15px;
        padding: 1rem;
        box-shadow: var(--navbar-shadow);
        z-index: 100;
        margin-top: 1rem;
        gap: 0.5rem;
        max-height: 80vh;
        overflow-y: auto;
        align-items: stretch;
    }

    .nav-links.active {
        display: flex;
    }

    .nav-links a {
        width: 100%;
        border-radius: 10px;
    }

    .menu-toggle {
        display: flex;
    }

    .menu-toggle.active {
        background-color: rgba(255, 255, 255, 0.3);
    }

    /* تعديلات القوائم المنسدلة للأجهزة المحمولة */
    .dropdown {
        width: 100%;
    }

    .dropdown-menu {
        position: static;
        opacity: 0;
        visibility: hidden;
        height: 0;
        transform: none;
        box-shadow: none;
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 10px;
        margin: 0.5rem 0;
        padding: 0;
        transition: all 0.3s ease;
        overflow: hidden;
        border: none;
    }

    .dropdown.mobile-active .dropdown-menu {
        opacity: 1;
        visibility: visible;
        height: auto;
        padding: 0.5rem 0;
    }

    .dropdown-menu::before {
        display: none;
    }

    .dropdown-menu a {
        color: rgba(255, 255, 255, 0.9) !important;
        padding: 0.8rem 1rem 0.8rem 2rem;
        border-right: none;
        border-right: 3px solid transparent;
    }

    .dropdown-menu a:hover {
        background-color: rgba(255, 255, 255, 0.15);
        color: white !important;
        border-right-color: white;
    }

    .dropdown-menu a i {
        color: rgba(255, 255, 255, 0.9);
    }
}
