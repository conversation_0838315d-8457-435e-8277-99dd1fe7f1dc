<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح البحث - نظام إدارة العلاوات والترفيعات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #ef4444;
        }

        .header h1 {
            color: #ef4444;
            margin-bottom: 10px;
        }

        .fix-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: #f9f9f9;
        }

        .fix-section h2 {
            color: #ef4444;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .btn {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
            font-size: 16px;
            font-weight: 600;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn.success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }

        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            background: white;
            border-left: 4px solid #ef4444;
        }

        .success {
            border-left-color: #10b981;
            background: #f0fdf4;
            color: #065f46;
        }

        .error {
            border-left-color: #ef4444;
            background: #fef2f2;
            color: #991b1b;
        }

        .search-demo {
            background: white;
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }

        .search-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            margin-bottom: 15px;
        }

        .employee-item {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            margin: 5px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .employee-item.highlight {
            background: #fef3c7;
            border-color: #f59e0b;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-search-plus"></i> إصلاح البحث في التقارير</h1>
            <p>حل مشكلة عدم ظهور نتائج البحث</p>
        </div>

        <!-- إصلاح البيانات -->
        <div class="fix-section">
            <h2><i class="fas fa-database"></i> إصلاح البيانات</h2>
            <button class="btn" onclick="fixData()">إصلاح وإنشاء البيانات</button>
            <button class="btn" onclick="checkData()">فحص البيانات الحالية</button>
            <div id="dataResult" class="result" style="display: none;"></div>
        </div>

        <!-- اختبار البحث -->
        <div class="fix-section">
            <h2><i class="fas fa-vial"></i> اختبار البحث</h2>
            <div class="search-demo">
                <input type="text" id="testSearch" class="search-input" placeholder="جرب البحث هنا...">
                <div id="searchResults"></div>
            </div>
        </div>

        <!-- إصلاح صفحة التقارير -->
        <div class="fix-section">
            <h2><i class="fas fa-tools"></i> إصلاح صفحة التقارير</h2>
            <button class="btn success" onclick="goToReports()">فتح صفحة التقارير</button>
            <button class="btn" onclick="reloadReports()">إعادة تحميل التقارير</button>
            <div id="reportsResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        let allEmployees = [];

        // إصلاح البيانات
        function fixData() {
            const resultDiv = document.getElementById('dataResult');
            resultDiv.style.display = 'block';
            
            try {
                // إنشاء بيانات جديدة مضمونة
                const employees = createGuaranteedEmployees();
                localStorage.setItem('employees', JSON.stringify(employees));
                allEmployees = employees;
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <h4>✅ تم إصلاح البيانات بنجاح!</h4>
                    <p><strong>عدد الموظفين:</strong> ${employees.length}</p>
                    <p><strong>أول موظف:</strong> ${employees[0].name}</p>
                    <p><strong>وظيفة أول موظف:</strong> ${employees[0].jobTitle}</p>
                    <p><strong>موقع عمل أول موظف:</strong> ${employees[0].workLocation}</p>
                `;
                
                updateSearchDemo();
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<h4>❌ خطأ: ${error.message}</h4>`;
            }
        }

        // فحص البيانات
        function checkData() {
            const resultDiv = document.getElementById('dataResult');
            resultDiv.style.display = 'block';
            
            try {
                const employees = JSON.parse(localStorage.getItem('employees') || '[]');
                allEmployees = employees;
                
                if (employees.length === 0) {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = '<h4>❌ لا توجد بيانات! اضغط "إصلاح وإنشاء البيانات"</h4>';
                } else {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h4>✅ البيانات موجودة!</h4>
                        <p><strong>عدد الموظفين:</strong> ${employees.length}</p>
                        <p><strong>عينة من الأسماء:</strong></p>
                        <ul>
                            ${employees.slice(0, 5).map(emp => `<li>${emp.name} - ${emp.jobTitle}</li>`).join('')}
                        </ul>
                    `;
                }
                
                updateSearchDemo();
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<h4>❌ خطأ: ${error.message}</h4>`;
            }
        }

        // تحديث عرض البحث
        function updateSearchDemo() {
            const searchResults = document.getElementById('searchResults');
            
            if (allEmployees.length === 0) {
                searchResults.innerHTML = '<p>لا توجد بيانات للبحث فيها</p>';
                return;
            }
            
            let html = '<h4>جميع الموظفين:</h4>';
            allEmployees.forEach(emp => {
                html += `
                    <div class="employee-item" data-name="${emp.name}" data-job="${emp.jobTitle}" data-location="${emp.workLocation}">
                        <span><strong>${emp.name}</strong> - ${emp.jobTitle} - ${emp.workLocation}</span>
                        <span>#${emp.id}</span>
                    </div>
                `;
            });
            
            searchResults.innerHTML = html;
        }

        // البحث التجريبي
        document.getElementById('testSearch').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const employeeItems = document.querySelectorAll('.employee-item');
            
            employeeItems.forEach(item => {
                const name = item.getAttribute('data-name').toLowerCase();
                const job = item.getAttribute('data-job').toLowerCase();
                const location = item.getAttribute('data-location').toLowerCase();
                
                if (searchTerm === '' || name.includes(searchTerm) || job.includes(searchTerm) || location.includes(searchTerm)) {
                    item.style.display = 'flex';
                    item.classList.toggle('highlight', searchTerm !== '' && (name.includes(searchTerm) || job.includes(searchTerm) || location.includes(searchTerm)));
                } else {
                    item.style.display = 'none';
                    item.classList.remove('highlight');
                }
            });
        });

        // فتح صفحة التقارير
        function goToReports() {
            window.open('allowance-reports.html', '_blank');
        }

        // إعادة تحميل التقارير
        function reloadReports() {
            const resultDiv = document.getElementById('reportsResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result success';
            resultDiv.innerHTML = `
                <h4>✅ تم إعداد البيانات للتقارير!</h4>
                <p>الآن افتح صفحة التقارير وجرب البحث</p>
                <p><strong>كلمات للبحث:</strong> أحمد، مهندس، المقر، محاسب</p>
            `;
        }

        // إنشاء بيانات مضمونة
        function createGuaranteedEmployees() {
            const today = new Date();
            const employees = [];
            
            const employeeData = [
                { name: 'أحمد محمد علي', job: 'مهندس برمجيات', location: 'المقر الرئيسي' },
                { name: 'فاطمة أحمد حسن', job: 'محاسب', location: 'الفرع الأول' },
                { name: 'محمد خالد عبدالله', job: 'مدير مبيعات', location: 'الفرع الثاني' },
                { name: 'سارة علي محمود', job: 'مسؤول موارد بشرية', location: 'المقر الرئيسي' },
                { name: 'عبدالله حسن أحمد', job: 'مهندس شبكات', location: 'الفرع الثالث' },
                { name: 'مريم محمد خالد', job: 'مصمم جرافيك', location: 'المقر الرئيسي' },
                { name: 'خالد عبدالرحمن', job: 'محلل نظم', location: 'الفرع الأول' },
                { name: 'نور الدين محمد', job: 'مدير مشروع', location: 'الفرع الثاني' },
                { name: 'هدى أحمد علي', job: 'مطور ويب', location: 'المقر الرئيسي' },
                { name: 'يوسف محمد حسن', job: 'مدير تسويق', location: 'الفرع الثالث' }
            ];
            
            employeeData.forEach((data, index) => {
                const daysToAdd = Math.floor(Math.random() * 60) + 10; // 10-70 يوم
                const nextAllowanceDate = new Date(today);
                nextAllowanceDate.setDate(today.getDate() + daysToAdd);
                
                const lastAllowanceDate = new Date(nextAllowanceDate);
                lastAllowanceDate.setFullYear(lastAllowanceDate.getFullYear() - 1);
                
                const employee = {
                    id: index + 1,
                    name: data.name,
                    jobTitle: data.job,
                    workLocation: data.location,
                    currentDegree: Math.floor(Math.random() * 10) + 1,
                    currentStage: Math.floor(Math.random() * 15) + 1,
                    currentSalary: (Math.floor(Math.random() * 500000) + 300000).toString(),
                    lastAllowanceDate: lastAllowanceDate.toISOString().split('T')[0],
                    nextAllowanceDate: nextAllowanceDate.toISOString().split('T')[0],
                    currentDueDate: nextAllowanceDate.toISOString().split('T')[0],
                    seniority: Math.floor(Math.random() * 20) + 1,
                    birthDate: '1980-01-01',
                    hireDate: '2010-01-01',
                    jobDescription: index % 3 === 0 ? 'teaching' : index % 3 === 1 ? 'technical' : 'administrative'
                };
                
                employees.push(employee);
            });
            
            return employees;
        }

        // تحميل البيانات عند بدء الصفحة
        window.addEventListener('load', function() {
            checkData();
        });
    </script>
</body>
</html>
