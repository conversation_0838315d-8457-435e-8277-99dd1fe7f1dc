'use client';

import { useState } from 'react';
import Link from 'next/link';

export default function AllowancesPage() {
  const [activeTab, setActiveTab] = useState('pending');
  const [searchQuery, setSearchQuery] = useState('');

  // بيانات تجريبية للعلاوات
  const allowances = {
    pending: [
      {
        id: 1,
        employeeName: 'أحمد محمد علي',
        employeeNumber: 'EMP001',
        currentSalary: 8000,
        allowanceAmount: 500,
        allowanceType: 'علاوة سنوية',
        dueDate: '2025-02-15',
        yearsOfService: 3,
        lastAllowance: '2024-02-15'
      },
      {
        id: 2,
        employeeName: 'سارة أحمد محمود',
        employeeNumber: 'EMP002',
        currentSalary: 7500,
        allowanceAmount: 450,
        allowanceType: 'علاوة سنوية',
        dueDate: '2025-03-10',
        yearsOfService: 2,
        lastAllowance: '2024-03-10'
      },
      {
        id: 3,
        employeeName: 'محمد خالد عبدالله',
        employeeNumber: 'EMP003',
        currentSalary: 9500,
        allowanceAmount: 600,
        allowanceType: 'علاوة استثنائية',
        dueDate: '2025-01-20',
        yearsOfService: 5,
        lastAllowance: '2024-01-20'
      }
    ],
    approved: [
      {
        id: 4,
        employeeName: 'فاطمة علي حسن',
        employeeNumber: 'EMP004',
        currentSalary: 8500,
        allowanceAmount: 550,
        allowanceType: 'علاوة سنوية',
        approvedDate: '2025-01-10',
        effectiveDate: '2025-01-15'
      },
      {
        id: 5,
        employeeName: 'خالد عبدالرحمن',
        employeeNumber: 'EMP005',
        currentSalary: 7800,
        allowanceAmount: 480,
        allowanceType: 'علاوة سنوية',
        approvedDate: '2025-01-05',
        effectiveDate: '2025-01-10'
      }
    ]
  };

  const filteredAllowances = allowances[activeTab as keyof typeof allowances].filter(
    (allowance) =>
      allowance.employeeName.includes(searchQuery) ||
      allowance.employeeNumber.includes(searchQuery)
  );

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA');
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">إدارة العلاوات</h1>
            <p className="text-gray-600 mt-2">متابعة وإدارة علاوات الموظفين</p>
          </div>
          <div className="flex space-x-4 space-x-reverse">
            <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
              إضافة علاوة جديدة
            </button>
            <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
              تصدير التقرير
            </button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-4 rounded-lg">
            <div className="flex items-center">
              <div className="bg-white bg-opacity-20 p-3 rounded-full mr-4">
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z"/>
                </svg>
              </div>
              <div>
                <p className="text-2xl font-bold">32</p>
                <p className="text-blue-100">العلاوات المستحقة</p>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-r from-green-500 to-green-600 text-white p-4 rounded-lg">
            <div className="flex items-center">
              <div className="bg-white bg-opacity-20 p-3 rounded-full mr-4">
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"/>
                </svg>
              </div>
              <div>
                <p className="text-2xl font-bold">25</p>
                <p className="text-green-100">العلاوات المعتمدة</p>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-r from-purple-500 to-purple-600 text-white p-4 rounded-lg">
            <div className="flex items-center">
              <div className="bg-white bg-opacity-20 p-3 rounded-full mr-4">
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"/>
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clipRule="evenodd"/>
                </svg>
              </div>
              <div>
                <p className="text-2xl font-bold">15,750</p>
                <p className="text-purple-100">إجمالي العلاوات (ريال)</p>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-r from-orange-500 to-orange-600 text-white p-4 rounded-lg">
            <div className="flex items-center">
              <div className="bg-white bg-opacity-20 p-3 rounded-full mr-4">
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd"/>
                </svg>
              </div>
              <div>
                <p className="text-2xl font-bold">8</p>
                <p className="text-orange-100">علاوات هذا الشهر</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="bg-white rounded-lg shadow-md">
        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 space-x-reverse px-6">
            <button
              onClick={() => setActiveTab('pending')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'pending'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              العلاوات المستحقة ({allowances.pending.length})
            </button>
            <button
              onClick={() => setActiveTab('approved')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'approved'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              العلاوات المعتمدة ({allowances.approved.length})
            </button>
          </nav>
        </div>

        {/* Search */}
        <div className="p-6 border-b border-gray-200">
          <div className="relative max-w-md">
            <input
              type="text"
              placeholder="البحث عن موظف..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {activeTab === 'pending' && (
            <div className="space-y-4">
              {filteredAllowances.map((allowance) => (
                <div key={allowance.id} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">{allowance.employeeName}</h3>
                        <span className="mr-2 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                          {allowance.employeeNumber}
                        </span>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                        <div>
                          <span className="font-medium">الراتب الحالي:</span> {formatCurrency(allowance.currentSalary)}
                        </div>
                        <div>
                          <span className="font-medium">مبلغ العلاوة:</span> {formatCurrency(allowance.allowanceAmount)}
                        </div>
                        <div>
                          <span className="font-medium">نوع العلاوة:</span> {allowance.allowanceType}
                        </div>
                        <div>
                          <span className="font-medium">تاريخ الاستحقاق:</span> {formatDate(allowance.dueDate)}
                        </div>
                        <div>
                          <span className="font-medium">سنوات الخدمة:</span> {allowance.yearsOfService} سنوات
                        </div>
                        <div>
                          <span className="font-medium">آخر علاوة:</span> {formatDate(allowance.lastAllowance)}
                        </div>
                      </div>
                    </div>
                    <div className="flex space-x-2 space-x-reverse">
                      <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors text-sm">
                        اعتماد
                      </button>
                      <button className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors text-sm">
                        تأجيل
                      </button>
                      <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm">
                        تفاصيل
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {activeTab === 'approved' && (
            <div className="space-y-4">
              {filteredAllowances.map((allowance) => (
                <div key={allowance.id} className="border border-gray-200 rounded-lg p-6 bg-green-50">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">{allowance.employeeName}</h3>
                        <span className="mr-2 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                          {allowance.employeeNumber}
                        </span>
                        <span className="mr-2 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                          معتمد
                        </span>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                        <div>
                          <span className="font-medium">الراتب الحالي:</span> {formatCurrency(allowance.currentSalary)}
                        </div>
                        <div>
                          <span className="font-medium">مبلغ العلاوة:</span> {formatCurrency(allowance.allowanceAmount)}
                        </div>
                        <div>
                          <span className="font-medium">نوع العلاوة:</span> {allowance.allowanceType}
                        </div>
                        <div>
                          <span className="font-medium">تاريخ الاعتماد:</span> {formatDate(allowance.approvedDate)}
                        </div>
                        <div>
                          <span className="font-medium">تاريخ السريان:</span> {formatDate(allowance.effectiveDate)}
                        </div>
                      </div>
                    </div>
                    <div className="flex space-x-2 space-x-reverse">
                      <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm">
                        طباعة
                      </button>
                      <button className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors text-sm">
                        تفاصيل
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}