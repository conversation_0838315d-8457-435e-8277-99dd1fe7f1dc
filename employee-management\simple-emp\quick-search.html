<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>البحث السريع عن الموظفين - نظام إدارة العلاوات والترفيعات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #667eea;
        }

        .header h1 {
            color: #667eea;
            margin-bottom: 10px;
            font-size: 2.2em;
        }

        .search-section {
            margin-bottom: 30px;
        }

        .search-box {
            width: 100%;
            padding: 20px;
            border: 3px solid #667eea;
            border-radius: 15px;
            font-size: 18px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .search-box:focus {
            outline: none;
            border-color: #764ba2;
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
        }

        .quick-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .quick-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .quick-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .results-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            min-height: 200px;
        }

        .employee-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .employee-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.15);
            border-color: #667eea;
        }

        .employee-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .employee-name {
            font-size: 1.3em;
            font-weight: 600;
            color: #333;
        }

        .employee-id {
            background: #667eea;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9em;
        }

        .employee-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .detail-item {
            display: flex;
            flex-direction: column;
        }

        .detail-label {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 5px;
        }

        .detail-value {
            font-weight: 600;
            color: #333;
        }

        .no-results {
            text-align: center;
            padding: 40px;
            color: #666;
            font-size: 1.1em;
        }

        .back-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            font-size: 16px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .stats {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .stat-item {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- زر العودة -->
        <a href="index.html" class="back-btn">
            <i class="fas fa-arrow-right"></i>
            العودة للرئيسية
        </a>

        <!-- العنوان -->
        <div class="header">
            <h1><i class="fas fa-search"></i> البحث السريع عن الموظفين</h1>
            <p>ابحث عن أي موظف بالاسم، الوظيفة، أو موقع العمل</p>
        </div>

        <!-- مربع البحث -->
        <div class="search-section">
            <input type="text" id="searchInput" class="search-box" placeholder="🔍 اكتب اسم الموظف، الوظيفة، أو موقع العمل...">
        </div>

        <!-- أزرار البحث السريع -->
        <div class="quick-buttons">
            <button class="quick-btn" onclick="quickSearch('مهندس')">
                <i class="fas fa-cogs"></i>
                المهندسين
            </button>
            <button class="quick-btn" onclick="quickSearch('محاسب')">
                <i class="fas fa-calculator"></i>
                المحاسبين
            </button>
            <button class="quick-btn" onclick="quickSearch('مدير')">
                <i class="fas fa-user-tie"></i>
                المديرين
            </button>
            <button class="quick-btn" onclick="quickSearch('المقر')">
                <i class="fas fa-building"></i>
                المقر الرئيسي
            </button>
            <button class="quick-btn" onclick="showAll()">
                <i class="fas fa-users"></i>
                جميع الموظفين
            </button>
        </div>

        <!-- الإحصائيات -->
        <div class="stats">
            <div class="stat-item">
                <i class="fas fa-users"></i>
                إجمالي الموظفين: <span id="totalCount">0</span>
            </div>
            <div class="stat-item">
                <i class="fas fa-search"></i>
                نتائج البحث: <span id="resultCount">0</span>
            </div>
        </div>

        <!-- النتائج -->
        <div class="results-section">
            <div id="resultsContainer">
                <div class="no-results">
                    <i class="fas fa-search" style="font-size: 3em; margin-bottom: 20px; color: #ddd;"></i>
                    <p>ابدأ البحث لعرض النتائج</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        let allEmployees = [];
        let filteredEmployees = [];

        // تحميل البيانات عند بدء الصفحة
        window.addEventListener('load', function() {
            loadEmployees();
            setupSearch();
        });

        // تحميل بيانات الموظفين
        function loadEmployees() {
            let employees = JSON.parse(localStorage.getItem('employees') || '[]');
            
            // إذا لم توجد بيانات، إنشاء بيانات تجريبية
            if (employees.length === 0) {
                employees = createSampleEmployees();
                localStorage.setItem('employees', JSON.stringify(employees));
            }
            
            allEmployees = employees;
            updateStats();
        }

        // إنشاء بيانات تجريبية
        function createSampleEmployees() {
            const today = new Date();
            const employees = [];
            
            const employeeData = [
                { name: 'أحمد محمد علي', job: 'مهندس برمجيات', location: 'المقر الرئيسي', salary: '850000' },
                { name: 'فاطمة أحمد حسن', job: 'محاسب', location: 'الفرع الأول', salary: '720000' },
                { name: 'محمد خالد عبدالله', job: 'مدير مبيعات', location: 'الفرع الثاني', salary: '950000' },
                { name: 'سارة علي محمود', job: 'مسؤول موارد بشرية', location: 'المقر الرئيسي', salary: '680000' },
                { name: 'عبدالله حسن أحمد', job: 'مهندس شبكات', location: 'الفرع الثالث', salary: '780000' },
                { name: 'مريم محمد خالد', job: 'مصمم جرافيك', location: 'المقر الرئيسي', salary: '650000' },
                { name: 'خالد عبدالرحمن', job: 'محلل نظم', location: 'الفرع الأول', salary: '820000' },
                { name: 'نور الدين محمد', job: 'مدير مشروع', location: 'الفرع الثاني', salary: '1200000' },
                { name: 'هدى أحمد علي', job: 'مطور ويب', location: 'المقر الرئيسي', salary: '750000' },
                { name: 'يوسف محمد حسن', job: 'مدير تسويق', location: 'الفرع الثالث', salary: '900000' }
            ];
            
            employeeData.forEach((data, index) => {
                const employee = {
                    id: index + 1,
                    name: data.name,
                    jobTitle: data.job,
                    workLocation: data.location,
                    currentSalary: data.salary,
                    currentDegree: Math.floor(Math.random() * 10) + 1,
                    currentStage: Math.floor(Math.random() * 15) + 1,
                    seniority: Math.floor(Math.random() * 20) + 1,
                    birthDate: '1980-01-01',
                    hireDate: '2010-01-01'
                };
                
                employees.push(employee);
            });
            
            return employees;
        }

        // تحديث الإحصائيات
        function updateStats() {
            document.getElementById('totalCount').textContent = allEmployees.length;
            document.getElementById('resultCount').textContent = filteredEmployees.length;
        }

        // عرض الموظفين
        function displayEmployees(employees) {
            const container = document.getElementById('resultsContainer');
            
            if (employees.length === 0) {
                container.innerHTML = `
                    <div class="no-results">
                        <i class="fas fa-search" style="font-size: 3em; margin-bottom: 20px; color: #ddd;"></i>
                        <p>لا توجد نتائج للبحث</p>
                    </div>
                `;
                return;
            }
            
            let html = '';
            
            employees.forEach(emp => {
                html += `
                    <div class="employee-card" onclick="viewEmployeeDetails(${emp.id})">
                        <div class="employee-header">
                            <div class="employee-name">${emp.name}</div>
                            <div class="employee-id">#${emp.id}</div>
                        </div>
                        <div class="employee-details">
                            <div class="detail-item">
                                <div class="detail-label">الوظيفة</div>
                                <div class="detail-value">${emp.jobTitle}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">موقع العمل</div>
                                <div class="detail-value">${emp.workLocation}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">الراتب</div>
                                <div class="detail-value">${formatSalary(emp.currentSalary)}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">الدرجة/المرحلة</div>
                                <div class="detail-value">${emp.currentDegree}/${emp.currentStage}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">سنوات الخدمة</div>
                                <div class="detail-value">${emp.seniority} سنة</div>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }

        // البحث السريع
        function quickSearch(term) {
            document.getElementById('searchInput').value = term;
            performSearch(term);
        }

        // عرض جميع الموظفين
        function showAll() {
            document.getElementById('searchInput').value = '';
            filteredEmployees = allEmployees;
            displayEmployees(filteredEmployees);
            updateStats();
        }

        // تنفيذ البحث
        function performSearch(searchTerm) {
            if (!searchTerm || searchTerm.trim() === '') {
                filteredEmployees = [];
                displayEmployees(filteredEmployees);
                updateStats();
                return;
            }
            
            const term = searchTerm.toLowerCase().trim();
            
            filteredEmployees = allEmployees.filter(emp => {
                return emp.name.toLowerCase().includes(term) ||
                       emp.jobTitle.toLowerCase().includes(term) ||
                       emp.workLocation.toLowerCase().includes(term) ||
                       emp.id.toString().includes(term);
            });
            
            displayEmployees(filteredEmployees);
            updateStats();
        }

        // إعداد البحث
        function setupSearch() {
            const searchInput = document.getElementById('searchInput');
            
            searchInput.addEventListener('input', function() {
                performSearch(this.value);
            });
        }

        // عرض تفاصيل الموظف
        function viewEmployeeDetails(employeeId) {
            // فتح صفحة تفاصيل الموظف
            window.open(`employee-details.html?id=${employeeId}`, '_blank');
        }

        // تنسيق الراتب
        function formatSalary(amount) {
            if (!amount) return '-';
            const num = parseInt(amount) || 0;
            return num.toLocaleString('ar-IQ') + ' دينار';
        }
    </script>
</body>
</html>
