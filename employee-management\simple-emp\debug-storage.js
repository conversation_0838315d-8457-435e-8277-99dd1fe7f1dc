/**
 * ملف للتحقق من بيانات التخزين المحلي وإدارتها
 */

// عرض محتويات التخزين المحلي في وحدة التحكم
function displayLocalStorage() {
    console.log('=== محتويات التخزين المحلي ===');

    // عرض العناوين الوظيفية
    const jobTitles = JSON.parse(localStorage.getItem('jobTitles') || '[]');
    console.log('العناوين الوظيفية:', jobTitles);

    // عرض مواقع العمل
    const workLocations = JSON.parse(localStorage.getItem('workLocations') || '[]');
    console.log('مواقع العمل:', workLocations);

    // عرض التحصيل الدراسي
    const educationLevels = JSON.parse(localStorage.getItem('educationLevels') || '[]');
    console.log('التحصيل الدراسي:', educationLevels);

    // عرض الموظفين
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');
    console.log('الموظفين:', employees);

    console.log('=== نهاية محتويات التخزين المحلي ===');
}

// إعادة تعيين العناوين الوظيفية إلى قائمة فارغة
function resetJobTitles() {
    const defaultJobTitles = [];

    localStorage.setItem('jobTitles', JSON.stringify(defaultJobTitles));
    console.log('تم إعادة تعيين العناوين الوظيفية إلى قائمة فارغة');
    displayLocalStorage();
}

// إعادة تعيين مواقع العمل إلى القيم الافتراضية
function resetWorkLocations() {
    const defaultWorkLocations = [
        { id: 1, name: 'كلية الهندسة' },
        { id: 2, name: 'كلية العلوم' },
        { id: 3, name: 'كلية الطب' },
        { id: 4, name: 'كلية الآداب' },
        { id: 5, name: 'رئاسة الجامعة' }
    ];

    localStorage.setItem('workLocations', JSON.stringify(defaultWorkLocations));
    console.log('تم إعادة تعيين مواقع العمل إلى القيم الافتراضية');
    displayLocalStorage();
}

// إعادة تعيين التحصيل الدراسي إلى القيم الافتراضية
function resetEducationLevels() {
    const defaultEducationLevels = [
        { id: 1, name: 'دبلوم' },
        { id: 2, name: 'بكالوريوس' },
        { id: 3, name: 'ماجستير' },
        { id: 4, name: 'دكتوراه' }
    ];

    localStorage.setItem('educationLevels', JSON.stringify(defaultEducationLevels));
    console.log('تم إعادة تعيين التحصيل الدراسي إلى القيم الافتراضية');
    displayLocalStorage();
}

// إعادة تعيين جميع البيانات إلى القيم الافتراضية
function resetAllData() {
    resetJobTitles();
    resetWorkLocations();
    resetEducationLevels();
    localStorage.removeItem('employees');
    console.log('تم إعادة تعيين جميع البيانات إلى القيم الافتراضية');
    displayLocalStorage();
}

// عرض محتويات التخزين المحلي عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    displayLocalStorage();

    // تم إزالة أدوات التصحيح من الواجهة
    // يمكن استدعاء الوظائف من وحدة التحكم عند الحاجة
});

// استخراج وإنشاء العناوين الوظيفية ومواقع العمل من بيانات الموظفين
function extractProfileDataFromEmployees() {
    console.log('بدء استخراج البيانات من الموظفين...');

    // الحصول على بيانات الموظفين
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');
    if (employees.length === 0) {
        console.log('لا يوجد موظفين في النظام');
        return;
    }

    // مجموعات للبيانات الفريدة
    const uniqueJobTitles = new Set();
    const uniqueWorkLocations = new Set();
    const uniqueEducationLevels = new Set();

    // استخراج البيانات من كل موظف
    employees.forEach(employee => {
        if (employee.jobTitle) uniqueJobTitles.add(employee.jobTitle);
        if (employee.workLocation) uniqueWorkLocations.add(employee.workLocation);
        if (employee.education) uniqueEducationLevels.add(employee.education);
    });

    // تحويل العناوين الوظيفية إلى التنسيق المطلوب
    const jobTitles = Array.from(uniqueJobTitles).map((title, index) => ({
        id: index + 1,
        name: title,
        category: determineJobCategory(title),
        newJobTitle: null
    }));

    // تحويل مواقع العمل إلى التنسيق المطلوب
    const workLocations = Array.from(uniqueWorkLocations).map((location, index) => ({
        id: index + 1,
        name: location
    }));

    // تحويل التحصيل الدراسي إلى التنسيق المطلوب
    const educationLevels = Array.from(uniqueEducationLevels).map((education, index) => ({
        id: index + 1,
        name: education
    }));

    // حفظ البيانات في التخزين المحلي
    if (jobTitles.length > 0) {
        localStorage.setItem('jobTitles', JSON.stringify(jobTitles));
        console.log('تم حفظ العناوين الوظيفية:', jobTitles);
    }

    if (workLocations.length > 0) {
        localStorage.setItem('workLocations', JSON.stringify(workLocations));
        console.log('تم حفظ مواقع العمل:', workLocations);
    }

    if (educationLevels.length > 0) {
        localStorage.setItem('educationLevels', JSON.stringify(educationLevels));
        console.log('تم حفظ التحصيل الدراسي:', educationLevels);
    }

    // تحديث مؤشرات التحديث
    localStorage.setItem('jobTitlesUpdated', new Date().toISOString());
    localStorage.setItem('workLocationsUpdated', new Date().toISOString());
    localStorage.setItem('educationLevelsUpdated', new Date().toISOString());
    localStorage.setItem('needsUpdate', 'true');

    console.log('تم استخراج وحفظ البيانات بنجاح');
}

// تحديد فئة العنوان الوظيفي
function determineJobCategory(title) {
    title = title.toLowerCase();
    
    // الفئات التدريسية
    if (title.includes('مدرس') || 
        title.includes('استاذ') || 
        title.includes('أستاذ') || 
        title.includes('دكتور') ||
        title.includes('معيد')) {
        return 'teaching';
    }
    
    // الفئات الفنية
    if (title.includes('فني') || 
        title.includes('تقني') || 
        title.includes('مختبر') ||
        title.includes('مهندس')) {
        return 'technical';
    }
    
    // الفئات الإدارية (الافتراضي)
    return 'administrative';
}

// تنظيف البيانات التعريفية
function clearProfileData() {
    localStorage.removeItem('jobTitles');
    localStorage.removeItem('workLocations');
    localStorage.removeItem('educationLevels');
    localStorage.setItem('needsUpdate', 'true');
    console.log('تم مسح البيانات التعريفية');
}

// وظيفة تنظيف وإعادة تهيئة التخزين المحلي
function resetAndInitializeStorage() {
    try {
        // حفظ نسخة احتياطية من البيانات الحالية
        const backup = {
            employees: localStorage.getItem('employees'),
            jobTitles: localStorage.getItem('jobTitles'),
            thanks: localStorage.getItem('thanks'),
            penalties: localStorage.getItem('penalties'),
            leaves: localStorage.getItem('leaves')
        };

        // مسح التخزين المحلي
        localStorage.clear();

        // إعادة تهيئة البيانات الأساسية
        if (backup.jobTitles) {
            localStorage.setItem('jobTitles', backup.jobTitles);
        } else {
            // تهيئة العناوين الوظيفية الافتراضية
            const defaultJobTitles = [
                { id: 1, name: 'مدرس مساعد', category: 'teaching', newJobTitle: 'مدرس' },
                { id: 2, name: 'مدرس', category: 'teaching', newJobTitle: 'أستاذ مساعد' },
                { id: 3, name: 'أستاذ مساعد', category: 'teaching', newJobTitle: 'أستاذ' },
                { id: 4, name: 'أستاذ', category: 'teaching', newJobTitle: 'أستاذ' },
                { id: 5, name: 'فني', category: 'technical', newJobTitle: 'فني أول' },
                { id: 6, name: 'مهندس', category: 'technical', newJobTitle: 'مهندس أول' },
                { id: 7, name: 'موظف إداري', category: 'administrative', newJobTitle: 'رئيس قسم' },
                { id: 8, name: 'رئيس قسم', category: 'administrative', newJobTitle: 'مدير' }
            ];
            localStorage.setItem('jobTitles', JSON.stringify(defaultJobTitles));
        }

        // استعادة بيانات الموظفين إذا كانت صالحة
        if (backup.employees) {
            try {
                const employees = JSON.parse(backup.employees);
                if (Array.isArray(employees)) {
                    localStorage.setItem('employees', backup.employees);
                }
            } catch (e) {
                console.error('خطأ في استعادة بيانات الموظفين:', e);
            }
        }

        // استعادة البيانات الأخرى إذا كانت موجودة
        if (backup.thanks) localStorage.setItem('thanks', backup.thanks);
        if (backup.penalties) localStorage.setItem('penalties', backup.penalties);
        if (backup.leaves) localStorage.setItem('leaves', backup.leaves);

        return { success: true, message: 'تم إعادة تهيئة التخزين المحلي بنجاح' };
    } catch (error) {
        console.error('خطأ في إعادة تهيئة التخزين المحلي:', error);
        return { success: false, message: error.message };
    }
}

// تصدير الوظيفة للاستخدام في الملفات الأخرى
window.resetAndInitializeStorage = resetAndInitializeStorage;
