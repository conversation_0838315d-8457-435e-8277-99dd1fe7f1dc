/**
 * ملف التعامل مع Select2
 * يحتوي على وظائف مخصصة للتعامل مع مكتبة Select2
 */

// تهيئة Select2 للقوائم المنسدلة
function initializeSelect2() {
    console.log('تهيئة Select2 للقوائم المنسدلة...');

    // التحقق من وجود jQuery و Select2
    if (typeof jQuery === 'undefined' || typeof jQuery.fn.select2 === 'undefined') {
        console.error('jQuery أو Select2 غير متوفر');
        return;
    }

    try {
        // تدمير مثيلات Select2 الموجودة
        destroySelect2();

        // تهيئة القوائم المنسدلة
        if (jQuery('#jobDescription').length) {
            jQuery('#jobDescription').select2({
                language: "ar",
                dir: "rtl",
                width: '100%',
                placeholder: "اختر الوصف الوظيفي",
                allowClear: true,
                dropdownParent: jQuery('body')
            });

            // حفظ القيمة الحالية
            const currentValue = jQuery('#jobDescription').val();
            if (currentValue) {
                // إعادة تعيين القيمة بعد التهيئة
                setTimeout(() => {
                    jQuery('#jobDescription').val(currentValue).trigger('change');
                }, 100);
            }

            console.log('تم تهيئة Select2 للوصف الوظيفي');
        }

        if (jQuery('#jobTitle').length) {
            jQuery('#jobTitle').select2({
                language: "ar",
                dir: "rtl",
                width: '100%',
                placeholder: "اختر العنوان الوظيفي",
                allowClear: true,
                dropdownParent: jQuery('body')
            });

            // حفظ القيمة الحالية
            const currentValue = jQuery('#jobTitle').val();
            if (currentValue) {
                // إعادة تعيين القيمة بعد التهيئة
                setTimeout(() => {
                    jQuery('#jobTitle').val(currentValue).trigger('change');
                }, 100);
            }

            console.log('تم تهيئة Select2 للعنوان الوظيفي');
        }

        if (jQuery('#education').length) {
            jQuery('#education').select2({
                language: "ar",
                dir: "rtl",
                width: '100%',
                placeholder: "اختر التحصيل الدراسي",
                allowClear: true,
                dropdownParent: jQuery('body')
            });

            // حفظ القيمة الحالية
            const currentValue = jQuery('#education').val();
            if (currentValue) {
                // إعادة تعيين القيمة بعد التهيئة
                setTimeout(() => {
                    jQuery('#education').val(currentValue).trigger('change');
                }, 100);
            }

            console.log('تم تهيئة Select2 للتحصيل الدراسي');
        }

        if (jQuery('#workLocation').length) {
            jQuery('#workLocation').select2({
                language: "ar",
                dir: "rtl",
                width: '100%',
                placeholder: "اختر موقع العمل",
                allowClear: true,
                dropdownParent: jQuery('body')
            });

            // حفظ القيمة الحالية
            const currentValue = jQuery('#workLocation').val();
            if (currentValue) {
                // إعادة تعيين القيمة بعد التهيئة
                setTimeout(() => {
                    jQuery('#workLocation').val(currentValue).trigger('change');
                }, 100);
            }

            console.log('تم تهيئة Select2 لموقع العمل');
        }

        console.log('تم تهيئة Select2 بنجاح');
    } catch (error) {
        console.error('خطأ في تهيئة Select2:', error);
    }
}

// تدمير مثيلات Select2
function destroySelect2() {
    console.log('تدمير مثيلات Select2...');

    // التحقق من وجود jQuery و Select2
    if (typeof jQuery === 'undefined' || typeof jQuery.fn.select2 === 'undefined') {
        console.error('jQuery أو Select2 غير متوفر');
        return;
    }

    try {
        // تدمير مثيلات Select2
        jQuery('#jobDescription, #jobTitle, #education, #workLocation').each(function() {
            if (jQuery(this).data('select2')) {
                jQuery(this).select2('destroy');
            }
        });

        console.log('تم تدمير مثيلات Select2 بنجاح');
    } catch (error) {
        console.error('خطأ في تدمير Select2:', error);
    }
}

// تحديث قيمة Select2
function updateSelect2Value(selector, value) {
    console.log(`تحديث قيمة Select2 للعنصر ${selector} إلى ${value}...`);

    // التحقق من وجود jQuery و Select2
    if (typeof jQuery === 'undefined' || typeof jQuery.fn.select2 === 'undefined') {
        console.error('jQuery أو Select2 غير متوفر');
        return;
    }

    try {
        // التحقق من وجود العنصر
        if (jQuery(selector).length === 0) {
            console.error(`العنصر ${selector} غير موجود`);
            return;
        }

        // التحقق من وجود القيمة في القائمة
        const selectElement = jQuery(selector)[0];
        let optionExists = false;

        for (let i = 0; i < selectElement.options.length; i++) {
            if (selectElement.options[i].value == value) {
                optionExists = true;
                break;
            }
        }

        if (!optionExists && value) {
            console.warn(`القيمة ${value} غير موجودة في القائمة ${selector}، إضافتها...`);

            // الحصول على النص المناسب للقيمة
            let optionText = value;

            // محاولة الحصول على النص من مصادر البيانات المختلفة
            if (selector === '#jobTitle' && window.appData && window.appData.jobTitles) {
                // البحث في جميع الفئات
                for (const category in window.appData.jobTitles) {
                    const title = window.appData.jobTitles[category].find(t => t.id == value);
                    if (title) {
                        optionText = title.name;
                        break;
                    }
                }
            } else if (selector === '#workLocation' && window.appData && window.appData.workLocations) {
                const location = window.appData.workLocations.find(l => l.id == value);
                if (location) {
                    optionText = location.name;
                }
            } else if (selector === '#education' && window.appData && window.appData.educationLevels) {
                const education = window.appData.educationLevels.find(e => e.id == value);
                if (education) {
                    optionText = education.name;
                }
            }

            // إضافة الخيار إلى القائمة
            const newOption = new Option(optionText, value, true, true);
            jQuery(selector).append(newOption);
            console.log(`تمت إضافة الخيار ${optionText} (${value}) إلى القائمة ${selector}`);
        }

        // تحديث القيمة
        jQuery(selector).val(value).trigger('change');
        console.log(`تم تحديث قيمة Select2 للعنصر ${selector} بنجاح`);
    } catch (error) {
        console.error(`خطأ في تحديث قيمة Select2 للعنصر ${selector}:`, error);
    }
}

// إضافة مستمعي أحداث Select2
function addSelect2EventListeners() {
    console.log('إضافة مستمعي أحداث Select2...');

    // التحقق من وجود jQuery و Select2
    if (typeof jQuery === 'undefined' || typeof jQuery.fn.select2 === 'undefined') {
        console.error('jQuery أو Select2 غير متوفر');
        return;
    }

    try {
        // إزالة مستمعي الأحداث الموجودة
        jQuery('#jobDescription, #jobTitle, #education, #workLocation').off('change');

        // إضافة مستمع حدث تغيير الوصف الوظيفي
        jQuery('#jobDescription').on('change', function() {
            console.log('تم تغيير الوصف الوظيفي');

            // استدعاء وظيفة تحديث العناوين الوظيفية
            if (typeof updateJobTitles === 'function') {
                updateJobTitles();
            }
        });

        // إضافة مستمع حدث تغيير العنوان الوظيفي
        jQuery('#jobTitle').on('change', function() {
            console.log('تم تغيير العنوان الوظيفي');

            // استدعاء وظيفة تحديث العنوان الوظيفي الجديد
            if (typeof updateNewJobTitle === 'function') {
                updateNewJobTitle();
            }
        });

        // إضافة مستمع حدث تغيير التحصيل الدراسي
        jQuery('#education').on('change', function() {
            console.log('تم تغيير التحصيل الدراسي');

            // استدعاء وظيفة تحديث العنوان الوظيفي الجديد
            if (typeof updateNewJobTitle === 'function') {
                updateNewJobTitle();
            }
        });

        // إضافة مستمع حدث تغيير تاريخ آخر ترفيع
        jQuery('#lastPromotionDate').on('change', function() {
            console.log('تم تغيير تاريخ آخر ترفيع');

            // استدعاء وظيفة تحديث العنوان الوظيفي الجديد
            if (typeof updateNewJobTitle === 'function') {
                updateNewJobTitle();
            }
        });

        console.log('تم إضافة مستمعي أحداث Select2 بنجاح');
    } catch (error) {
        console.error('خطأ في إضافة مستمعي أحداث Select2:', error);
    }
}

// تصدير الوظائف للاستخدام في ملفات أخرى
window.initializeSelect2 = initializeSelect2;
window.destroySelect2 = destroySelect2;
window.updateSelect2Value = updateSelect2Value;
window.addSelect2EventListeners = addSelect2EventListeners;
