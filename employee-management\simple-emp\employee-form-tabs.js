// تهيئة النموذج عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة التبويبات
    initTabs();
    
    // تحميل البيانات الأولية
    loadInitialData();
    
    // إضافة مستمعي الأحداث
    addEventListeners();
    
    // التحقق من وجود معرف موظف في عنوان URL
    const urlParams = new URLSearchParams(window.location.search);
    const employeeId = urlParams.get('id');
    
    if (employeeId) {
        // تحميل بيانات الموظف
        loadEmployeeData(employeeId);
    } else {
        // حساب تاريخ التقاعد عند تغيير تاريخ الميلاد
        const birthDateInput = document.getElementById('birthDate');
        if (birthDateInput) {
            birthDateInput.addEventListener('change', calculateRetirementDate);
        }
    }
});

// تهيئة التبويبات
function initTabs() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            // إزالة الفئة النشطة من جميع الأزرار
            tabButtons.forEach(btn => btn.classList.remove('active'));
            // إضافة الفئة النشطة للزر المضغوط
            this.classList.add('active');
            
            // إخفاء جميع محتويات التبويبات
            tabContents.forEach(content => content.classList.remove('active'));
            
            // إظهار محتوى التبويب المطلوب
            const tabId = this.getAttribute('data-tab');
            document.getElementById(tabId).classList.add('active');
        });
    });
}

// إضافة مستمعي الأحداث
function addEventListeners() {
    const employeeForm = document.getElementById('employeeForm');
    const birthDateInput = document.getElementById('birthDate');
    const hireDateInput = document.getElementById('hireDate');
    const currentDegreeInput = document.getElementById('currentDegree');
    const currentStageInput = document.getElementById('currentStage');
    const jobDescriptionSelect = document.getElementById('jobDescription');
    const lastPromotionDateInput = document.getElementById('lastPromotionDate');
    
    // إضافة مستمع حدث لتاريخ الميلاد
    if (birthDateInput) {
        birthDateInput.addEventListener('change', calculateRetirementDate);
    }
    
    // إضافة مستمع حدث لتاريخ التعيين
    if (hireDateInput && lastPromotionDateInput) {
        hireDateInput.addEventListener('change', function() {
            if (!lastPromotionDateInput.value) {
                lastPromotionDateInput.value = hireDateInput.value;
            }
            calculateNextPromotionDate();
        });
    }
    
    // إضافة مستمع حدث لتاريخ الاستحقاق الحالي
    const currentDueDateInput = document.getElementById('currentDueDate');
    if (currentDueDateInput) {
        currentDueDateInput.addEventListener('change', function() {
            calculateNextAllowanceDate(); // حساب تاريخ الاستحقاق الجديد بناءً على العلاوة
        });
    }
    
    // إضافة مستمع حدث لقدم الترفيع
    const promotionSeniorityInput = document.getElementById('promotionSeniority');
    if (promotionSeniorityInput) {
        promotionSeniorityInput.addEventListener('change', function() {
            calculateNextPromotionDate(); // حساب تاريخ الترفيع القادم
        });
    }
    
    // إضافة مستمع حدث لقدم العلاوة
    const allowanceSeniorityInput = document.getElementById('allowanceSeniority');
    if (allowanceSeniorityInput) {
        allowanceSeniorityInput.addEventListener('change', function() {
            calculateNextAllowanceDate(); // حساب تاريخ الاستحقاق الجديد بناءً على العلاوة
        });
    }
    
    if (currentDegreeInput && currentStageInput) {
        currentDegreeInput.addEventListener('change', function() {
            calculateSalary();
            calculateNextPromotionDate();
            updateNewDegreeAndStage();
        });
        
        currentStageInput.addEventListener('change', function() {
            calculateSalary();
            updateNewDegreeAndStage();
        });
    }
    
    if (jobDescriptionSelect) {
        jobDescriptionSelect.addEventListener('change', updateJobTitles);
    }
    
    // إضافة مستمع حدث لتقديم النموذج
    if (employeeForm) {
        employeeForm.addEventListener('submit', function(e) {
            e.preventDefault();
            saveEmployeeData();
        });
    }
}

// تحميل بيانات الموظف
function loadEmployeeData(employeeId) {
    // الحصول على الموظفين من التخزين المحلي
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');
    
    // البحث عن الموظف بالمعرف
    const employee = employees.find(emp => emp.id == employeeId);
    
    if (employee) {
        // ملء النموذج ببيانات الموظف
        fillEmployeeForm(employee);
    } else {
        alert('لم يتم العثور على الموظف!');
        window.location.href = 'employees-list.html';
    }
}

// حفظ بيانات الموظف
function saveEmployeeData() {
    // التحقق من صحة البيانات
    if (!validateForm()) {
        return;
    }
    
    // جمع بيانات الموظف من النموذج
    const employeeData = {
        id: document.getElementById('employeeId').value || Date.now(), // استخدام الوقت الحالي كمعرف إذا كان جديداً
        name: document.getElementById('fullName').value,
        jobDescription: document.getElementById('jobDescription').value,
        jobTitle: document.getElementById('jobTitle').value,
        workLocation: document.getElementById('workLocation').value,
        education: document.getElementById('education').value,
        birthDate: document.getElementById('birthDate').value,
        hireDate: document.getElementById('hireDate').value,
        currentDegree: parseInt(document.getElementById('currentDegree').value),
        currentStage: parseInt(document.getElementById('currentStage').value),
        allowanceSeniority: parseInt(document.getElementById('allowanceSeniority').value), // قدم العلاوة
        promotionSeniority: parseInt(document.getElementById('promotionSeniority').value), // قدم الترفيع
        currentSalary: document.getElementById('currentSalary').value.replace(/[^\d]/g, ''), // إزالة الأحرف غير الرقمية
        lastPromotionDate: document.getElementById('lastPromotionDate').value,
        nextPromotionDate: document.getElementById('nextPromotionDate').value,
        currentDueDate: document.getElementById('currentDueDate').value,
        newDueDate: document.getElementById('newDueDate').value,
        retirementDate: document.getElementById('retirementDate').value,
        status: 'active'
    };
    
    // الحصول على الموظفين الحاليين
    let employees = JSON.parse(localStorage.getItem('employees') || '[]');
    
    // التحقق مما إذا كان الموظف موجوداً بالفعل
    const existingEmployeeIndex = employees.findIndex(emp => emp.id == employeeData.id);
    
    if (existingEmployeeIndex !== -1) {
        // تحديث الموظف الموجود
        employees[existingEmployeeIndex] = employeeData;
    } else {
        // إضافة موظف جديد
        employees.push(employeeData);
    }
    
    // حفظ البيانات في التخزين المحلي
    localStorage.setItem('employees', JSON.stringify(employees));
    
    // عرض رسالة نجاح
    alert('تم حفظ بيانات الموظف بنجاح!');
    
    // العودة إلى صفحة قائمة الموظفين
    window.location.href = 'employees-list.html';
}

// التحقق من صحة النموذج
function validateForm() {
    // التحقق من الحقول المطلوبة
    const requiredFields = [
        'fullName', 'jobDescription', 'birthDate', 'hireDate',
        'currentDegree', 'currentStage', 'allowanceSeniority', 'promotionSeniority',
        'lastPromotionDate', 'currentDueDate'
    ];
    
    for (const fieldId of requiredFields) {
        const field = document.getElementById(fieldId);
        if (!field || !field.value.trim()) {
            alert(`يرجى ملء حقل ${field.labels[0].textContent.replace(':', '')}`);
            
            // تحديد التبويب الذي يحتوي على الحقل
            const tabContent = field.closest('.tab-content');
            if (tabContent) {
                const tabId = tabContent.id;
                const tabBtn = document.querySelector(`.tab-btn[data-tab="${tabId}"]`);
                if (tabBtn) {
                    tabBtn.click(); // تنشيط التبويب المناسب
                }
            }
            
            field.focus();
            return false;
        }
    }
    
    // التحقق من صحة قدم العلاوة
    const allowanceSeniority = parseInt(document.getElementById('allowanceSeniority').value);
    if (isNaN(allowanceSeniority) || allowanceSeniority < 0 || allowanceSeniority > 120) {
        alert('يرجى إدخال قيمة صحيحة لقدم العلاوة (من 0 إلى 120 شهر)');
        document.querySelector('.tab-btn[data-tab="allowance-info"]').click(); // تنشيط تبويب العلاوات
        document.getElementById('allowanceSeniority').focus();
        return false;
    }
    
    // التحقق من صحة قدم الترفيع
    const promotionSeniority = parseInt(document.getElementById('promotionSeniority').value);
    if (isNaN(promotionSeniority) || promotionSeniority < 0 || promotionSeniority > 120) {
        alert('يرجى إدخال قيمة صحيحة لقدم الترفيع (من 0 إلى 120 شهر)');
        document.querySelector('.tab-btn[data-tab="promotion-info"]').click(); // تنشيط تبويب الترفيعات
        document.getElementById('promotionSeniority').focus();
        return false;
    }
    
    return true;
}
