// النظام المستقر لإدارة العلاوات والترفيعات
class StableEmployeeSystem {
    constructor() {
        this.employees = this.loadData('employees') || [];
        this.thanks = this.loadData('thanks') || [];
        this.init();
    }

    init() {
        this.updateStats();
        this.loadEmployees();
        this.loadThanks();
        this.setupEventListeners();
        this.populateEmployeeSelect();
    }

    // إدارة البيانات
    loadData(key) {
        try {
            return JSON.parse(localStorage.getItem(key)) || [];
        } catch (error) {
            console.error(`خطأ في تحميل ${key}:`, error);
            return [];
        }
    }

    saveData(key, data) {
        try {
            localStorage.setItem(key, JSON.stringify(data));
            return true;
        } catch (error) {
            console.error(`خطأ في حفظ ${key}:`, error);
            return false;
        }
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // نموذج الموظف
        document.getElementById('employeeForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.addEmployee();
        });

        // نموذج التشكر
        document.getElementById('thanksForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.addThanks();
        });

        // البحث في الموظفين
        document.getElementById('employeeSearch').addEventListener('input', (e) => {
            this.searchEmployees(e.target.value);
        });

        // البحث في التشكرات
        document.getElementById('thanksSearch').addEventListener('input', (e) => {
            this.searchThanks(e.target.value);
        });
    }

    // إضافة موظف
    addEmployee() {
        const formData = {
            id: Date.now(),
            name: document.getElementById('employeeName').value,
            employeeNumber: document.getElementById('employeeNumber').value,
            jobTitle: document.getElementById('jobTitle').value,
            appointmentDate: document.getElementById('appointmentDate').value,
            currentSalary: parseInt(document.getElementById('currentSalary').value),
            lastAllowanceDate: document.getElementById('lastAllowanceDate').value,
            createdAt: new Date().toISOString()
        };

        // التحقق من عدم تكرار الرقم الوظيفي
        if (this.employees.find(emp => emp.employeeNumber === formData.employeeNumber)) {
            this.showMessage('الرقم الوظيفي موجود مسبقاً', 'error');
            return;
        }

        this.employees.push(formData);
        
        if (this.saveData('employees', this.employees)) {
            this.showMessage('تم إضافة الموظف بنجاح', 'success');
            document.getElementById('employeeForm').reset();
            this.loadEmployees();
            this.populateEmployeeSelect();
            this.updateStats();
        } else {
            this.showMessage('خطأ في حفظ بيانات الموظف', 'error');
        }
    }

    // إضافة شكر
    addThanks() {
        const formData = {
            id: Date.now(),
            employeeId: parseInt(document.getElementById('thanksEmployee').value),
            type: document.getElementById('thanksType').value,
            date: document.getElementById('thanksDate').value,
            number: document.getElementById('thanksNumber').value,
            reason: document.getElementById('thanksReason').value,
            createdAt: new Date().toISOString()
        };

        this.thanks.push(formData);
        
        if (this.saveData('thanks', this.thanks)) {
            this.showMessage('تم إضافة الشكر بنجاح', 'success');
            document.getElementById('thanksForm').reset();
            this.loadThanks();
            this.updateStats();
        } else {
            this.showMessage('خطأ في حفظ بيانات الشكر', 'error');
        }
    }

    // تحميل الموظفين
    loadEmployees() {
        const tbody = document.getElementById('employeesTableBody');
        tbody.innerHTML = '';

        this.employees.forEach(employee => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${employee.name}</td>
                <td>${employee.employeeNumber}</td>
                <td>${employee.jobTitle}</td>
                <td>${employee.currentSalary.toLocaleString()}</td>
                <td>
                    <button class="btn" onclick="system.editEmployee(${employee.id})" style="margin: 0.2rem;">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-danger" onclick="system.deleteEmployee(${employee.id})" style="margin: 0.2rem;">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    // تحميل التشكرات
    loadThanks() {
        const tbody = document.getElementById('thanksTableBody');
        tbody.innerHTML = '';

        this.thanks.forEach(thank => {
            const employee = this.employees.find(emp => emp.id === thank.employeeId);
            const employeeName = employee ? employee.name : 'غير معروف';
            
            const typeText = {
                'university': 'شكر جامعي',
                'ministerial': 'شكر وزاري',
                'presidential': 'شكر رئاسي'
            }[thank.type] || thank.type;

            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${employeeName}</td>
                <td>${typeText}</td>
                <td>${this.formatDate(thank.date)}</td>
                <td>${thank.number || '-'}</td>
                <td>
                    <button class="btn" onclick="system.editThanks(${thank.id})" style="margin: 0.2rem;">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-danger" onclick="system.deleteThanks(${thank.id})" style="margin: 0.2rem;">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    // ملء قائمة الموظفين في نموذج التشكر
    populateEmployeeSelect() {
        const select = document.getElementById('thanksEmployee');
        select.innerHTML = '<option value="">اختر الموظف</option>';
        
        this.employees.forEach(employee => {
            const option = document.createElement('option');
            option.value = employee.id;
            option.textContent = `${employee.name} - ${employee.employeeNumber}`;
            select.appendChild(option);
        });
    }

    // تحديث الإحصائيات
    updateStats() {
        document.getElementById('totalEmployees').textContent = this.employees.length;
        document.getElementById('totalThanks').textContent = this.thanks.length;
        
        // حساب العلاوات المستحقة (تقريبي)
        const today = new Date();
        const pendingAllowances = this.employees.filter(emp => {
            if (!emp.lastAllowanceDate) return true;
            const lastDate = new Date(emp.lastAllowanceDate);
            const yearsDiff = (today - lastDate) / (1000 * 60 * 60 * 24 * 365);
            return yearsDiff >= 1;
        }).length;
        
        document.getElementById('pendingAllowances').textContent = pendingAllowances;
        document.getElementById('pendingPromotions').textContent = Math.floor(this.employees.length * 0.3);
    }

    // البحث في الموظفين
    searchEmployees(searchTerm) {
        const tbody = document.getElementById('employeesTableBody');
        const rows = tbody.querySelectorAll('tr');
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            const isVisible = text.includes(searchTerm.toLowerCase());
            row.style.display = isVisible ? '' : 'none';
        });
    }

    // البحث في التشكرات
    searchThanks(searchTerm) {
        const tbody = document.getElementById('thanksTableBody');
        const rows = tbody.querySelectorAll('tr');
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            const isVisible = text.includes(searchTerm.toLowerCase());
            row.style.display = isVisible ? '' : 'none';
        });
    }

    // حذف موظف
    deleteEmployee(id) {
        if (confirm('هل أنت متأكد من حذف هذا الموظف؟')) {
            this.employees = this.employees.filter(emp => emp.id !== id);
            this.saveData('employees', this.employees);
            this.loadEmployees();
            this.populateEmployeeSelect();
            this.updateStats();
            this.showMessage('تم حذف الموظف بنجاح', 'success');
        }
    }

    // حذف شكر
    deleteThanks(id) {
        if (confirm('هل أنت متأكد من حذف هذا الشكر؟')) {
            this.thanks = this.thanks.filter(thank => thank.id !== id);
            this.saveData('thanks', this.thanks);
            this.loadThanks();
            this.updateStats();
            this.showMessage('تم حذف الشكر بنجاح', 'success');
        }
    }

    // تنسيق التاريخ (ميلادي فقط - إجباري)
    formatDate(dateString) {
        if (!dateString) return '-';

        const date = new Date(dateString);
        if (isNaN(date.getTime())) return dateString;

        // إجبار استخدام التقويم الميلادي مع تنسيق يدوي لضمان عدم ظهور التاريخ الهجري
        const year = date.getFullYear();
        const month = date.getMonth() + 1;
        const day = date.getDate();

        const monthNames = [
            'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
            'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
        ];

        return `${day} ${monthNames[month - 1]} ${year}`;
    }

    // عرض الرسائل
    showMessage(message, type) {
        const messageEl = document.getElementById('systemMessage');
        messageEl.textContent = message;
        messageEl.className = `message ${type}`;
        messageEl.style.display = 'block';
        
        setTimeout(() => {
            messageEl.style.display = 'none';
        }, 5000);
    }
}

// وظائف التبويبات
function showTab(tabName) {
    // إخفاء جميع التبويبات
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });
    
    // إزالة التفعيل من جميع الأزرار
    document.querySelectorAll('.tab').forEach(btn => {
        btn.classList.remove('active');
    });
    
    // إظهار التبويب المطلوب
    document.getElementById(tabName).classList.add('active');
    
    // تفعيل الزر المطلوب
    event.target.classList.add('active');
}

// إضافة بيانات تجريبية
function addSampleData() {
    if (confirm('هل تريد إضافة بيانات تجريبية؟ سيتم استبدال البيانات الحالية.')) {
        const sampleEmployees = [
            {
                id: 1,
                name: "د. أحمد محمد علي",
                employeeNumber: "2020001",
                jobTitle: "أستاذ مساعد",
                appointmentDate: "2020-01-15",
                currentSalary: 850000,
                lastAllowanceDate: "2023-01-15"
            },
            {
                id: 2,
                name: "د. فاطمة حسن محمود",
                employeeNumber: "2019002",
                jobTitle: "مدرس",
                appointmentDate: "2019-09-01",
                currentSalary: 750000,
                lastAllowanceDate: "2023-09-01"
            },
            {
                id: 3,
                name: "م. علي حسين كريم",
                employeeNumber: "2021003",
                jobTitle: "مهندس أول",
                appointmentDate: "2021-03-10",
                currentSalary: 650000,
                lastAllowanceDate: "2024-03-10"
            }
        ];

        const sampleThanks = [
            {
                id: 1,
                employeeId: 1,
                type: "university",
                date: "2024-03-15",
                number: "ج/123/2024",
                reason: "التميز في البحث العلمي"
            },
            {
                id: 2,
                employeeId: 2,
                type: "ministerial",
                date: "2024-02-20",
                number: "و/456/2024",
                reason: "الإنجاز المتميز في التدريس"
            }
        ];

        system.employees = sampleEmployees;
        system.thanks = sampleThanks;
        system.saveData('employees', sampleEmployees);
        system.saveData('thanks', sampleThanks);
        system.init();
        system.showMessage('تم إضافة البيانات التجريبية بنجاح', 'success');
    }
}

// مسح جميع البيانات
function clearAllData() {
    if (confirm('هل أنت متأكد من مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        localStorage.clear();
        system.employees = [];
        system.thanks = [];
        system.init();
        system.showMessage('تم مسح جميع البيانات', 'success');
    }
}

// تصدير البيانات
function exportData() {
    const data = {
        employees: system.employees,
        thanks: system.thanks,
        exportDate: new Date().toISOString()
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `employee-data-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
    
    system.showMessage('تم تصدير البيانات بنجاح', 'success');
}

// تقارير بسيطة
function generateAllowanceReport() {
    const reportContent = document.getElementById('reportContent');
    const today = new Date();
    
    const pendingAllowances = system.employees.filter(emp => {
        if (!emp.lastAllowanceDate) return true;
        const lastDate = new Date(emp.lastAllowanceDate);
        const yearsDiff = (today - lastDate) / (1000 * 60 * 60 * 24 * 365);
        return yearsDiff >= 1;
    });

    reportContent.innerHTML = `
        <h4>تقرير العلاوات المستحقة</h4>
        <p>عدد الموظفين المستحقين للعلاوة: ${pendingAllowances.length}</p>
        <ul>
            ${pendingAllowances.map(emp => `<li>${emp.name} - ${emp.employeeNumber}</li>`).join('')}
        </ul>
    `;
}

function generateThanksReport() {
    const reportContent = document.getElementById('reportContent');
    const thanksStats = {
        university: system.thanks.filter(t => t.type === 'university').length,
        ministerial: system.thanks.filter(t => t.type === 'ministerial').length,
        presidential: system.thanks.filter(t => t.type === 'presidential').length
    };

    reportContent.innerHTML = `
        <h4>تقرير التشكرات</h4>
        <p>إجمالي التشكرات: ${system.thanks.length}</p>
        <ul>
            <li>شكر جامعي: ${thanksStats.university}</li>
            <li>شكر وزاري: ${thanksStats.ministerial}</li>
            <li>شكر رئاسي: ${thanksStats.presidential}</li>
        </ul>
    `;
}

// تهيئة النظام
let system;
document.addEventListener('DOMContentLoaded', function() {
    system = new StableEmployeeSystem();
});
