<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير العلاوات المستحقة - برنامج إدارة العلاوات والترفيعات</title>
    <link rel="stylesheet" href="styles.css">
    <!-- إضافة خط Cairo من Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- إضافة Font Awesome للأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- إضافة أنماط شريط التنقل المحسن -->
    <link rel="stylesheet" href="navbar.css">
    <!-- إضافة أنماط محسنة -->
    <link rel="stylesheet" href="enhanced-styles.css">
    <!-- إضافة أنماط النوافذ المنبثقة -->
    <link rel="stylesheet" href="modals.css">
    <!-- إضافة أنماط الإشعارات -->
    <link rel="stylesheet" href="notifications.css">

    <style>
        /* حالات الاستحقاق */
        .status-badge {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.85em;
            font-weight: 600;
            text-align: center;
            display: inline-block;
            min-width: 100px;
        }

        .status-overdue {
            background: #fecaca;
            color: #991b1b;
        }

        .status-urgent {
            background: #fef3c7;
            color: #92400e;
        }

        .status-upcoming {
            background: #dbeafe;
            color: #1e40af;
        }

        .status-future {
            background: #d1fae5;
            color: #065f46;
        }

        /* ألوان الصفوف */
        .table-enhanced tbody tr.overdue {
            background-color: #fef2f2;
        }

        .table-enhanced tbody tr.urgent {
            background-color: #fffbeb;
        }

        .table-enhanced tbody tr.upcoming {
            background-color: #eff6ff;
        }

        /* تحسين عرض الجدول */
        .table-enhanced th {
            text-align: center;
            font-weight: 600;
        }

        .table-enhanced td {
            text-align: center;
            vertical-align: middle;
        }

        .table-enhanced td:nth-child(2) {
            text-align: right;
        }

        .table-enhanced td:nth-child(3) {
            text-align: right;
        }

        .table-enhanced td:nth-child(4) {
            text-align: right;
        }

        /* تنسيق التقرير المطور */
        .advanced-report-table {
            font-size: 0.9rem;
        }

        .advanced-report-table th {
            background: #f8f9fa;
            color: #374151;
            font-weight: 600;
            text-align: center;
            padding: 12px 8px;
            border: 1px solid #e5e7eb;
        }

        .advanced-report-table td {
            padding: 10px 8px;
            border: 1px solid #e5e7eb;
            vertical-align: middle;
        }

        /* أعمدة محددة العرض */
        .priority-col { width: 60px; }
        .employee-col { width: 200px; }
        .job-col { width: 150px; }
        .location-col { width: 120px; }
        .degree-col { width: 100px; }
        .salary-col { width: 140px; }
        .due-date-col { width: 150px; }
        .allowance-col { width: 120px; }
        .status-col { width: 120px; }
        .actions-col { width: 100px; }

        /* تنسيق بيانات الموظف */
        .employee-info {
            text-align: right;
        }

        .employee-name {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .employee-id {
            font-size: 0.8rem;
            color: #6b7280;
        }

        /* تنسيق معلومات الوظيفة */
        .job-info {
            text-align: right;
        }

        .job-description {
            font-weight: 500;
            color: #374151;
            margin-bottom: 4px;
        }

        .job-title {
            font-size: 0.8rem;
            color: #6b7280;
        }

        /* تنسيق الدرجة والمرحلة */
        .degree-info {
            text-align: center;
        }

        .current-degree, .current-stage {
            font-size: 0.85rem;
            margin-bottom: 2px;
        }

        /* تنسيق الراتب */
        .salary-info {
            text-align: center;
        }

        .current-salary {
            font-weight: 600;
            color: #059669;
            margin-bottom: 4px;
        }

        .service-years {
            font-size: 0.8rem;
            color: #6b7280;
        }

        /* تنسيق التاريخ */
        .due-date {
            text-align: center;
        }

        .date {
            font-weight: 500;
            margin-bottom: 4px;
        }

        .days-info {
            font-size: 0.8rem;
            padding: 2px 6px;
            border-radius: 12px;
            background: #f3f4f6;
            color: #374151;
        }

        /* تنسيق العلاوة */
        .allowance-amount {
            text-align: center;
        }

        .amount {
            font-weight: 600;
            color: #dc2626;
            margin-bottom: 4px;
        }

        .allowance-type {
            font-size: 0.8rem;
            color: #6b7280;
        }

        /* شارات الأولوية */
        .priority-badge {
            display: inline-block;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            text-align: center;
            line-height: 30px;
            font-weight: 600;
            color: white;
        }

        .priority-4 { background: #dc2626; }
        .priority-3 { background: #f59e0b; }
        .priority-2 { background: #3b82f6; }
        .priority-1 { background: #10b981; }

        /* ألوان الصفوف الطبيعية */
        .row-overdue { background-color: #ffffff; }
        .row-urgent { background-color: #f9f9f9; }
        .row-upcoming { background-color: #ffffff; }
        .row-future { background-color: #f9f9f9; }

        /* أزرار الإجراءات */
        .actions {
            text-align: center;
        }

        .btn-action {
            display: inline-block;
            width: 32px;
            height: 32px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin: 0 2px;
            transition: all 0.2s;
        }

        .btn-view {
            background: #3b82f6;
            color: white;
        }

        .btn-view:hover {
            background: #2563eb;
        }

        .btn-print {
            background: #6b7280;
            color: white;
        }

        .btn-print:hover {
            background: #4b5563;
        }

        /* التصميم المتطور للتقرير */
        .modern-report-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0,0,0,0.12);
            margin: 20px 0;
            border: 1px solid #e5e7eb;
        }

        .modern-report-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .modern-report-table .header-row {
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #1e40af 100%);
            color: white;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .modern-report-table th {
            padding: 16px 12px;
            text-align: center;
            font-weight: 700;
            border: 1px solid #1e40af;
            font-size: 12px;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
            white-space: nowrap;
            min-width: 100px;
        }

        .modern-report-table td {
            padding: 12px 10px;
            text-align: center;
            border: 1px solid #e5e7eb;
            font-size: 12px;
            vertical-align: middle;
            transition: all 0.2s ease;
        }

        .modern-report-table tbody tr:nth-child(even) {
            background-color: #f8fafc;
        }

        .modern-report-table tbody tr:hover {
            background-color: #e0f2fe;
            transform: scale(1.01);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        /* تنسيق خاص للأعمدة */
        .employee-name {
            font-weight: 600;
            color: #1f2937;
        }

        .salary-amount {
            font-weight: 600;
            color: #059669;
            font-family: 'Courier New', monospace;
        }

        .date-cell {
            color: #6b7280;
            font-size: 11px;
        }

        .allowance-amount {
            font-weight: 700;
            color: #dc2626;
            font-family: 'Courier New', monospace;
        }

        /* أزرار الإجراءات المتطورة */
        .action-buttons {
            display: flex;
            gap: 3px;
            justify-content: center;
            flex-wrap: wrap;
            padding: 4px;
        }

        .action-btn {
            padding: 6px 10px;
            border: none;
            border-radius: 6px;
            font-size: 10px;
            font-weight: 600;
            cursor: pointer;
            color: white;
            min-width: 70px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        .btn-current {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        }
        .btn-new {
            background: linear-gradient(135deg, #10b981, #047857);
        }
        .btn-allowance {
            background: linear-gradient(135deg, #ef4444, #dc2626);
        }
        .btn-promotion {
            background: linear-gradient(135deg, #f59e0b, #d97706);
        }
        .btn-details {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
        }
        .btn-edit {
            background: linear-gradient(135deg, #06b6d4, #0891b2);
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .action-btn:active {
            transform: translateY(0);
        }

        /* تأثيرات بصرية إضافية */
        .modern-report-table tbody tr {
            transition: all 0.3s ease;
        }

        .priority-high {
            border-left: 4px solid #ef4444;
        }

        .priority-medium {
            border-left: 4px solid #f59e0b;
        }

        .priority-low {
            border-left: 4px solid #10b981;
        }

        /* الإحصائيات السريعة */
        .quick-stats {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 15px;
            min-width: 200px;
            border-left: 4px solid;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }

        .stat-total { border-left-color: #3b82f6; }
        .stat-urgent { border-left-color: #ef4444; }
        .stat-amount { border-left-color: #10b981; }

        .stat-card i {
            font-size: 2.5rem;
            opacity: 0.8;
        }

        .stat-total i { color: #3b82f6; }
        .stat-urgent i { color: #ef4444; }
        .stat-amount i { color: #10b981; }

        .stat-info {
            display: flex;
            flex-direction: column;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #1f2937;
            line-height: 1;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #6b7280;
            margin-top: 5px;
        }

        /* رأس الجدول */
        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-bottom: 1px solid #e5e7eb;
        }

        .table-header h3 {
            margin: 0;
            color: #1f2937;
            font-size: 1.5rem;
            font-weight: 700;
        }

        .table-header h3 i {
            color: #3b82f6;
            margin-right: 10px;
        }

        .table-actions {
            display: flex;
            gap: 10px;
        }

        .btn-export, .btn-print {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            color: white;
        }

        .btn-export {
            background: linear-gradient(135deg, #10b981, #047857);
        }

        .btn-print {
            background: linear-gradient(135deg, #6b7280, #4b5563);
        }

        .btn-export:hover, .btn-print:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        /* تحسين الجدول */
        .table-wrapper {
            overflow-x: auto;
            max-height: 70vh;
            overflow-y: auto;
        }

        .modern-report-table th i {
            margin-right: 5px;
            opacity: 0.8;
        }

        /* تحسين الاستجابة */
        @media (max-width: 1200px) {
            .modern-report-table {
                font-size: 11px;
            }

            .modern-report-table th,
            .modern-report-table td {
                padding: 8px 6px;
            }

            .action-btn {
                padding: 4px 6px;
                font-size: 9px;
                min-width: 50px;
            }

            .quick-stats {
                flex-direction: column;
                align-items: center;
            }

            .stat-card {
                min-width: 300px;
            }

            .table-header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
        }

        /* رسائل عدم وجود بيانات */
        .no-data-message {
            text-align: center;
            padding: 3rem 2rem;
        }

        .no-data-content {
            max-width: 400px;
            margin: 0 auto;
        }

        .no-data-content i {
            font-size: 3rem;
            color: #6b7280;
            margin-bottom: 1rem;
            display: block;
        }

        .no-data-content.success i {
            color: #10b981;
        }

        .no-data-content h3 {
            margin-bottom: 1rem;
            color: #374151;
        }

        .no-data-content p {
            color: #6b7280;
            margin-bottom: 2rem;
        }

        .action-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
    </style>
</head>
<body>
    <header>
        <nav>
            <div class="logo">
                <i class="fas fa-chart-line logo-icon"></i>
                <span>برنامج إدارة العلاوات والترفيعات</span>
            </div>
            <div class="nav-links">
                <div class="dropdown">
                    <a href="index.html"><i class="fas fa-home"></i> الرئيسية</a>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-id-card"></i> الملفات التعريفية <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="work-locations.html"><i class="fas fa-building"></i> مواقع العمل</a>
                        <a href="job-titles.html"><i class="fas fa-user-tie"></i> العناوين الوظيفية</a>
                        <a href="education-levels.html"><i class="fas fa-graduation-cap"></i> التحصيل الدراسي</a>
                        <a href="leaves.html"><i class="fas fa-calendar-alt"></i> الاجازات</a>
                        <a href="alert-periods.html"><i class="fas fa-bell"></i> فترة التنبيه</a>
                    </div>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-users"></i> الموظفين <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="employee-form.html"><i class="fas fa-user-plus"></i> فتح ملف موظف</a>
                        <a href="employees-list.html"><i class="fas fa-list"></i> عرض الموظفين</a>
                    </div>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-medal"></i> التشكرات والعقوبات <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="thanks.html"><i class="fas fa-award"></i> التشكرات</a>
                        <a href="penalties.html"><i class="fas fa-exclamation-triangle"></i> العقوبات</a>
                    </div>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-calendar-alt"></i> الاجازات والغيابات <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="employee-leaves.html"><i class="fas fa-calendar-check"></i> الاجازات</a>
                        <a href="absences.html"><i class="fas fa-calendar-times"></i> الغيابات</a>
                    </div>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle active"><i class="fas fa-chart-bar"></i> التقارير <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="allowance-reports.html" class="active"><i class="fas fa-file-alt"></i> تقرير العلاوات المستحقة</a>
                        <a href="promotion-reports.html"><i class="fas fa-file-alt"></i> تقرير الترفيعات المستحقة</a>
                        <a href="retirement-reports.html"><i class="fas fa-user-clock"></i> قرب الاحالة الى التقاعد</a>
                        <a href="salary-scale.html"><i class="fas fa-money-bill-wave"></i> سلم الرواتب</a>
                    </div>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-bell"></i> التنبيهات <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="allowance-alerts.html"><i class="fas fa-bell"></i> تنبيه العلاوات المستحقة</a>
                        <a href="promotion-alerts.html"><i class="fas fa-bell"></i> تنبيه الترفيعات المستحقة</a>
                        <a href="retirement-alerts.html"><i class="fas fa-bell"></i> تنبيه الاحالة الى التقاعد</a>
                    </div>
                </div>
            </div>
            <div class="menu-toggle" id="menuToggle">
                <i class="fas fa-bars"></i>
            </div>
        </nav>
    </header>

    <main>
        <h1 class="page-title">تقرير العلاوات المستحقة</h1>

        <div class="card-enhanced">
            <div class="card-header-enhanced">
                <div class="card-title-enhanced">
                    <i class="fas fa-filter"></i>
                    خيارات التصفية
                </div>
            </div>
            <div class="card-body-enhanced">
                <form id="filterForm" class="form-enhanced">
                    <div class="form-row" style="display: flex; gap: 1rem; flex-wrap: wrap;">
                        <div class="form-group-enhanced" style="flex: 1; min-width: 200px;">
                            <label for="dateFrom" class="form-label-enhanced">من تاريخ:</label>
                            <input type="date" id="dateFrom" class="form-control-enhanced">
                        </div>
                        <div class="form-group-enhanced" style="flex: 1; min-width: 200px;">
                            <label for="dateTo" class="form-label-enhanced">إلى تاريخ:</label>
                            <input type="date" id="dateTo" class="form-control-enhanced">
                        </div>
                        <div class="form-group-enhanced" style="flex: 1; min-width: 200px;">
                            <label for="workLocation" class="form-label-enhanced">موقع العمل:</label>
                            <select id="workLocation" class="form-select-enhanced">
                                <option value="">الكل</option>
                                <option value="1">كلية الهندسة</option>
                                <option value="2">كلية العلوم</option>
                                <option value="3">كلية الطب</option>
                                <option value="4">كلية الآداب</option>
                                <option value="5">رئاسة الجامعة</option>
                            </select>
                        </div>
                        <div class="form-group-enhanced" style="flex: 1; min-width: 200px;">
                            <label for="jobDescription" class="form-label-enhanced">الوصف الوظيفي:</label>
                            <select id="jobDescription" class="form-select-enhanced">
                                <option value="">الكل</option>
                                <option value="teaching">تدريسي</option>
                                <option value="technical">فني</option>
                                <option value="administrative">اداري</option>
                            </select>
                        </div>
                    </div>
                    <div style="display: flex; justify-content: center; margin-top: 1rem;">
                        <button type="submit" class="btn-enhanced btn-primary-enhanced">
                            <i class="fas fa-search"></i>
                            بحث
                        </button>
                        <button type="reset" class="btn-enhanced btn-secondary-enhanced" style="margin-right: 1rem;">
                            <i class="fas fa-undo"></i>
                            إعادة تعيين
                        </button>
                        <button type="button" id="printBtn" class="btn-enhanced btn-outline-enhanced" style="margin-right: 1rem;">
                            <i class="fas fa-print"></i>
                            طباعة
                        </button>
                        <button type="button" id="exportBtn" class="btn-enhanced btn-outline-enhanced" style="margin-right: 1rem;">
                            <i class="fas fa-file-excel"></i>
                            تصدير Excel
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <div class="card-enhanced" style="margin-top: 2rem;">
            <div class="card-header-enhanced">
                <div class="card-title-enhanced">
                    <i class="fas fa-list"></i>
                    قائمة الموظفين المستحقين للعلاوة
                </div>
                <div style="display: flex; align-items: center;">
                    <span class="badge-enhanced badge-primary" style="margin-left: 1rem;">
                        <i class="fas fa-users"></i>
                        إجمالي: <span id="totalEmployees">25</span>
                    </span>
                    <div class="search-container" style="position: relative;">
                        <input type="text" placeholder="بحث..." class="form-control-enhanced" style="padding-left: 2.5rem; width: 250px;">
                        <i class="fas fa-search" style="position: absolute; left: 1rem; top: 50%; transform: translateY(-50%); color: #6b7280;"></i>
                    </div>
                </div>
            </div>
            <div class="card-body-enhanced">
                <!-- إحصائيات سريعة -->
                <div class="quick-stats">
                    <div class="stat-card stat-total">
                        <i class="fas fa-users"></i>
                        <div class="stat-info">
                            <span class="stat-number" id="totalEmployees">0</span>
                            <span class="stat-label">إجمالي الموظفين</span>
                        </div>
                    </div>
                    <div class="stat-card stat-urgent">
                        <i class="fas fa-exclamation-triangle"></i>
                        <div class="stat-info">
                            <span class="stat-number" id="urgentCount">0</span>
                            <span class="stat-label">حالات عاجلة</span>
                        </div>
                    </div>
                    <div class="stat-card stat-amount">
                        <i class="fas fa-money-bill-wave"></i>
                        <div class="stat-info">
                            <span class="stat-number" id="totalAmount">0</span>
                            <span class="stat-label">إجمالي العلاوات (د.ع)</span>
                        </div>
                    </div>
                </div>

                <div class="modern-report-container">
                    <div class="table-header">
                        <h3><i class="fas fa-table"></i> تقرير العلاوات المستحقة</h3>
                        <div class="table-actions">
                            <button class="btn-export" onclick="exportToExcel()">
                                <i class="fas fa-file-excel"></i> تصدير Excel
                            </button>
                            <button class="btn-print" onclick="printReport()">
                                <i class="fas fa-print"></i> طباعة
                            </button>
                        </div>
                    </div>

                    <div class="table-wrapper">
                        <table class="modern-report-table">
                            <thead>
                                <tr class="header-row">
                                    <th><i class="fas fa-id-badge"></i> رقم الموظف</th>
                                    <th><i class="fas fa-user"></i> اسم الموظف</th>
                                    <th><i class="fas fa-building"></i> مكان العمل</th>
                                    <th><i class="fas fa-layer-group"></i> الدرجة</th>
                                    <th><i class="fas fa-stairs"></i> المرحلة</th>
                                    <th><i class="fas fa-money-bill"></i> الراتب الحالي</th>
                                    <th><i class="fas fa-calendar"></i> تاريخ الاستحقاق الحالي</th>
                                    <th><i class="fas fa-arrow-up"></i> الدرجة الجديدة</th>
                                    <th><i class="fas fa-arrow-up"></i> المرحلة الجديدة</th>
                                    <th><i class="fas fa-money-bill-trend-up"></i> الراتب الجديد</th>
                                    <th><i class="fas fa-calendar-plus"></i> تاريخ الاستحقاق الجديد</th>
                                    <th><i class="fas fa-gift"></i> العلاوة السنوية المستحقة</th>
                                    <th><i class="fas fa-cogs"></i> الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="employeesTableBody">
                                <!-- سيتم تحميل البيانات من localStorage -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 1.5rem;">
                    <div>
                        <span>عرض 1-3 من 25 سجل</span>
                    </div>
                    <div class="pagination" style="display: flex; gap: 0.5rem;">
                        <button class="btn-enhanced btn-outline-enhanced" style="padding: 0.4rem 0.8rem;">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                        <button class="btn-enhanced btn-primary-enhanced" style="padding: 0.4rem 0.8rem;">1</button>
                        <button class="btn-enhanced btn-outline-enhanced" style="padding: 0.4rem 0.8rem;">2</button>
                        <button class="btn-enhanced btn-outline-enhanced" style="padding: 0.4rem 0.8rem;">3</button>
                        <button class="btn-enhanced btn-outline-enhanced" style="padding: 0.4rem 0.8rem;">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="footer-content">
            <div class="footer-logo">
                <i class="fas fa-chart-line"></i> برنامج إدارة العلاوات والترفيعات
            </div>
            <div class="footer-links">
                <a href="#">الرئيسية</a>
                <a href="#">عن البرنامج</a>
                <a href="#">الميزات</a>
                <a href="#">المساعدة</a>
                <a href="#">اتصل بنا</a>
            </div>
            <div class="footer-social">
                <a href="#" class="social-icon"><i class="fab fa-facebook-f"></i></a>
                <a href="#" class="social-icon"><i class="fab fa-twitter"></i></a>
                <a href="#" class="social-icon"><i class="fab fa-linkedin-in"></i></a>
                <a href="#" class="social-icon"><i class="fab fa-instagram"></i></a>
            </div>
            <p>جميع الحقوق محفوظة &copy; 2025 - برنامج إدارة العلاوات والترفيعات</p>
        </div>
    </footer>

    <div class="dark-mode-toggle" id="darkModeToggle">
        <i class="fas fa-moon"></i>
    </div>

    <script src="script.js"></script>
    <script src="reports.js"></script>
</body>
</html>
