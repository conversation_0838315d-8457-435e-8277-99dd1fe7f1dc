'use client';

import { Card, CardBody, CardHeader } from '@nextui-org/react';
import { 
  <PERSON>Chart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  <PERSON><PERSON><PERSON>, 
  Legend, 
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell
} from 'recharts';

// بيانات تجريبية للرسوم البيانية
const employeesByLocation = [
  { name: 'المقر الرئيسي', value: 45 },
  { name: 'الفرع الأول', value: 30 },
  { name: 'الفرع الثاني', value: 25 },
  { name: 'الفرع الثالث', value: 20 },
];

const employeesByJobTitle = [
  { name: 'مدير', value: 10 },
  { name: 'مهندس', value: 25 },
  { name: 'محاسب', value: 15 },
  { name: 'إداري', value: 30 },
  { name: 'فني', value: 20 },
];

const employeesByEducation = [
  { name: 'دكتوراه', value: 5 },
  { name: 'ماجستير', value: 15 },
  { name: 'بكالوريوس', value: 60 },
  { name: 'دبلوم', value: 20 },
];

const monthlyData = [
  { name: 'يناير', appreciations: 4, penalties: 1, leaves: 8 },
  { name: 'فبراير', appreciations: 3, penalties: 2, leaves: 10 },
  { name: 'مارس', appreciations: 5, penalties: 0, leaves: 7 },
  { name: 'أبريل', appreciations: 2, penalties: 3, leaves: 12 },
  { name: 'مايو', appreciations: 6, penalties: 1, leaves: 9 },
  { name: 'يونيو', appreciations: 4, penalties: 2, leaves: 11 },
];

// ألوان للرسوم البيانية
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82ca9d'];

export default function Dashboard() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
      {/* إحصائيات الموظفين حسب موقع العمل */}
      <Card className="shadow-md">
        <CardHeader className="pb-0 pt-4 px-4 flex-col items-start">
          <h4 className="font-bold text-large">الموظفين حسب موقع العمل</h4>
        </CardHeader>
        <CardBody className="overflow-visible py-2">
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={employeesByLocation}
                cx="50%"
                cy="50%"
                labelLine={true}
                label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {employeesByLocation.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </CardBody>
      </Card>

      {/* إحصائيات الموظفين حسب العنوان الوظيفي */}
      <Card className="shadow-md">
        <CardHeader className="pb-0 pt-4 px-4 flex-col items-start">
          <h4 className="font-bold text-large">الموظفين حسب العنوان الوظيفي</h4>
        </CardHeader>
        <CardBody className="overflow-visible py-2">
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={employeesByJobTitle}
                cx="50%"
                cy="50%"
                labelLine={true}
                label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {employeesByJobTitle.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </CardBody>
      </Card>

      {/* إحصائيات الموظفين حسب التحصيل الدراسي */}
      <Card className="shadow-md">
        <CardHeader className="pb-0 pt-4 px-4 flex-col items-start">
          <h4 className="font-bold text-large">الموظفين حسب التحصيل الدراسي</h4>
        </CardHeader>
        <CardBody className="overflow-visible py-2">
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={employeesByEducation}
                cx="50%"
                cy="50%"
                labelLine={true}
                label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {employeesByEducation.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </CardBody>
      </Card>

      {/* إحصائيات شهرية للتشكرات والعقوبات والإجازات */}
      <Card className="shadow-md">
        <CardHeader className="pb-0 pt-4 px-4 flex-col items-start">
          <h4 className="font-bold text-large">إحصائيات شهرية</h4>
        </CardHeader>
        <CardBody className="overflow-visible py-2">
          <ResponsiveContainer width="100%" height={300}>
            <BarChart
              data={monthlyData}
              margin={{
                top: 5,
                right: 30,
                left: 20,
                bottom: 5,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey="appreciations" name="كتب الشكر" fill="#8884d8" />
              <Bar dataKey="penalties" name="العقوبات" fill="#ff8042" />
              <Bar dataKey="leaves" name="الإجازات" fill="#82ca9d" />
            </BarChart>
          </ResponsiveContainer>
        </CardBody>
      </Card>
    </div>
  );
}
