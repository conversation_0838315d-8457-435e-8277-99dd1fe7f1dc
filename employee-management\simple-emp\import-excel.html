<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>استيراد بيانات الموظفين من Excel - نظام إدارة العلاوات والترفيعات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #667eea;
        }

        .header h1 {
            color: #667eea;
            margin-bottom: 10px;
            font-size: 2.2em;
        }

        .step-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: #f9f9f9;
        }

        .step-section h2 {
            color: #667eea;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .upload-area {
            border: 3px dashed #667eea;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            background: white;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: #764ba2;
            background: #f8f9ff;
        }

        .upload-area.dragover {
            border-color: #10b981;
            background: #f0fdf4;
        }

        .file-input {
            display: none;
        }

        .upload-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }

        .upload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .template-section {
            background: #e0f2fe;
            border: 1px solid #0288d1;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .template-btn {
            background: linear-gradient(135deg, #0288d1 0%, #0277bd 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .template-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .preview-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .preview-table th,
        .preview-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #ddd;
        }

        .preview-table th {
            background: #667eea;
            color: white;
            font-weight: 600;
        }

        .preview-table tr:hover {
            background: #f8f9fa;
        }

        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .success {
            border-left-color: #10b981;
            background: #f0fdf4;
            color: #065f46;
        }

        .error {
            border-left-color: #ef4444;
            background: #fef2f2;
            color: #991b1b;
        }

        .warning {
            border-left-color: #f59e0b;
            background: #fef3c7;
            color: #92400e;
        }

        .progress {
            background: #e5e7eb;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-bar {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            height: 20px;
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: 600;
        }

        .back-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            font-size: 16px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .column-mapping {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .mapping-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #ddd;
        }

        .mapping-item label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }

        .mapping-item select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .action-buttons {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            color: white;
            transition: all 0.3s ease;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .btn-info {
            background: linear-gradient(135deg, #0288d1 0%, #0277bd 100%);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- زر العودة -->
        <a href="index.html" class="back-btn">
            <i class="fas fa-arrow-right"></i>
            العودة للرئيسية
        </a>

        <!-- العنوان -->
        <div class="header">
            <h1><i class="fas fa-file-excel"></i> استيراد بيانات الموظفين من Excel</h1>
            <p>قم بتحميل ملف Excel يحتوي على بيانات الموظفين (مطابق لنموذج إضافة الموظف)</p>
        </div>

        <!-- تحميل القالب -->
        <div class="step-section">
            <h2><i class="fas fa-download"></i> الخطوة 1: تحميل القالب</h2>
            <div class="template-section">
                <h3>📋 قالب Excel مطابق لنموذج إضافة الموظف</h3>
                <p><strong>القالب مطابق تماماً لنموذج إضافة الموظف في النظام:</strong></p>
                <ul style="margin: 10px 0; padding-right: 20px;">
                    <li><strong>معلومات الموظف الأساسية:</strong> الرقم الوظيفي، الاسم الرباعي، الجنس، الوصف الوظيفي، العنوان الوظيفي، التحصيل الدراسي، التخصص، موقع العمل</li>
                    <li><strong>معلومات التواريخ:</strong> تاريخ التولد، تاريخ التعيين، تاريخ الإحالة على التقاعد</li>
                    <li><strong>معلومات العلاوة الحالية:</strong> الدرجة الحالية، المرحلة الحالية، الراتب الحالي، تاريخ الاستحقاق الحالي، قدم العلاوة</li>
                    <li><strong>معلومات العلاوة الجديدة:</strong> الدرجة الجديدة، المرحلة الجديدة، الراتب الجديد، تاريخ الاستحقاق الجديد</li>
                    <li><strong>معلومات الترفيع:</strong> تاريخ آخر ترفيع، قدم الترفيع، العنوان الوظيفي الجديد، تاريخ الترفيع القادم</li>
                </ul>
                <p><strong>✅ مطابق 100%</strong> لجميع حقول النموذج الفعلي في النظام</p>
                <p><strong>✅ يشمل 3 موظفين</strong> كأمثلة بالبيانات الكاملة</p>
                <button class="template-btn" onclick="downloadTemplate()">
                    <i class="fas fa-download"></i>
                    تحميل القالب المطابق للنموذج
                </button>
            </div>
        </div>

        <!-- رفع الملف -->
        <div class="step-section">
            <h2><i class="fas fa-upload"></i> الخطوة 2: رفع ملف Excel</h2>
            <div class="upload-area" id="uploadArea" onclick="document.getElementById('fileInput').click()">
                <i class="fas fa-cloud-upload-alt" style="font-size: 3em; color: #667eea; margin-bottom: 20px;"></i>
                <h3>اسحب ملف Excel هنا أو انقر للاختيار</h3>
                <p>يدعم ملفات .xlsx و .xls</p>
                <input type="file" id="fileInput" class="file-input" accept=".xlsx,.xls" onchange="handleFile(this.files[0])">
                <button class="upload-btn">
                    <i class="fas fa-folder-open"></i>
                    اختيار ملف
                </button>
            </div>
            <div id="fileInfo" style="display: none; margin-top: 15px;"></div>
        </div>

        <!-- معاينة البيانات -->
        <div class="step-section" id="previewSection" style="display: none;">
            <h2><i class="fas fa-eye"></i> الخطوة 3: معاينة البيانات</h2>
            <div id="columnMapping" class="column-mapping"></div>
            <div id="previewContainer"></div>
            <div id="importResult"></div>
            <div class="action-buttons">
                <button id="downloadTemplate" class="btn btn-secondary">
                    <i class="fas fa-download"></i>
                    تحميل قالب الإكسل
                </button>
                <button id="extractProfileData" class="btn btn-info">
                    <i class="fas fa-sync"></i>
                    استخراج البيانات التعريفية
                </button>
            </div>
            <button class="upload-btn" onclick="importData()" id="importBtn" style="display: none;">
                <i class="fas fa-database"></i>
                استيراد البيانات
            </button>
        </div>
    </div>

    <script src="debug-storage.js"></script>
    <script>
        let excelData = [];
        let columnHeaders = [];

        // إعداد السحب والإفلات
        const uploadArea = document.getElementById('uploadArea');
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        // تحميل القالب الشامل
        function downloadTemplate() {
            const templateData = [
                // رؤوس الأعمدة - مطابقة تماماً للنموذج الفعلي
                [
                    'الرقم الوظيفي', 'الاسم الرباعي', 'الجنس', 'الوصف الوظيفي', 'العنوان الوظيفي',
                    'التحصيل الدراسي', 'التخصص', 'موقع العمل', 'تاريخ التولد', 'تاريخ التعيين',
                    'تاريخ الإحالة على التقاعد', 'الدرجة الحالية', 'المرحلة الحالية', 'الراتب الحالي',
                    'تاريخ الاستحقاق الحالي', 'قدم العلاوة (بالأشهر)', 'الدرجة الجديدة', 'المرحلة الجديدة',
                    'الراتب الجديد', 'تاريخ الاستحقاق الجديد', 'تاريخ آخر ترفيع', 'قدم الترفيع (بالأشهر)',
                    'العنوان الوظيفي الجديد', 'تاريخ الترفيع القادم'
                ],

                // بيانات تجريبية - الموظف الأول
                [
                    '12345', 'أحمد محمد علي حسن', 'ذكر', 'تدريسي', 'مدرس',
                    'بكالوريوس', 'هندسة الحاسوب', 'المقر الرئيسي', '1985-03-20', '2015-01-15',
                    '2050-03-20', '5', '3', '850000', '2025-01-15', '60', '5', '4',
                    '900000', '2025-07-15', '2020-01-15', '48', 'مدرس أول', '2025-01-15'
                ],

                // بيانات تجريبية - الموظف الثاني
                [
                    '12346', 'فاطمة أحمد حسن محمود', 'أنثى', 'تدريسي', 'مدرس مساعد',
                    'ماجستير', 'الرياضيات', 'الفرع الأول', '1990-07-12', '2018-06-01',
                    '2055-07-12', '4', '2', '720000', '2025-06-01', '36', '4', '3',
                    '780000', '2025-12-01', '2021-06-01', '30', 'مدرس', '2024-06-01'
                ],

                // بيانات تجريبية - الموظف الثالث
                [
                    '12347', 'محمد خالد عبدالله صالح', 'ذكر', 'تدريسي', 'أستاذ مساعد',
                    'دكتوراه', 'الفيزياء', 'المقر الرئيسي', '1982-11-05', '2012-09-10',
                    '2047-11-05', '6', '4', '950000', '2024-09-10', '72', '6', '5',
                    '1000000', '2025-03-10', '2019-09-10', '60', 'أستاذ', '2024-09-10'
                ]
            ];

            const ws = XLSX.utils.aoa_to_sheet(templateData);

            // تنسيق العرض
            const range = XLSX.utils.decode_range(ws['!ref']);
            ws['!cols'] = [];
            for (let i = 0; i <= range.e.c; i++) {
                ws['!cols'][i] = { wch: 20 }; // عرض الأعمدة
            }

            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'ملف_الموظف_الشامل');
            XLSX.writeFile(wb, 'قالب_ملف_الموظف_مطابق_للنظام.xlsx');
        }

        // معالجة الملف المرفوع
        function handleFile(file) {
            if (!file) return;

            const fileInfo = document.getElementById('fileInfo');
            fileInfo.style.display = 'block';
            fileInfo.innerHTML = `
                <div class="result">
                    <h4><i class="fas fa-file-excel"></i> معلومات الملف:</h4>
                    <p><strong>الاسم:</strong> ${file.name}</p>
                    <p><strong>الحجم:</strong> ${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                    <p><strong>النوع:</strong> ${file.type}</p>
                    <div class="progress">
                        <div class="progress-bar" id="progressBar" style="width: 0%">0%</div>
                    </div>
                </div>
            `;

            const reader = new FileReader();
            
            reader.onprogress = function(e) {
                if (e.lengthComputable) {
                    const percentLoaded = Math.round((e.loaded / e.total) * 100);
                    const progressBar = document.getElementById('progressBar');
                    progressBar.style.width = percentLoaded + '%';
                    progressBar.textContent = percentLoaded + '%';
                }
            };

            reader.onload = function(e) {
                try {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, { type: 'array' });
                    const sheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[sheetName];
                    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
                    
                    if (jsonData.length > 0) {
                        columnHeaders = jsonData[0];
                        excelData = jsonData.slice(1);
                        showPreview();
                    } else {
                        showError('الملف فارغ أو لا يحتوي على بيانات');
                    }
                } catch (error) {
                    showError('خطأ في قراءة الملف: ' + error.message);
                }
            };

            reader.onerror = function() {
                showError('خطأ في تحميل الملف');
            };

            reader.readAsArrayBuffer(file);
        }

        // عرض معاينة البيانات
        function showPreview() {
            const previewSection = document.getElementById('previewSection');
            const columnMapping = document.getElementById('columnMapping');
            const previewContainer = document.getElementById('previewContainer');
            
            previewSection.style.display = 'block';

            // إنشاء خريطة الأعمدة - مطابقة تماماً للنموذج الفعلي
            const systemFields = [
                { key: 'employeeId', label: 'الرقم الوظيفي', required: false },
                { key: 'fullName', label: 'الاسم الرباعي', required: true },
                { key: 'gender', label: 'الجنس', required: true },
                { key: 'jobDescription', label: 'الوصف الوظيفي', required: true },
                { key: 'jobTitle', label: 'العنوان الوظيفي', required: true },
                { key: 'education', label: 'التحصيل الدراسي', required: true },
                { key: 'specialization', label: 'التخصص' },
                { key: 'workLocation', label: 'موقع العمل', required: true },
                { key: 'birthDate', label: 'تاريخ التولد', required: true },
                { key: 'hireDate', label: 'تاريخ التعيين', required: true },
                { key: 'retirementDate', label: 'تاريخ الإحالة على التقاعد' },
                { key: 'currentDegree', label: 'الدرجة الحالية', required: true },
                { key: 'currentStage', label: 'المرحلة الحالية', required: true },
                { key: 'currentSalary', label: 'الراتب الحالي' },
                { key: 'currentDueDate', label: 'تاريخ الاستحقاق الحالي', required: true },
                { key: 'allowanceSeniority', label: 'قدم العلاوة (بالأشهر)', required: true },
                { key: 'newDegree', label: 'الدرجة الجديدة' },
                { key: 'newStage', label: 'المرحلة الجديدة' },
                { key: 'newSalary', label: 'الراتب الجديد' },
                { key: 'newDueDate', label: 'تاريخ الاستحقاق الجديد' },
                { key: 'lastPromotionDate', label: 'تاريخ آخر ترفيع', required: true },
                { key: 'promotionSeniority', label: 'قدم الترفيع (بالأشهر)', required: true },
                { key: 'newJobTitle', label: 'العنوان الوظيفي الجديد' },
                { key: 'nextPromotionDate', label: 'تاريخ الترفيع القادم' }
            ];

            let mappingHTML = '<h3>ربط الأعمدة: <span style="color: #ef4444;">*</span> = حقول إجبارية</h3>';
            mappingHTML += '<button type="button" onclick="autoMapFields()" style="background: #10b981; color: white; padding: 8px 16px; border: none; border-radius: 4px; margin-bottom: 15px; cursor: pointer;">🔗 ربط تلقائي للحقول</button>';

            // تجميع الحقول حسب الفئة (مطابق للنموذج الفعلي)
            const fieldGroups = {
                'معلومات الموظف الأساسية': systemFields.slice(0, 8),
                'معلومات التواريخ': systemFields.slice(8, 11),
                'معلومات العلاوة الحالية': systemFields.slice(11, 16),
                'معلومات العلاوة الجديدة': systemFields.slice(16, 20),
                'معلومات الترفيع': systemFields.slice(20)
            };

            Object.keys(fieldGroups).forEach(groupName => {
                mappingHTML += `<h4 style="color: #667eea; margin-top: 20px; margin-bottom: 10px;">${groupName}</h4>`;
                mappingHTML += '<div class="column-mapping">';

                fieldGroups[groupName].forEach(field => {
                    const requiredMark = field.required ? '<span style="color: #ef4444;">*</span>' : '';
                    mappingHTML += `
                        <div class="mapping-item">
                            <label>${field.label} ${requiredMark}:</label>
                            <select id="map_${field.key}">
                                <option value="">-- اختر العمود --</option>
                                ${columnHeaders.map((header, index) => {
                                    // محاولة الربط التلقائي الذكي
                                    const isMatch = header === field.label ||
                                                  header.includes(field.label) ||
                                                  (field.key === 'employeeId' && header.includes('الرقم الوظيفي')) ||
                                                  (field.key === 'fullName' && header.includes('الاسم الرباعي')) ||
                                                  (field.key === 'gender' && header.includes('الجنس')) ||
                                                  (field.key === 'jobDescription' && header.includes('الوصف الوظيفي')) ||
                                                  (field.key === 'jobTitle' && header.includes('العنوان الوظيفي')) ||
                                                  (field.key === 'education' && header.includes('التحصيل الدراسي')) ||
                                                  (field.key === 'specialization' && header.includes('التخصص')) ||
                                                  (field.key === 'workLocation' && header.includes('موقع العمل')) ||
                                                  (field.key === 'birthDate' && header.includes('تاريخ التولد')) ||
                                                  (field.key === 'hireDate' && header.includes('تاريخ التعيين')) ||
                                                  (field.key === 'currentDegree' && header.includes('الدرجة الحالية')) ||
                                                  (field.key === 'currentStage' && header.includes('المرحلة الحالية')) ||
                                                  (field.key === 'currentSalary' && header.includes('الراتب الحالي')) ||
                                                  (field.key === 'currentDueDate' && header.includes('تاريخ الاستحقاق الحالي')) ||
                                                  (field.key === 'allowanceSeniority' && header.includes('قدم العلاوة')) ||
                                                  (field.key === 'lastPromotionDate' && header.includes('تاريخ آخر ترفيع')) ||
                                                  (field.key === 'promotionSeniority' && header.includes('قدم الترفيع'));
                                    return `<option value="${index}" ${isMatch ? 'selected' : ''}>${header}</option>`;
                                }).join('')}
                            </select>
                        </div>
                    `;
                });

                mappingHTML += '</div>';
            });

            columnMapping.innerHTML = mappingHTML;



            // عرض معاينة البيانات
            let previewHTML = `
                <h3>معاينة البيانات (أول 5 صفوف):</h3>
                <table class="preview-table">
                    <thead>
                        <tr>${columnHeaders.map(header => `<th>${header}</th>`).join('')}</tr>
                    </thead>
                    <tbody>
                        ${excelData.slice(0, 5).map(row => 
                            `<tr>${row.map(cell => `<td>${cell || '-'}</td>`).join('')}</tr>`
                        ).join('')}
                    </tbody>
                </table>
                <p style="margin-top: 10px; color: #666;">
                    <strong>إجمالي الصفوف:</strong> ${excelData.length}
                </p>
            `;

            previewContainer.innerHTML = previewHTML;
            document.getElementById('importBtn').style.display = 'inline-block';

            // إضافة زر للاستيراد المباشر إذا كان القالب الخاص بنا
            if (columnHeaders && columnHeaders.length === 24 && columnHeaders[0] === 'الرقم الوظيفي') {
                document.getElementById('importBtn').innerHTML = '<i class="fas fa-upload"></i> استيراد البيانات (تم التعرف على القالب)';
                document.getElementById('importBtn').onclick = importDirectly;
            }
        }

        // استيراد البيانات
        function importData() {
            const resultDiv = document.getElementById('importResult');
            
            try {
                // جمع خريطة الأعمدة
                const mapping = {};
                const systemFields = [
                    { key: 'employeeId', label: 'الرقم الوظيفي', required: false },
                    { key: 'fullName', label: 'الاسم الرباعي', required: true },
                    { key: 'gender', label: 'الجنس', required: true },
                    { key: 'jobDescription', label: 'الوصف الوظيفي', required: true },
                    { key: 'jobTitle', label: 'العنوان الوظيفي', required: true },
                    { key: 'education', label: 'التحصيل الدراسي', required: true },
                    { key: 'specialization', label: 'التخصص' },
                    { key: 'workLocation', label: 'موقع العمل', required: true },
                    { key: 'birthDate', label: 'تاريخ التولد', required: true },
                    { key: 'hireDate', label: 'تاريخ التعيين', required: true },
                    { key: 'retirementDate', label: 'تاريخ الإحالة على التقاعد' },
                    { key: 'currentDegree', label: 'الدرجة الحالية', required: true },
                    { key: 'currentStage', label: 'المرحلة الحالية', required: true },
                    { key: 'currentSalary', label: 'الراتب الحالي' },
                    { key: 'currentDueDate', label: 'تاريخ الاستحقاق الحالي', required: true },
                    { key: 'allowanceSeniority', label: 'قدم العلاوة (بالأشهر)', required: true },
                    { key: 'newDegree', label: 'الدرجة الجديدة' },
                    { key: 'newStage', label: 'المرحلة الجديدة' },
                    { key: 'newSalary', label: 'الراتب الجديد' },
                    { key: 'newDueDate', label: 'تاريخ الاستحقاق الجديد' },
                    { key: 'lastPromotionDate', label: 'تاريخ آخر ترفيع', required: true },
                    { key: 'promotionSeniority', label: 'قدم الترفيع (بالأشهر)', required: true },
                    { key: 'newJobTitle', label: 'العنوان الوظيفي الجديد' },
                    { key: 'nextPromotionDate', label: 'تاريخ الترفيع القادم' }
                ];

                systemFields.forEach(field => {
                    const select = document.getElementById(`map_${field.key}`);
                    if (select && select.value !== '') {
                        mapping[field.key] = parseInt(select.value);
                    }
                });

                // التحقق من الحقول المطلوبة (مطابق للنموذج الفعلي)
                const requiredFields = ['fullName', 'gender', 'jobDescription', 'jobTitle', 'education', 'workLocation', 'birthDate', 'hireDate', 'currentDegree', 'currentStage', 'currentDueDate', 'allowanceSeniority', 'lastPromotionDate', 'promotionSeniority'];
                const missingFields = [];

                requiredFields.forEach(field => {
                    if (!mapping[field]) {
                        const fieldLabel = systemFields.find(f => f.key === field)?.label || field;
                        missingFields.push(fieldLabel);
                    }
                });

                if (missingFields.length > 0) {
                    throw new Error('يجب ربط الحقول الإجبارية التالية: ' + missingFields.join(', '));
                }

                // تحويل البيانات
                const employees = [];
                let successCount = 0;
                let errorCount = 0;
                const errors = [];

                excelData.forEach((row, index) => {
                    try {
                        // إنشاء رقم وظيفي تلقائي إذا لم يتم تحديده
                        const autoEmployeeId = `EMP${Date.now()}${index + 1}`;
                        
                        // إنشاء كائن الموظف (مطابق للنموذج الفعلي)
                        const employee = {
                            // معرف فريد
                            id: Date.now() + index,
                            createdAt: new Date().toISOString(),

                            // معلومات الموظف الأساسية
                            employeeId: row[mapping.employeeId] || autoEmployeeId,
                            employeeNumber: row[mapping.employeeId] || autoEmployeeId,
                            fullName: row[mapping.fullName] || '',
                            gender: row[mapping.gender] || '',
                            jobDescription: row[mapping.jobDescription] || '',
                            jobTitle: row[mapping.jobTitle] || '',
                            education: row[mapping.education] || '',
                            specialization: row[mapping.specialization] || '',
                            workLocation: row[mapping.workLocation] || '',

                            // معلومات التواريخ
                            birthDate: formatExcelDate(row[mapping.birthDate]) || '',
                            hireDate: formatExcelDate(row[mapping.hireDate]) || '',
                            retirementDate: formatExcelDate(row[mapping.retirementDate]) || '',

                            // معلومات العلاوة الحالية
                            currentDegree: row[mapping.currentDegree] || 1,
                            currentStage: row[mapping.currentStage] || 1,
                            currentSalary: row[mapping.currentSalary] || '0',
                            currentDueDate: formatExcelDate(row[mapping.currentDueDate]) || '',
                            allowanceSeniority: row[mapping.allowanceSeniority] || 0,

                            // معلومات العلاوة الجديدة
                            newDegree: row[mapping.newDegree] || '',
                            newStage: row[mapping.newStage] || '',
                            newSalary: row[mapping.newSalary] || '',
                            newDueDate: formatExcelDate(row[mapping.newDueDate]) || '',

                            // معلومات الترفيع
                            lastPromotionDate: formatExcelDate(row[mapping.lastPromotionDate]) || '',
                            promotionSeniority: row[mapping.promotionSeniority] || 0,
                            newJobTitle: row[mapping.newJobTitle] || '',
                            nextPromotionDate: formatExcelDate(row[mapping.nextPromotionDate]) || '',

                            // للتوافق مع النظام القديم
                            name: row[mapping.fullName] || '',
                            seniority: calculateSeniority(formatExcelDate(row[mapping.hireDate]))
                        };

                        employees.push(employee);
                        successCount++;
                    } catch (error) {
                        errors.push(`الصف ${index + 2}: ${error.message}`);
                        errorCount++;
                    }
                });

                // حفظ البيانات
                if (employees.length > 0) {
                    const existingEmployees = JSON.parse(localStorage.getItem('employees') || '[]');
                    const allEmployees = [...existingEmployees, ...employees];
                    localStorage.setItem('employees', JSON.stringify(allEmployees));

                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h4>✅ تم استيراد البيانات بنجاح!</h4>
                        <p><strong>تم استيراد:</strong> ${successCount} موظف</p>
                        ${errorCount > 0 ? `<p><strong>أخطاء:</strong> ${errorCount}</p>` : ''}
                        ${errors.length > 0 ? `<details><summary>عرض الأخطاء</summary><ul>${errors.map(error => `<li>${error}</li>`).join('')}</ul></details>` : ''}
                        <p style="margin-top: 15px;">
                            <a href="employees-list.html" class="template-btn">
                                <i class="fas fa-users"></i>
                                عرض قائمة الموظفين
                            </a>
                        </p>
                    `;
                } else {
                    throw new Error('لم يتم استيراد أي موظف');
                }

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<h4>❌ خطأ في الاستيراد: ${error.message}</h4>`;
            }
        }

        // تنسيق تاريخ Excel
        function formatExcelDate(excelDate) {
            if (!excelDate) return null;
            
            // إذا كان التاريخ رقم (Excel date serial)
            if (typeof excelDate === 'number') {
                const date = new Date((excelDate - 25569) * 86400 * 1000);
                return date.toISOString().split('T')[0];
            }
            
            // إذا كان التاريخ نص
            if (typeof excelDate === 'string') {
                const date = new Date(excelDate);
                if (!isNaN(date.getTime())) {
                    return date.toISOString().split('T')[0];
                }
            }
            
            return null;
        }

        // حساب سنوات الخدمة
        function calculateSeniority(hireDate) {
            if (!hireDate) return 0;

            const hire = new Date(hireDate);
            const today = new Date();
            const years = Math.floor((today - hire) / (365.25 * 24 * 60 * 60 * 1000));

            return years >= 0 ? years : 0;
        }

        // استيراد مباشر للقالب الخاص بنا
        function importDirectly() {
            const resultDiv = document.getElementById('importResult');

            try {
                // الربط المباشر للقالب
                const mapping = {
                    employeeId: 0,
                    fullName: 1,
                    gender: 2,
                    jobDescription: 3,
                    jobTitle: 4,
                    education: 5,
                    specialization: 6,
                    workLocation: 7,
                    birthDate: 8,
                    hireDate: 9,
                    retirementDate: 10,
                    currentDegree: 11,
                    currentStage: 12,
                    currentSalary: 13,
                    currentDueDate: 14,
                    allowanceSeniority: 15,
                    newDegree: 16,
                    newStage: 17,
                    newSalary: 18,
                    newDueDate: 19,
                    lastPromotionDate: 20,
                    promotionSeniority: 21,
                    newJobTitle: 22,
                    nextPromotionDate: 23
                };

                // تحويل البيانات
                const employees = [];
                let successCount = 0;
                let errorCount = 0;
                const errors = [];

                excelData.forEach((row, index) => {
                    try {
                        // التحقق من الحقول الإجبارية
                        if (!row[mapping.employeeId] || row[mapping.employeeId].toString().trim() === '') {
                            errors.push(`الصف ${index + 2}: الرقم الوظيفي مطلوب`);
                            errorCount++;
                            return;
                        }

                        if (!row[mapping.fullName] || row[mapping.fullName].toString().trim() === '') {
                            errors.push(`الصف ${index + 2}: الاسم الرباعي مطلوب`);
                            errorCount++;
                            return;
                        }

                        // إنشاء كائن الموظف
                        const employee = {
                            id: Date.now() + index,
                            createdAt: new Date().toISOString(),
                            employeeId: row[mapping.employeeId] || '',
                            fullName: row[mapping.fullName] || '',
                            gender: row[mapping.gender] || '',
                            jobDescription: row[mapping.jobDescription] || '',
                            jobTitle: row[mapping.jobTitle] || '',
                            education: row[mapping.education] || '',
                            specialization: row[mapping.specialization] || '',
                            workLocation: row[mapping.workLocation] || '',
                            birthDate: formatExcelDate(row[mapping.birthDate]) || '',
                            hireDate: formatExcelDate(row[mapping.hireDate]) || '',
                            retirementDate: formatExcelDate(row[mapping.retirementDate]) || '',
                            currentDegree: row[mapping.currentDegree] || 1,
                            currentStage: row[mapping.currentStage] || 1,
                            currentSalary: row[mapping.currentSalary] || '0',
                            currentDueDate: formatExcelDate(row[mapping.currentDueDate]) || '',
                            allowanceSeniority: row[mapping.allowanceSeniority] || 0,
                            newDegree: row[mapping.newDegree] || '',
                            newStage: row[mapping.newStage] || '',
                            newSalary: row[mapping.newSalary] || '',
                            newDueDate: formatExcelDate(row[mapping.newDueDate]) || '',
                            lastPromotionDate: formatExcelDate(row[mapping.lastPromotionDate]) || '',
                            promotionSeniority: row[mapping.promotionSeniority] || 0,
                            newJobTitle: row[mapping.newJobTitle] || '',
                            nextPromotionDate: formatExcelDate(row[mapping.nextPromotionDate]) || '',
                            employeeNumber: row[mapping.employeeId] || '',
                            name: row[mapping.fullName] || '',
                            seniority: calculateSeniority(formatExcelDate(row[mapping.hireDate]))
                        };

                        employees.push(employee);
                        successCount++;
                    } catch (error) {
                        errors.push(`الصف ${index + 2}: ${error.message}`);
                        errorCount++;
                    }
                });

                // حفظ البيانات
                if (employees.length > 0) {
                    const existingEmployees = JSON.parse(localStorage.getItem('employees') || '[]');
                    const allEmployees = [...existingEmployees, ...employees];
                    localStorage.setItem('employees', JSON.stringify(allEmployees));

                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h4>✅ تم استيراد البيانات بنجاح!</h4>
                        <p><strong>تم استيراد:</strong> ${successCount} موظف</p>
                        ${errorCount > 0 ? `<p><strong>أخطاء:</strong> ${errorCount}</p>` : ''}
                        ${errors.length > 0 ? `<details><summary>عرض الأخطاء</summary><ul>${errors.map(error => `<li>${error}</li>`).join('')}</ul></details>` : ''}
                        <p style="margin-top: 15px;">
                            <a href="employees-list.html" class="template-btn">
                                <i class="fas fa-users"></i>
                                عرض قائمة الموظفين
                            </a>
                        </p>
                    `;
                } else {
                    throw new Error('لم يتم استيراد أي موظف');
                }

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<h4>❌ خطأ في الاستيراد: ${error.message}</h4>`;
            }
        }

        // الربط التلقائي للحقول
        function autoMapFields() {
            const systemFields = [
                { key: 'employeeId', label: 'الرقم الوظيفي', required: false },
                { key: 'fullName', label: 'الاسم الرباعي', required: true },
                { key: 'gender', label: 'الجنس', required: true },
                { key: 'jobDescription', label: 'الوصف الوظيفي', required: true },
                { key: 'jobTitle', label: 'العنوان الوظيفي', required: true },
                { key: 'education', label: 'التحصيل الدراسي', required: true },
                { key: 'specialization', label: 'التخصص' },
                { key: 'workLocation', label: 'موقع العمل', required: true },
                { key: 'birthDate', label: 'تاريخ التولد', required: true },
                { key: 'hireDate', label: 'تاريخ التعيين', required: true },
                { key: 'retirementDate', label: 'تاريخ الإحالة على التقاعد' },
                { key: 'currentDegree', label: 'الدرجة الحالية', required: true },
                { key: 'currentStage', label: 'المرحلة الحالية', required: true },
                { key: 'currentSalary', label: 'الراتب الحالي' },
                { key: 'currentDueDate', label: 'تاريخ الاستحقاق الحالي', required: true },
                { key: 'allowanceSeniority', label: 'قدم العلاوة (بالأشهر)', required: true },
                { key: 'newDegree', label: 'الدرجة الجديدة' },
                { key: 'newStage', label: 'المرحلة الجديدة' },
                { key: 'newSalary', label: 'الراتب الجديد' },
                { key: 'newDueDate', label: 'تاريخ الاستحقاق الجديد' },
                { key: 'lastPromotionDate', label: 'تاريخ آخر ترفيع', required: true },
                { key: 'promotionSeniority', label: 'قدم الترفيع (بالأشهر)', required: true },
                { key: 'newJobTitle', label: 'العنوان الوظيفي الجديد' },
                { key: 'nextPromotionDate', label: 'تاريخ الترفيع القادم' }
            ];

            let mappedCount = 0;
            systemFields.forEach(field => {
                const select = document.getElementById(`map_${field.key}`);
                if (select) {
                    for (let i = 0; i < columnHeaders.length; i++) {
                        if (columnHeaders[i] === field.label) {
                            select.value = i;
                            mappedCount++;
                            break;
                        }
                    }
                }
            });

            alert(`تم ربط ${mappedCount} حقل تلقائياً. تحقق من الربط قبل الاستيراد.`);
        }

        // عرض خطأ
        function showError(message) {
            const fileInfo = document.getElementById('fileInfo');
            fileInfo.style.display = 'block';
            fileInfo.innerHTML = `
                <div class="result error">
                    <h4>❌ خطأ:</h4>
                    <p>${message}</p>
                </div>
            `;
        }

        document.getElementById('extractProfileData').addEventListener('click', function() {
            extractProfileDataFromEmployees();
            showNotification('تم استخراج البيانات التعريفية بنجاح', 'success');
        });
    </script>
</body>
</html>
