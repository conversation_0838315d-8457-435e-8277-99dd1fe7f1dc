/**
 * نظام إدارة ملفات الموظفين المنفصلة
 * كل موظف له ملف منفصل لكتب الشكر
 */

class EmployeeFileManager {
    constructor() {
        this.storagePrefix = 'employee_';
    }

    // إنشاء مفتاح التخزين للموظف
    getEmployeeKey(employeeId, dataType = 'thanks') {
        return `${this.storagePrefix}${employeeId}_${dataType}`;
    }

    // حفظ كتب الشكر للموظف
    saveEmployeeThanks(employeeId, thanks) {
        const key = this.getEmployeeKey(employeeId, 'thanks');
        localStorage.setItem(key, JSON.stringify(thanks));
        console.log(`تم حفظ ${thanks.length} كتاب شكر للموظف ${employeeId}`);
    }

    // تحميل كتب الشكر للموظف
    loadEmployeeThanks(employeeId) {
        const key = this.getEmployeeKey(employeeId, 'thanks');
        const data = localStorage.getItem(key);
        return data ? JSON.parse(data) : [];
    }

    // إضافة كتاب شكر للموظف
    addThankToEmployee(employeeId, thank) {
        const thanks = this.loadEmployeeThanks(employeeId);
        thank.id = Date.now();
        thank.employeeId = parseInt(employeeId);
        thanks.push(thank);
        this.saveEmployeeThanks(employeeId, thanks);
        return thank;
    }

    // حذف كتاب شكر من ملف الموظف
    removeThankFromEmployee(employeeId, thankId) {
        const thanks = this.loadEmployeeThanks(employeeId);
        const updatedThanks = thanks.filter(thank => thank.id !== thankId);
        this.saveEmployeeThanks(employeeId, updatedThanks);
        return updatedThanks.length < thanks.length; // true إذا تم الحذف
    }

    // تحديث كتاب شكر للموظف
    updateEmployeeThank(employeeId, thankId, updatedThank) {
        const thanks = this.loadEmployeeThanks(employeeId);
        const thankIndex = thanks.findIndex(thank => thank.id === thankId);
        
        if (thankIndex !== -1) {
            thanks[thankIndex] = { ...thanks[thankIndex], ...updatedThank };
            this.saveEmployeeThanks(employeeId, thanks);
            return thanks[thankIndex];
        }
        return null;
    }

    // الحصول على جميع كتب الشكر لجميع الموظفين
    getAllThanks() {
        const employees = JSON.parse(localStorage.getItem('employees') || '[]');
        const allThanks = [];

        employees.forEach(employee => {
            const employeeThanks = this.loadEmployeeThanks(employee.id);
            allThanks.push(...employeeThanks);
        });

        return allThanks;
    }

    // الحصول على إحصائيات كتب الشكر للموظف
    getEmployeeThanksStats(employeeId) {
        const thanks = this.loadEmployeeThanks(employeeId);
        
        const stats = {
            total: thanks.length,
            appreciation: thanks.filter(t => t.type === 'appreciation').length,
            ministerial: thanks.filter(t => t.type === 'ministerial').length,
            presidential: thanks.filter(t => t.type === 'presidential').length,
            thisYear: thanks.filter(t => {
                const thankYear = new Date(t.date).getFullYear();
                const currentYear = new Date().getFullYear();
                return thankYear === currentYear;
            }).length
        };

        return stats;
    }

    // نقل كتب الشكر من النظام القديم إلى النظام الجديد
    migrateFromOldSystem() {
        console.log('بدء نقل البيانات من النظام القديم...');
        
        const oldThanks = JSON.parse(localStorage.getItem('thanks') || '[]');
        const employees = JSON.parse(localStorage.getItem('employees') || '[]');
        
        let migratedCount = 0;

        // تنظيم كتب الشكر حسب الموظف
        const thanksByEmployee = {};
        oldThanks.forEach(thank => {
            if (!thanksByEmployee[thank.employeeId]) {
                thanksByEmployee[thank.employeeId] = [];
            }
            thanksByEmployee[thank.employeeId].push(thank);
        });

        // حفظ كتب الشكر في ملفات منفصلة
        Object.keys(thanksByEmployee).forEach(employeeId => {
            this.saveEmployeeThanks(employeeId, thanksByEmployee[employeeId]);
            migratedCount += thanksByEmployee[employeeId].length;
        });

        console.log(`تم نقل ${migratedCount} كتاب شكر إلى النظام الجديد`);
        
        // إنشاء نسخة احتياطية من النظام القديم
        localStorage.setItem('thanks_backup', JSON.stringify(oldThanks));
        
        return migratedCount;
    }

    // البحث في كتب الشكر
    searchThanks(searchTerm, employeeId = null) {
        let thanks;
        
        if (employeeId) {
            thanks = this.loadEmployeeThanks(employeeId);
        } else {
            thanks = this.getAllThanks();
        }

        return thanks.filter(thank => {
            const employee = this.getEmployeeById(thank.employeeId);
            const employeeName = employee ? employee.name : '';
            
            return employeeName.includes(searchTerm) ||
                   thank.type.includes(searchTerm) ||
                   thank.issuer?.includes(searchTerm) ||
                   thank.reason?.includes(searchTerm);
        });
    }

    // مساعد للحصول على بيانات الموظف
    getEmployeeById(employeeId) {
        const employees = JSON.parse(localStorage.getItem('employees') || '[]');
        return employees.find(emp => emp.id == employeeId);
    }

    // تنظيف ملفات الموظفين المحذوفين
    cleanupDeletedEmployees() {
        const employees = JSON.parse(localStorage.getItem('employees') || '[]');
        const activeEmployeeIds = employees.map(emp => emp.id.toString());
        
        let cleanedCount = 0;

        // البحث عن ملفات الموظفين في التخزين المحلي
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            
            if (key && key.startsWith(this.storagePrefix)) {
                const parts = key.split('_');
                if (parts.length >= 3) {
                    const employeeId = parts[1];
                    
                    // إذا لم يعد الموظف موجوداً، احذف ملفه
                    if (!activeEmployeeIds.includes(employeeId)) {
                        localStorage.removeItem(key);
                        cleanedCount++;
                        console.log(`تم حذف ملف الموظف المحذوف: ${employeeId}`);
                    }
                }
            }
        }

        return cleanedCount;
    }

    // تصدير بيانات موظف معين
    exportEmployeeData(employeeId) {
        const employee = this.getEmployeeById(employeeId);
        const thanks = this.loadEmployeeThanks(employeeId);
        const stats = this.getEmployeeThanksStats(employeeId);

        return {
            employee: employee,
            thanks: thanks,
            stats: stats,
            exportDate: new Date().toISOString()
        };
    }

    // استيراد بيانات موظف
    importEmployeeData(employeeId, data) {
        if (data.thanks && Array.isArray(data.thanks)) {
            this.saveEmployeeThanks(employeeId, data.thanks);
            return data.thanks.length;
        }
        return 0;
    }
}

// إنشاء مثيل عام للاستخدام
window.employeeFileManager = new EmployeeFileManager();
