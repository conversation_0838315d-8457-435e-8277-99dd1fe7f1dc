@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --primary: #0070f3;
  --primary-foreground: #ffffff;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
    --primary: #3694ff;
    --primary-foreground: #ffffff;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* تخصيص الألوان للتطبيق */
.bg-primary {
  background-color: var(--primary);
}

.text-primary {
  color: var(--primary);
}

/* تخصيص الاتجاه للغة العربية */
html[dir="rtl"] * {
  text-align: right;
}

/* تخصيص الخطوط للغة العربية */
@font-face {
  font-family: 'Cairo';
  src: url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700&display=swap');
}

html[dir="rtl"] body {
  font-family: 'Cairo', Arial, sans-serif;
}
