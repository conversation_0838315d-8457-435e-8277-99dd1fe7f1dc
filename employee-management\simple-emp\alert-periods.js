// انتظر حتى يتم تحميل المستند بالكامل
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة صفحة فترات التنبيه
    initAlertPeriodsPage();
});

// تهيئة صفحة فترات التنبيه
function initAlertPeriodsPage() {
    // تحميل فترات التنبيه
    loadAlertPeriods();
    
    // تهيئة نموذج إضافة فترة تنبيه
    initAddAlertPeriodForm();
    
    // تهيئة البحث والتصفية
    initSearchAndFilter();
    
    // تهيئة النوافذ المنبثقة
    initModals();
}

// تحميل فترات التنبيه
function loadAlertPeriods() {
    // في الإصدار النهائي، سيتم استبدال هذا بطلب API لجلب البيانات من الخادم
    // هنا نستخدم بيانات تجريبية للعرض
    
    // التحقق من وجود بيانات في التخزين المحلي
    let alertPeriods = localStorage.getItem('alertPeriods');
    
    if (!alertPeriods) {
        // إذا لم تكن هناك بيانات، استخدم بيانات افتراضية
        alertPeriods = [
            { id: 1, name: 'تنبيه العلاوة قبل شهر', type: 'allowance', days: 30, description: 'تنبيه قبل موعد استحقاق العلاوة بشهر' },
            { id: 2, name: 'تنبيه العلاوة قبل أسبوعين', type: 'allowance', days: 14, description: 'تنبيه قبل موعد استحقاق العلاوة بأسبوعين' },
            { id: 3, name: 'تنبيه الترفيع قبل شهرين', type: 'promotion', days: 60, description: 'تنبيه قبل موعد استحقاق الترفيع بشهرين' },
            { id: 4, name: 'تنبيه الترفيع قبل شهر', type: 'promotion', days: 30, description: 'تنبيه قبل موعد استحقاق الترفيع بشهر' },
            { id: 5, name: 'تنبيه التقاعد قبل 6 أشهر', type: 'retirement', days: 180, description: 'تنبيه قبل موعد التقاعد بستة أشهر' }
        ];
        
        // حفظ البيانات في التخزين المحلي
        localStorage.setItem('alertPeriods', JSON.stringify(alertPeriods));
    } else {
        // تحويل البيانات من JSON إلى كائن JavaScript
        alertPeriods = JSON.parse(alertPeriods);
    }
    
    // عرض البيانات في الجدول
    displayAlertPeriods(alertPeriods);
}

// عرض فترات التنبيه في الجدول
function displayAlertPeriods(alertPeriods) {
    const tableBody = document.querySelector('#alertPeriodsTable tbody');
    if (!tableBody) return;
    
    // مسح محتوى الجدول
    tableBody.innerHTML = '';
    
    // إذا لم تكن هناك فترات تنبيه، عرض رسالة
    if (alertPeriods.length === 0) {
        const emptyRow = document.createElement('tr');
        emptyRow.innerHTML = `<td colspan="5" class="text-center">لا توجد فترات تنبيه. أضف فترة تنبيه جديدة.</td>`;
        tableBody.appendChild(emptyRow);
        return;
    }
    
    // إضافة الصفوف إلى الجدول
    alertPeriods.forEach((alert, index) => {
        const row = document.createElement('tr');
        
        // تحويل نوع التنبيه إلى نص عربي
        let typeText = '';
        switch (alert.type) {
            case 'allowance':
                typeText = 'علاوة';
                break;
            case 'promotion':
                typeText = 'ترفيع';
                break;
            case 'retirement':
                typeText = 'تقاعد';
                break;
            default:
                typeText = alert.type;
        }
        
        row.innerHTML = `
            <td>${index + 1}</td>
            <td>${alert.name}</td>
            <td>${typeText}</td>
            <td>${alert.days} يوم</td>
            <td>
                <div class="action-buttons">
                    <button class="action-btn edit-btn" data-id="${alert.id}" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn delete-btn" data-id="${alert.id}" data-name="${alert.name}" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;
        tableBody.appendChild(row);
    });
    
    // إضافة مستمعي الأحداث لأزرار التعديل والحذف
    addActionButtonsEventListeners();
}

// إضافة مستمعي الأحداث لأزرار التعديل والحذف
function addActionButtonsEventListeners() {
    // أزرار التعديل
    const editButtons = document.querySelectorAll('.edit-btn');
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const alertId = this.getAttribute('data-id');
            openEditModal(alertId);
        });
    });
    
    // أزرار الحذف
    const deleteButtons = document.querySelectorAll('.delete-btn');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const alertId = this.getAttribute('data-id');
            const alertName = this.getAttribute('data-name');
            openDeleteModal(alertId, alertName);
        });
    });
}

// تهيئة نموذج إضافة فترة تنبيه
function initAddAlertPeriodForm() {
    const addForm = document.getElementById('addAlertPeriodForm');
    if (!addForm) return;
    
    addForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // الحصول على قيم الحقول
        const name = document.getElementById('alertName').value.trim();
        const type = document.getElementById('alertType').value;
        const days = document.getElementById('alertDays').value;
        const description = document.getElementById('alertDescription').value.trim();
        
        // التحقق من صحة البيانات
        if (!name) {
            alert('يرجى إدخال اسم التنبيه');
            return;
        }
        
        if (!type) {
            alert('يرجى اختيار نوع التنبيه');
            return;
        }
        
        if (!days || days < 1) {
            alert('يرجى إدخال عدد الأيام بشكل صحيح');
            return;
        }
        
        // إضافة فترة التنبيه الجديدة
        addAlertPeriod(name, type, days, description);
        
        // إعادة تعيين النموذج
        this.reset();
    });
}

// إضافة فترة تنبيه جديدة
function addAlertPeriod(name, type, days, description) {
    // الحصول على فترات التنبيه الحالية
    let alertPeriods = JSON.parse(localStorage.getItem('alertPeriods') || '[]');
    
    // إنشاء معرف فريد جديد
    const newId = alertPeriods.length > 0 ? Math.max(...alertPeriods.map(alert => alert.id)) + 1 : 1;
    
    // إنشاء فترة التنبيه الجديدة
    const newAlert = {
        id: newId,
        name: name,
        type: type,
        days: parseInt(days),
        description: description
    };
    
    // إضافة فترة التنبيه الجديدة إلى المصفوفة
    alertPeriods.push(newAlert);
    
    // حفظ البيانات المحدثة في التخزين المحلي
    localStorage.setItem('alertPeriods', JSON.stringify(alertPeriods));
    
    // تحديث عرض الجدول
    displayAlertPeriods(alertPeriods);
    
    // عرض رسالة نجاح
    showNotification('تم إضافة فترة التنبيه بنجاح', 'success');
}

// تهيئة البحث والتصفية
function initSearchAndFilter() {
    const searchInput = document.getElementById('searchAlert');
    const filterSelect = document.getElementById('filterAlertType');
    
    if (searchInput) {
        searchInput.addEventListener('input', applySearchAndFilter);
    }
    
    if (filterSelect) {
        filterSelect.addEventListener('change', applySearchAndFilter);
    }
}

// تطبيق البحث والتصفية
function applySearchAndFilter() {
    const searchInput = document.getElementById('searchAlert');
    const filterSelect = document.getElementById('filterAlertType');
    
    if (!searchInput || !filterSelect) return;
    
    const searchTerm = searchInput.value.trim().toLowerCase();
    const filterType = filterSelect.value;
    
    // الحصول على فترات التنبيه
    const alertPeriods = JSON.parse(localStorage.getItem('alertPeriods') || '[]');
    
    // تصفية فترات التنبيه بناءً على مصطلح البحث والنوع
    const filteredAlerts = alertPeriods.filter(alert => {
        const matchesSearch = alert.name.toLowerCase().includes(searchTerm) ||
                             alert.description.toLowerCase().includes(searchTerm);
        const matchesType = !filterType || alert.type === filterType;
        return matchesSearch && matchesType;
    });
    
    // عرض النتائج المصفاة
    displayAlertPeriods(filteredAlerts);
}

// تهيئة النوافذ المنبثقة
function initModals() {
    // الحصول على عناصر النوافذ المنبثقة
    const editModal = document.getElementById('editModal');
    const deleteModal = document.getElementById('deleteModal');
    const closeButtons = document.querySelectorAll('.close-modal');
    
    // أزرار الإغلاق
    closeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const modal = this.closest('.modal');
            closeModal(modal);
        });
    });
    
    // إغلاق النافذة عند النقر خارجها
    window.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal')) {
            closeModal(e.target);
        }
    });
    
    // زر إلغاء التعديل
    const cancelEditBtn = document.getElementById('cancelEditBtn');
    if (cancelEditBtn) {
        cancelEditBtn.addEventListener('click', function() {
            closeModal(editModal);
        });
    }
    
    // زر حفظ التعديل
    const saveEditBtn = document.getElementById('saveEditBtn');
    if (saveEditBtn) {
        saveEditBtn.addEventListener('click', function() {
            saveEditedAlertPeriod();
        });
    }
    
    // زر إلغاء الحذف
    const cancelDeleteBtn = document.getElementById('cancelDeleteBtn');
    if (cancelDeleteBtn) {
        cancelDeleteBtn.addEventListener('click', function() {
            closeModal(deleteModal);
        });
    }
    
    // زر تأكيد الحذف
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    if (confirmDeleteBtn) {
        confirmDeleteBtn.addEventListener('click', function() {
            const alertId = this.getAttribute('data-id');
            deleteAlertPeriod(alertId);
            closeModal(deleteModal);
        });
    }
}

// فتح نافذة التعديل
function openEditModal(alertId) {
    // الحصول على فترة التنبيه المراد تعديلها
    const alertPeriods = JSON.parse(localStorage.getItem('alertPeriods') || '[]');
    const alert = alertPeriods.find(alert => alert.id == alertId);
    
    if (!alert) return;
    
    // ملء حقول النموذج بالبيانات الحالية
    document.getElementById('editAlertId').value = alert.id;
    document.getElementById('editAlertName').value = alert.name;
    document.getElementById('editAlertType').value = alert.type;
    document.getElementById('editAlertDays').value = alert.days;
    document.getElementById('editAlertDescription').value = alert.description || '';
    
    // فتح النافذة
    const editModal = document.getElementById('editModal');
    openModal(editModal);
}

// فتح نافذة تأكيد الحذف
function openDeleteModal(alertId, alertName) {
    // عرض اسم فترة التنبيه في رسالة التأكيد
    document.getElementById('deleteAlertName').textContent = alertName;
    
    // تعيين معرف فترة التنبيه لزر التأكيد
    document.getElementById('confirmDeleteBtn').setAttribute('data-id', alertId);
    
    // فتح النافذة
    const deleteModal = document.getElementById('deleteModal');
    openModal(deleteModal);
}

// حفظ فترة التنبيه المعدلة
function saveEditedAlertPeriod() {
    // الحصول على قيم الحقول
    const id = document.getElementById('editAlertId').value;
    const name = document.getElementById('editAlertName').value.trim();
    const type = document.getElementById('editAlertType').value;
    const days = document.getElementById('editAlertDays').value;
    const description = document.getElementById('editAlertDescription').value.trim();
    
    // التحقق من صحة البيانات
    if (!name) {
        alert('يرجى إدخال اسم التنبيه');
        return;
    }
    
    if (!type) {
        alert('يرجى اختيار نوع التنبيه');
        return;
    }
    
    if (!days || days < 1) {
        alert('يرجى إدخال عدد الأيام بشكل صحيح');
        return;
    }
    
    // الحصول على فترات التنبيه الحالية
    let alertPeriods = JSON.parse(localStorage.getItem('alertPeriods') || '[]');
    
    // البحث عن فترة التنبيه وتحديثها
    const index = alertPeriods.findIndex(alert => alert.id == id);
    if (index !== -1) {
        alertPeriods[index] = {
            id: parseInt(id),
            name: name,
            type: type,
            days: parseInt(days),
            description: description
        };
        
        // حفظ البيانات المحدثة في التخزين المحلي
        localStorage.setItem('alertPeriods', JSON.stringify(alertPeriods));
        
        // تحديث عرض الجدول
        displayAlertPeriods(alertPeriods);
        
        // إغلاق النافذة
        closeModal(document.getElementById('editModal'));
        
        // عرض رسالة نجاح
        showNotification('تم تحديث فترة التنبيه بنجاح', 'success');
    }
}

// حذف فترة التنبيه
function deleteAlertPeriod(alertId) {
    // الحصول على فترات التنبيه الحالية
    let alertPeriods = JSON.parse(localStorage.getItem('alertPeriods') || '[]');
    
    // حذف فترة التنبيه
    alertPeriods = alertPeriods.filter(alert => alert.id != alertId);
    
    // حفظ البيانات المحدثة في التخزين المحلي
    localStorage.setItem('alertPeriods', JSON.stringify(alertPeriods));
    
    // تحديث عرض الجدول
    displayAlertPeriods(alertPeriods);
    
    // عرض رسالة نجاح
    showNotification('تم حذف فترة التنبيه بنجاح', 'success');
}

// فتح نافذة منبثقة
function openModal(modal) {
    if (modal) {
        modal.style.display = 'block';
    }
}

// إغلاق نافذة منبثقة
function closeModal(modal) {
    if (modal) {
        modal.style.display = 'none';
    }
}

// عرض إشعار
function showNotification(message, type = 'info') {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-info-circle'}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close"><i class="fas fa-times"></i></button>
    `;
    
    // إضافة الإشعار إلى الصفحة
    document.body.appendChild(notification);
    
    // إضافة مستمع حدث لزر الإغلاق
    const closeButton = notification.querySelector('.notification-close');
    closeButton.addEventListener('click', function() {
        notification.remove();
    });
    
    // إزالة الإشعار تلقائيًا بعد 5 ثوانٍ
    setTimeout(() => {
        notification.classList.add('fade-out');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 5000);
}
