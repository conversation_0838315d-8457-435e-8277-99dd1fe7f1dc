/**
 * ملف لإضافة بيانات موظفين افتراضية للاختبار
 */

// إضافة بيانات موظفين افتراضية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // التحقق مما إذا كانت هناك بيانات موظفين موجودة بالفعل
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');
    
    // إذا لم تكن هناك بيانات موظفين، أضف بيانات افتراضية
    if (employees.length === 0) {
        // إنشاء مصفوفة من الموظفين الافتراضيين
        const defaultEmployees = [
            {
                id: 1001,
                name: "أحمد محمد علي",
                jobDescription: "teaching",
                jobTitle: "مدرس",
                education: "دكتوراه",
                workLocation: "كلية الهندسة",
                birthDate: "1980-05-15",
                hireDate: "2010-09-01",
                currentGrade: 3,
                currentStage: 5,
                currentSalary: 1250000,
                currentDueDate: "2023-09-01",
                newGrade: 3,
                newStage: 6,
                newSalary: 1300000,
                allowanceSeniority: 0,
                newDueDate: "2024-09-01",
                lastPromotionDate: "2020-09-01",
                promotionSeniority: 0,
                nextPromotionDate: "2024-09-01",
                retirementDate: "2045-05-15"
            },
            {
                id: 1002,
                name: "فاطمة حسين محمود",
                jobDescription: "teaching",
                jobTitle: "أستاذ مساعد",
                education: "دكتوراه",
                workLocation: "كلية العلوم",
                birthDate: "1975-08-20",
                hireDate: "2005-10-15",
                currentGrade: 2,
                currentStage: 8,
                currentSalary: 1450000,
                currentDueDate: "2023-10-15",
                newGrade: 2,
                newStage: 9,
                newSalary: 1500000,
                allowanceSeniority: 0,
                newDueDate: "2024-10-15",
                lastPromotionDate: "2018-10-15",
                promotionSeniority: 0,
                nextPromotionDate: "2022-10-15",
                retirementDate: "2040-08-20"
            },
            {
                id: 1003,
                name: "محمد جاسم عبد الله",
                jobDescription: "technical",
                jobTitle: "مهندس",
                education: "بكالوريوس",
                workLocation: "قسم الصيانة",
                birthDate: "1985-03-10",
                hireDate: "2015-02-01",
                currentGrade: 4,
                currentStage: 3,
                currentSalary: 950000,
                currentDueDate: "2023-02-01",
                newGrade: 4,
                newStage: 4,
                newSalary: 1000000,
                allowanceSeniority: 0,
                newDueDate: "2024-02-01",
                lastPromotionDate: "2019-02-01",
                promotionSeniority: 0,
                nextPromotionDate: "2023-02-01",
                retirementDate: "2050-03-10"
            },
            {
                id: 1004,
                name: "زينب علي حسن",
                jobDescription: "administrative",
                jobTitle: "مدير إداري",
                education: "ماجستير",
                workLocation: "قسم الموارد البشرية",
                birthDate: "1982-11-25",
                hireDate: "2012-07-01",
                currentGrade: 3,
                currentStage: 4,
                currentSalary: 1150000,
                currentDueDate: "2023-07-01",
                newGrade: 3,
                newStage: 5,
                newSalary: 1200000,
                allowanceSeniority: 0,
                newDueDate: "2024-07-01",
                lastPromotionDate: "2020-07-01",
                promotionSeniority: 0,
                nextPromotionDate: "2024-07-01",
                retirementDate: "2047-11-25"
            },
            {
                id: 1005,
                name: "علي حسين كاظم",
                jobDescription: "teaching",
                jobTitle: "مدرس مساعد",
                education: "ماجستير",
                workLocation: "كلية الطب",
                birthDate: "1988-06-05",
                hireDate: "2018-09-01",
                currentGrade: 4,
                currentStage: 2,
                currentSalary: 900000,
                currentDueDate: "2023-09-01",
                newGrade: 4,
                newStage: 3,
                newSalary: 950000,
                allowanceSeniority: 0,
                newDueDate: "2024-09-01",
                lastPromotionDate: "2021-09-01",
                promotionSeniority: 0,
                nextPromotionDate: "2025-09-01",
                retirementDate: "2053-06-05"
            }
        ];
        
        // حفظ البيانات في التخزين المحلي
        localStorage.setItem('employees', JSON.stringify(defaultEmployees));
        
        console.log('تم إضافة بيانات موظفين افتراضية للاختبار');
    }
});
