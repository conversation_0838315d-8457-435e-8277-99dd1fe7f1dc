/**
 * ملف لإدارة لوحة المعلومات الرئيسية
 */

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('تحميل لوحة المعلومات...');

    // تحديث الإحصائيات فوراً
    updateStatistics();

    // تحديث التنبيهات
    updateAlerts();

    // تحديث قائمة الموظفين الجدد
    updateNewEmployees();

    // إضافة مستمعات الأحداث للأزرار
    addButtonEventListeners();

    // تحديث الإحصائيات كل 30 ثانية
    setInterval(updateStatistics, 30000);

    // إضافة مراقب للتغييرات في التخزين المحلي
    window.addEventListener('storage', function(e) {
        console.log('تم اكتشاف تغيير في التخزين المحلي:', e.key);
        
        // تحديث الإحصائيات عند تغيير بيانات الموظفين أو العناوين الوظيفية
        if (e.key === 'employees' || e.key === 'jobTitles') {
            console.log('تم تغيير البيانات، جاري تحديث الإحصائيات...');
            updateStatistics();
            updateNewEmployees();
        }
    });

    // تحديث الإحصائيات عند العودة إلى الصفحة
    window.addEventListener('focus', function() {
        console.log('تم التركيز على النافذة، تحديث الإحصائيات...');
        updateStatistics();
        updateNewEmployees();
    });
});

// تحديث الإحصائيات
function updateStatistics() {
    console.log('تحديث الإحصائيات...');

    try {
        // الحصول على بيانات الموظفين
        const employeesData = localStorage.getItem('employees');
        console.log('بيانات الموظفين من التخزين المحلي:', employeesData);
        
        const employees = JSON.parse(employeesData || '[]');
        console.log('عدد الموظفين المستردة:', employees.length);

        // حساب إجمالي الموظفين
        const totalEmployees = employees.length;
        const totalElement = document.getElementById('employeeCount');
        if (totalElement) {
            totalElement.textContent = totalEmployees;
            console.log('تم تحديث إجمالي الموظفين:', totalEmployees);
        }

        // حساب عدد الموظفين حسب الفئة
        const teachingCount = employees.filter(emp => emp.jobDescription === 'teaching').length;
        const technicalCount = employees.filter(emp => emp.jobDescription === 'technical').length;
        const administrativeCount = employees.filter(emp => emp.jobDescription === 'administrative').length;

        console.log('إحصائيات الموظفين:', {
            تدريسي: teachingCount,
            فني: technicalCount,
            اداري: administrativeCount
        });

        // تحديث العناصر في الصفحة
        const elements = {
            'teachingCount': teachingCount,
            'technicalCount': technicalCount,
            'administrativeCount': administrativeCount
        };

        for (const [id, value] of Object.entries(elements)) {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
                console.log(`تم تحديث ${id}:`, value);
            } else {
                console.warn(`لم يتم العثور على العنصر: ${id}`);
            }
        }

        // تحديث عدد العناوين الوظيفية
        const jobTitles = JSON.parse(localStorage.getItem('jobTitles') || '[]');
        const jobTitleElement = document.getElementById('jobTitleCount');
        if (jobTitleElement) {
            jobTitleElement.textContent = jobTitles.length;
            console.log('تم تحديث عدد العناوين الوظيفية:', jobTitles.length);
        }

        // تحديث عدد مواقع العمل
        const workLocations = JSON.parse(localStorage.getItem('workLocations') || '[]');
        const locationElement = document.getElementById('locationCount');
        if (locationElement) {
            locationElement.textContent = workLocations.length;
            console.log('تم تحديث عدد مواقع العمل:', workLocations.length);
        }

        // تحديث عدد كتب الشكر
        const thanks = JSON.parse(localStorage.getItem('thanks') || '[]');
        const appreciationElement = document.getElementById('appreciationCount');
        if (appreciationElement) {
            appreciationElement.textContent = thanks.length;
            console.log('تم تحديث عدد كتب الشكر:', thanks.length);
        }

        // تحديث عدد العقوبات
        const penalties = JSON.parse(localStorage.getItem('penalties') || '[]');
        const penaltyElement = document.getElementById('penaltyCount');
        if (penaltyElement) {
            penaltyElement.textContent = penalties.length;
            console.log('تم تحديث عدد العقوبات:', penalties.length);
        }

        // تحديث عدد الإجازات الحالية
        const leaves = JSON.parse(localStorage.getItem('employeeLeaves') || '[]');
        const currentDate = new Date();
        const activeLeaves = leaves.filter(leave => {
            const startDate = new Date(leave.startDate);
            const endDate = new Date(leave.endDate);
            return startDate <= currentDate && endDate >= currentDate;
        });
        const leaveElement = document.getElementById('leaveCount');
        if (leaveElement) {
            leaveElement.textContent = activeLeaves.length;
            console.log('تم تحديث عدد الإجازات الحالية:', activeLeaves.length);
        }

        console.log('تم تحديث الإحصائيات بنجاح');
    } catch (error) {
        console.error('خطأ في تحديث الإحصائيات:', error);
    }
}

// تحديث التنبيهات
function updateAlerts() {
    console.log('تحديث التنبيهات...');

    // الحصول على بيانات الموظفين
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');

    // الحصول على التاريخ الحالي
    const currentDate = new Date();

    // تحديد فترة التنبيه (30 يوم)
    const alertPeriod = 30;

    // إنشاء قائمة التنبيهات
    const alerts = [];

    // تحديث عدد التنبيهات (تم تعطيل هذه الميزة)
    // const alertsCountElement = document.getElementById('alertsCount');

    // فحص العلاوات المستحقة
    employees.forEach(employee => {
        // استخدام nextAllowanceDate بدلاً من newDueDate
        if (employee.nextAllowanceDate) {
            const dueDate = new Date(employee.nextAllowanceDate);
            const diffTime = dueDate - currentDate;
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

            if (diffDays >= 0 && diffDays <= alertPeriod) {
                alerts.push({
                    type: 'allowance',
                    employee: employee.name,
                    date: employee.nextAllowanceDate,
                    daysLeft: diffDays,
                    message: `الموظف ${employee.name} مستحق للعلاوة السنوية`
                });
            }
        }
    });

    // فحص الترفيعات المستحقة
    employees.forEach(employee => {
        if (employee.nextPromotionDate) {
            const promotionDate = new Date(employee.nextPromotionDate);
            const diffTime = promotionDate - currentDate;
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

            if (diffDays >= 0 && diffDays <= alertPeriod) {
                alerts.push({
                    type: 'promotion',
                    employee: employee.name,
                    date: employee.nextPromotionDate,
                    daysLeft: diffDays,
                    message: `الموظف ${employee.name} مستحق للترفيع الوظيفي`
                });
            }
        }
    });

    // فحص الإحالة للتقاعد
    employees.forEach(employee => {
        if (employee.retirementDate) {
            const retirementDate = new Date(employee.retirementDate);
            const diffTime = retirementDate - currentDate;
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

            if (diffDays >= 0 && diffDays <= alertPeriod) {
                alerts.push({
                    type: 'retirement',
                    employee: employee.name,
                    date: employee.retirementDate,
                    daysLeft: diffDays,
                    message: `الموظف ${employee.name} سيتم إحالته للتقاعد قريباً`
                });
            }
        }
    });

    // ترتيب التنبيهات حسب التاريخ
    alerts.sort((a, b) => new Date(a.date) - new Date(b.date));

    // تحديث عدد التنبيهات في الشريط العلوي (تم تعطيل هذه الميزة)
    /*
    if (alertsCountElement) {
        alertsCountElement.textContent = alerts.length;

        // إظهار أو إخفاء شارة التنبيهات
        if (alerts.length > 0) {
            alertsCountElement.style.display = 'flex';
        } else {
            alertsCountElement.style.display = 'none';
        }
    }
    */

    // عرض التنبيهات (أقصى 3 تنبيهات)
    const alertsContainer = document.querySelector('.dashboard-section:first-child');
    const alertsHeader = alertsContainer.querySelector('.section-header');

    // حذف التنبيهات القديمة
    const oldAlerts = alertsContainer.querySelectorAll('.alert');
    oldAlerts.forEach(alert => alert.remove());

    // إضافة التنبيهات الجديدة
    const alertsToShow = alerts.slice(0, 3);

    if (alertsToShow.length > 0) {
        alertsToShow.forEach(alert => {
            let alertClass = '';
            let alertIcon = '';
            let alertBadge = '';

            switch (alert.type) {
                case 'allowance':
                    alertClass = 'blue-alert';
                    alertIcon = 'fas fa-info-circle';
                    alertBadge = 'علاوة';
                    break;
                case 'promotion':
                    alertClass = 'green-alert';
                    alertIcon = 'fas fa-arrow-up';
                    alertBadge = 'ترفيع';
                    break;
                case 'retirement':
                    alertClass = 'amber-alert';
                    alertIcon = 'fas fa-user-clock';
                    alertBadge = 'تقاعد';
                    break;
            }

            const alertElement = document.createElement('div');
            alertElement.className = `alert ${alertClass}`;
            alertElement.innerHTML = `
                <div class="alert-header">
                    <h3><i class="${alertIcon}"></i> ${alertBadge} مستحق</h3>
                    <span class="alert-badge">${alertBadge}</span>
                </div>
                <p>${alert.message}</p>
                <span class="date"><i class="fas fa-clock"></i> ${alert.date} (متبقي ${alert.daysLeft} يوم)</span>
            `;

            alertsContainer.insertBefore(alertElement, null);
        });
    } else {
        // إذا لم تكن هناك تنبيهات، عرض رسالة
        const noAlertsElement = document.createElement('div');
        noAlertsElement.className = 'alert';
        noAlertsElement.innerHTML = `
            <div class="alert-header">
                <h3><i class="fas fa-check-circle"></i> لا توجد تنبيهات</h3>
            </div>
            <p>لا توجد تنبيهات حالية للعلاوات أو الترفيعات أو التقاعد</p>
        `;

        alertsContainer.insertBefore(noAlertsElement, null);
    }

    console.log('تم تحديث التنبيهات بنجاح');
}

// تحديث قائمة الموظفين الجدد
function updateNewEmployees() {
    console.log('تحديث قائمة الموظفين الجدد...');

    // الحصول على بيانات الموظفين
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');

    // ترتيب الموظفين حسب تاريخ التعيين (الأحدث أولاً)
    employees.sort((a, b) => new Date(b.hireDate) - new Date(a.hireDate));

    // الحصول على أحدث 3 موظفين
    const newEmployees = employees.slice(0, 3);

    // عرض الموظفين الجدد
    const employeesContainer = document.querySelector('.dashboard-section:nth-child(2)');
    const employeesHeader = employeesContainer.querySelector('.section-header');

    // حذف بطاقات الموظفين القديمة
    const oldCards = employeesContainer.querySelectorAll('.employee-card');
    oldCards.forEach(card => card.remove());

    // إضافة بطاقات الموظفين الجدد
    if (newEmployees.length > 0) {
        newEmployees.forEach(employee => {
            // الحصول على العنوان الوظيفي
            let jobTitle = '';
            if (employee.jobTitle) {
                const jobTitles = JSON.parse(localStorage.getItem('jobTitles') || '[]');
                const title = jobTitles.find(t => t.id == employee.jobTitle);
                if (title) {
                    jobTitle = title.name;
                }
            }

            // الحصول على موقع العمل
            let workLocation = '';
            if (employee.workLocation) {
                const workLocations = JSON.parse(localStorage.getItem('workLocations') || '[]');
                const location = workLocations.find(l => l.id == employee.workLocation);
                if (location) {
                    workLocation = location.name;
                }
            }

            // إنشاء الأحرف الأولى من اسم الموظف
            const nameParts = employee.name.split(' ');
            const initials = nameParts.length > 1
                ? `${nameParts[0].charAt(0)} ${nameParts[1].charAt(0)}`
                : nameParts[0].charAt(0);

            const employeeCard = document.createElement('div');
            employeeCard.className = 'employee-card';
            employeeCard.innerHTML = `
                <div class="employee-info">
                    <div class="employee-avatar">${initials}</div>
                    <div class="employee-name-title">
                        <h3>${employee.name}</h3>
                        <p>${jobTitle || 'غير محدد'}</p>
                    </div>
                </div>
                <div class="employee-details">
                    <p>${workLocation || 'غير محدد'}</p>
                    <p class="date">تاريخ التعيين: ${employee.hireDate}</p>
                </div>
            `;

            employeesContainer.appendChild(employeeCard);
        });
    } else {
        // إذا لم يكن هناك موظفين، عرض رسالة
        const noEmployeesElement = document.createElement('div');
        noEmployeesElement.className = 'employee-card';
        noEmployeesElement.innerHTML = `
            <div class="employee-info">
                <div class="employee-avatar"><i class="fas fa-user-plus"></i></div>
                <div class="employee-name-title">
                    <h3>لا يوجد موظفين</h3>
                    <p>قم بإضافة موظفين جدد</p>
                </div>
            </div>
        `;

        employeesContainer.appendChild(noEmployeesElement);
    }

    console.log('تم تحديث قائمة الموظفين الجدد بنجاح');
}

// إضافة مستمعات الأحداث للأزرار
function addButtonEventListeners() {
    console.log('إضافة مستمعات الأحداث للأزرار...');

    // أزرار الاستكشاف
    const exploreButtons = document.querySelectorAll('.feature-card .btn');
    exploreButtons.forEach((button, index) => {
        button.addEventListener('click', function() {
            switch (index) {
                case 0: // إدارة الموظفين
                    window.location.href = 'employee-form.html';
                    break;
                case 1: // العلاوات والترفيعات
                    window.location.href = 'allowance-reports.html';
                    break;
                case 2: // التقارير والإحصائيات
                    window.location.href = 'promotion-reports.html';
                    break;
            }
        });
    });

    // روابط عرض الكل
    const viewAllLinks = document.querySelectorAll('.view-all');
    viewAllLinks.forEach((link, index) => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            switch (index) {
                case 0: // التنبيهات
                    window.location.href = 'allowance-alerts.html';
                    break;
                case 1: // الموظفين الجدد
                    window.location.href = 'employees-list.html';
                    break;
            }
        });
    });

    console.log('تم إضافة مستمعات الأحداث للأزرار بنجاح');
}

// تحديث الرسوم البيانية
function updateCharts(teachingCount, technicalCount, administrativeCount) {
    // يمكن إضافة رسوم بيانية هنا في المستقبل
    console.log('تحديث الرسوم البيانية...');
    console.log('إحصائيات الموظفين:', { teachingCount, technicalCount, administrativeCount });
}
