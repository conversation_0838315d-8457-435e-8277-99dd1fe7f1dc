// انتظر حتى يتم تحميل المستند بالكامل
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة النموذج
    initEmployeeForm();
});

// إضافة مستمع حدث للتركيز على النافذة
window.addEventListener('focus', function() {
    console.log('تم التركيز على النافذة، تحديث البيانات...');

    // تحديث البيانات عند العودة إلى الصفحة
    refreshData();
});

// إضافة مستمع حدث لاستقبال الرسائل من النوافذ الأخرى
window.addEventListener('message', function(event) {
    console.log('تم استلام رسالة:', event.data);

    // التحقق من نوع الرسالة
    if (event.data === 'jobTitlesUpdated') {
        console.log('تم تحديث العناوين الوظيفية في نافذة أخرى، جاري التحديث...');

        // تحديث البيانات
        refreshData();
    }
});

// تحديث البيانات
function refreshData() {
    console.log("تحديث البيانات...");

    // الحصول على القيم الحالية
    const jobDescriptionValue = document.getElementById('jobDescription').value;
    const jobTitleValue = document.getElementById('jobTitle').value;
    const workLocationValue = document.getElementById('workLocation').value;
    const educationValue = document.getElementById('education').value;

    console.log("القيم الحالية:", {
        jobDescription: jobDescriptionValue,
        jobTitle: jobTitleValue,
        workLocation: workLocationValue,
        education: educationValue
    });

    // تحديث العناوين الوظيفية باستخدام وظيفة loadJobTitles
    if (typeof loadJobTitles === 'function') {
        console.log('استخدام وظيفة loadJobTitles لتحديث العناوين الوظيفية...');
        const jobTitles = loadJobTitles();

        // تحديث البيانات في متغير عام
        if (window.appData) {
            window.appData.jobTitles = jobTitles;
            window.appData.lastUpdate = new Date().toISOString();
        }
    } else {
        console.error('وظيفة loadJobTitles غير متوفرة');
        return;
    }

    // تدمير مثيلات Select2
    if (typeof destroySelect2 === 'function') {
        destroySelect2();
    }

    // تحديث قائمة العناوين الوظيفية
    if (jobDescriptionValue) {
        // تحديث العناوين الوظيفية
        updateJobTitles();

        // إعادة تهيئة Select2
        setTimeout(function() {
            if (typeof initializeSelect2 === 'function') {
                console.log('إعادة تهيئة Select2...');
                initializeSelect2();

                // إعادة تحديد القيم
                if (typeof updateSelect2Value === 'function') {
                    updateSelect2Value('#jobDescription', jobDescriptionValue);
                    updateSelect2Value('#workLocation', workLocationValue);
                    updateSelect2Value('#education', educationValue);

                    // إعادة تحديد العنوان الوظيفي
                    setTimeout(function() {
                        if (jobTitleValue) {
                            // التحقق من وجود العنوان الوظيفي في القائمة الجديدة
                            const jobTitleSelect = document.getElementById('jobTitle');
                            let optionExists = false;

                            for (let i = 0; i < jobTitleSelect.options.length; i++) {
                                if (jobTitleSelect.options[i].value == jobTitleValue) {
                                    optionExists = true;
                                    break;
                                }
                            }

                            if (optionExists) {
                                updateSelect2Value('#jobTitle', jobTitleValue);

                                // تحديث العنوان الوظيفي الجديد
                                setTimeout(function() {
                                    updateNewJobTitle();
                                }, 200);
                            } else {
                                console.warn('العنوان الوظيفي لم يعد موجوداً في القائمة');
                                // تحديث العنوان الوظيفي الجديد
                                updateNewJobTitle();
                            }
                        }
                    }, 300);
                }

                // إضافة مستمعي الأحداث
                if (typeof addSelect2EventListeners === 'function') {
                    addSelect2EventListeners();
                }

                // عرض إشعار بالتحديث
                showNotification('تم تحديث العناوين الوظيفية', 'success');
            }
        }, 300);
    }
}

// عرض إشعار - تم نقل هذه الوظيفة إلى ملف simple-messages.js

// تهيئة نموذج الموظف
function initEmployeeForm() {
    console.log("بدء تهيئة نموذج الموظف");

    // إضافة مستمع حدث لزر تحديث العنوان الوظيفي الجديد
    const autoUpdateJobTitleBtn = document.getElementById('autoUpdateJobTitleBtn');
    if (autoUpdateJobTitleBtn) {
        autoUpdateJobTitleBtn.addEventListener('click', function() {
            if (typeof updateNewJobTitle === 'function') {
                const updated = updateNewJobTitle();
                if (updated) {
                    showNotification('تم تحديث العنوان الوظيفي الجديد باستخدام القيمة المحددة يدوياً', 'success');
                } else {
                    showNotification('تم تحديث العنوان الوظيفي الجديد باستخدام نفس العنوان الوظيفي الحالي', 'success');
                }
            } else {
                console.error('وظيفة updateNewJobTitle غير متوفرة');
                showNotification('حدث خطأ أثناء تحديث العنوان الوظيفي الجديد', 'error');
            }
        });
    }

    // التحقق مما إذا كان المستخدم قادماً من صفحة العناوين الوظيفية
    const needsUpdate = localStorage.getItem('needsUpdate');
    if (needsUpdate === 'true') {
        console.log('تم تحديث العناوين الوظيفية، جاري تحديث القائمة...');

        // الحصول على الوصف الوظيفي الحالي
        const jobDescriptionSelect = document.getElementById('jobDescription');
        if (jobDescriptionSelect && jobDescriptionSelect.value) {
            // مسح ذاكرة التخزين المؤقت للعناوين الوظيفية
            localStorage.removeItem('jobTitlesCache');

            // تحديث قائمة العناوين الوظيفية بدون إظهار إشعار
            if (typeof updateJobTitlesList === 'function') {
                updateJobTitlesList(jobDescriptionSelect.value);
                // تم إزالة الإشعار هنا
            }
        }

        // مسح مؤشرات التحديث
        localStorage.removeItem('needsUpdate');
        localStorage.removeItem('jobTitlesUpdated');
    }

    // الحصول على عناصر النموذج
    const employeeForm = document.getElementById('employeeForm');
    const birthDateInput = document.getElementById('birthDate');
    const hireDateInput = document.getElementById('hireDate');
    const lastPromotionDateInput = document.getElementById('lastPromotionDate');
    const currentDegreeInput = document.getElementById('currentDegree');
    const currentStageInput = document.getElementById('currentStage');
    const jobDescriptionSelect = document.getElementById('jobDescription');
    const manageJobTitlesBtn = document.getElementById('manageJobTitlesBtn');

    // إضافة مستمع حدث لزر إدارة العناوين الوظيفية
    if (manageJobTitlesBtn) {
        manageJobTitlesBtn.addEventListener('click', function() {
            console.log('تم النقر على زر إدارة العناوين الوظيفية');

            // حفظ الوصف الوظيفي الحالي في التخزين المحلي
            if (jobDescriptionSelect && jobDescriptionSelect.value) {
                localStorage.setItem('currentJobDescription', jobDescriptionSelect.value);
                console.log('تم حفظ الوصف الوظيفي الحالي:', jobDescriptionSelect.value);
            }

            // إضافة مستمع للرسائل من النوافذ الأخرى قبل فتح النافذة الجديدة
            const messageHandler = function(event) {
                console.log('تم استلام رسالة في نموذج الموظف:', event.data);

                // التحقق من نوع الرسالة
                if (event.data && event.data.type === 'jobTitlesChanged') {
                    console.log('تم تغيير العناوين الوظيفية في نافذة أخرى، جاري التحديث...');
                    console.log('تفاصيل التغيير:', event.data);

                    // مسح ذاكرة التخزين المؤقت للعناوين الوظيفية
                    localStorage.removeItem('jobTitlesCache');

                    // تحديث قائمة العناوين الوظيفية إذا كان هناك وصف وظيفي محدد
                    if (jobDescriptionSelect && jobDescriptionSelect.value) {
                        // التحقق مما إذا كان التغيير يؤثر على الفئة المحددة حالياً
                        let shouldUpdate = true;

                        if (event.data.action && event.data.data) {
                            if (event.data.action === 'edit') {
                                // في حالة التعديل، نتحقق مما إذا كان العنوان الوظيفي القديم أو الجديد ينتمي إلى الفئة المحددة
                                shouldUpdate = (event.data.oldData && event.data.oldData.category === jobDescriptionSelect.value) ||
                                              (event.data.newData && event.data.newData.category === jobDescriptionSelect.value);
                            } else if (event.data.action === 'delete' || event.data.action === 'add') {
                                // في حالة الحذف أو الإضافة، نتحقق مما إذا كان العنوان الوظيفي ينتمي إلى الفئة المحددة
                                shouldUpdate = event.data.data.category === jobDescriptionSelect.value;
                            }
                        }

                        if (shouldUpdate) {
                            console.log('التغيير يؤثر على الفئة المحددة حالياً، تحديث القائمة...');

                            // تحديث القائمة
                            if (typeof updateJobTitlesList === 'function') {
                                updateJobTitlesList(jobDescriptionSelect.value);
                                showNotification('تم تحديث العناوين الوظيفية', 'success');
                            } else if (typeof window.updateJobTitlesList === 'function') {
                                window.updateJobTitlesList(jobDescriptionSelect.value);
                                showNotification('تم تحديث العناوين الوظيفية', 'success');
                            } else {
                                console.error('وظيفة updateJobTitlesList غير متوفرة');
                            }
                        } else {
                            console.log('التغيير لا يؤثر على الفئة المحددة حالياً، تجاهل التحديث');
                        }
                    } else {
                        console.log('لا يوجد وصف وظيفي محدد حالياً');
                    }
                }
            };

            // إضافة مستمع للرسائل
            window.addEventListener('message', messageHandler);

            // بدلاً من فتح نافذة جديدة، سننتقل إلى صفحة إدارة العناوين الوظيفية في نفس النافذة
            // حفظ الصفحة الحالية في التخزين المحلي للعودة إليها لاحقاً
            localStorage.setItem('previousPage', window.location.href);
            window.location.href = 'job-titles.html';

            // إزالة مستمع الرسائل لأننا سننتقل إلى صفحة أخرى
            window.removeEventListener('message', messageHandler);
        });
    }

    // لا نحتاج إلى تهيئة مدير العناوين الوظيفية بعد الآن
    // تم استبداله بملف simple-job-titles.js

    // تحميل البيانات الأولية
    loadInitialData();

    // تهيئة Select2 بعد تحميل البيانات
    setTimeout(function() {
        if (typeof initializeSelect2 === 'function') {
            console.log('تهيئة Select2...');
            initializeSelect2();
            addSelect2EventListeners();

            // تحديث العناوين الوظيفية بناءً على الوصف الوظيفي المحدد
            if (jobDescriptionSelect && jobDescriptionSelect.value) {
                console.log('تحديث العناوين الوظيفية بناءً على الوصف الوظيفي المحدد...');
                updateJobTitles();
            }
        } else {
            console.error('وظيفة initializeSelect2 غير متوفرة');
        }
    }, 1000);

    // التحقق مما إذا كان هناك معرف موظف في عنوان URL
    const urlParams = new URLSearchParams(window.location.search);
    const employeeId = urlParams.get('id');

    console.log("معرف الموظف من URL:", employeeId);

    // إذا كان هناك معرف موظف، قم بتحميل بيانات الموظف
    if (employeeId) {
        console.log("جاري تحميل بيانات الموظف...");

        // الحصول على الموظفين من التخزين المحلي
        const employeesJson = localStorage.getItem('employees');
        console.log("بيانات الموظفين من التخزين المحلي:", employeesJson);

        if (employeesJson) {
            try {
                const employees = JSON.parse(employeesJson);
                console.log("تم تحويل بيانات الموظفين:", employees);

                // البحث عن الموظف بواسطة المعرف
                const employee = employees.find(emp => emp.id == employeeId);
                console.log("الموظف الذي تم العثور عليه:", employee);

                if (employee) {
                    // تغيير عنوان الصفحة
                    document.querySelector('.form-title').textContent = 'تعديل بيانات الموظف';

                    // ملء حقول النموذج ببيانات الموظف
                    fillEmployeeForm(employee);
                } else {
                    alert('لم يتم العثور على الموظف');
                    console.error("لم يتم العثور على الموظف بالمعرف:", employeeId);
                }
            } catch (error) {
                console.error("خطأ في تحليل بيانات الموظفين:", error);
                alert('حدث خطأ في تحميل بيانات الموظف');
            }
        } else {
            console.error("لا توجد بيانات موظفين في التخزين المحلي");
            alert('لا توجد بيانات موظفين');
        }
    } else {
        console.log("لا يوجد معرف موظف في URL - نموذج إضافة موظف جديد");
    }

    // إضافة مستمعي الأحداث
    if (birthDateInput) {
        birthDateInput.addEventListener('change', calculateRetirementDate);
    }

    if (hireDateInput && lastPromotionDateInput) {
        hireDateInput.addEventListener('change', function() {
            // إذا لم يتم تعيين تاريخ آخر ترفيع، استخدم تاريخ التعيين
            if (!lastPromotionDateInput.value) {
                lastPromotionDateInput.value = hireDateInput.value;
            }
            calculateNextPromotionDate();
        });

        lastPromotionDateInput.addEventListener('change', function() {
            calculateNextPromotionDate();
        });
    }

    // إضافة مستمع حدث لتاريخ الاستحقاق الحالي
    const currentDueDateInput = document.getElementById('currentDueDate');
    if (currentDueDateInput) {
        currentDueDateInput.addEventListener('change', function() {
            calculateNextAllowanceDate(); // حساب تاريخ الاستحقاق الجديد بناءً على العلاوة
        });
    }

    // إضافة مستمع حدث لقدم الترفيع
    const promotionSeniorityInput = document.getElementById('promotionSeniority');
    if (promotionSeniorityInput) {
        promotionSeniorityInput.addEventListener('change', function() {
            calculateNextPromotionDate(); // حساب تاريخ الترفيع القادم
        });
    }

    // إضافة مستمع حدث لقدم العلاوة
    const allowanceSeniorityInput = document.getElementById('allowanceSeniority');
    if (allowanceSeniorityInput) {
        allowanceSeniorityInput.addEventListener('change', function() {
            calculateNextAllowanceDate(); // حساب تاريخ الاستحقاق الجديد بناءً على العلاوة
        });
    }

    if (currentDegreeInput && currentStageInput) {
        currentDegreeInput.addEventListener('change', function() {
            calculateSalary();
            calculateNextPromotionDate();
            updateNewDegreeAndStage();
        });

        currentStageInput.addEventListener('change', function() {
            calculateSalary();
            updateNewDegreeAndStage();
        });
    }

    if (jobDescriptionSelect) {
        jobDescriptionSelect.addEventListener('change', function() {
            updateJobTitles();

            // إعادة تعيين العنوان الوظيفي الجديد عند تغيير الوصف الوظيفي
            const newJobTitleElement = document.getElementById('newJobTitle');
            if (newJobTitleElement) {
                newJobTitleElement.value = '';
            }
        });
    }

    // إضافة مستمع حدث لتقديم النموذج
    if (employeeForm) {
        employeeForm.addEventListener('submit', function(e) {
            e.preventDefault();
            saveEmployeeData();
        });
    }
}

// تحميل البيانات الأولية
function loadInitialData() {
    console.log('بدء تحميل البيانات الأولية...');

    // تحميل مواقع العمل من التخزين المحلي
    let workLocations = JSON.parse(localStorage.getItem('workLocations') || '[]');
    console.log('مواقع العمل المحملة:', workLocations);

    // إذا لم تكن هناك بيانات، استخدم بيانات افتراضية
    if (workLocations.length === 0) {
        console.log('لا توجد مواقع عمل، استخدام البيانات الافتراضية');
        workLocations = [
            { id: 1, name: 'كلية الهندسة' },
            { id: 2, name: 'كلية العلوم' },
            { id: 3, name: 'كلية الطب' },
            { id: 4, name: 'كلية الآداب' },
            { id: 5, name: 'رئاسة الجامعة' }
        ];
        // حفظ البيانات الافتراضية في التخزين المحلي
        localStorage.setItem('workLocations', JSON.stringify(workLocations));
    }

    // تحميل العناوين الوظيفية باستخدام وظيفة loadJobTitles
    let jobTitles = {};

    if (typeof loadJobTitles === 'function') {
        console.log('استخدام وظيفة loadJobTitles لتحميل العناوين الوظيفية...');
        jobTitles = loadJobTitles();
    } else {
        console.error('وظيفة loadJobTitles غير متوفرة، استخدام البيانات الافتراضية');

        // تنظيم العناوين الوظيفية حسب الفئة - قائمة فارغة
        jobTitles = {
            teaching: [],
            technical: [],
            administrative: []
        };
    }

    // تحميل التحصيل الدراسي من التخزين المحلي
    let educationLevels = JSON.parse(localStorage.getItem('educationLevels') || '[]');
    console.log('التحصيل الدراسي المحمل:', educationLevels);

    // إذا لم تكن هناك بيانات، استخدم بيانات افتراضية
    if (educationLevels.length === 0) {
        console.log('لا يوجد تحصيل دراسي، استخدام البيانات الافتراضية');
        educationLevels = [
            { id: 1, name: 'دبلوم' },
            { id: 2, name: 'بكالوريوس' },
            { id: 3, name: 'ماجستير' },
            { id: 4, name: 'دكتوراه' }
        ];
        // حفظ البيانات الافتراضية في التخزين المحلي
        localStorage.setItem('educationLevels', JSON.stringify(educationLevels));
    }

    // تحميل جدول الرواتب من التخزين المحلي
    let salaryScale = JSON.parse(localStorage.getItem('salaryScale'));
    let salaryTable = {};
    console.log('جدول الرواتب المحمل:', salaryScale);

    if (salaryScale && salaryScale.grades) {
        console.log('تحويل بيانات سلم الرواتب إلى الشكل المطلوب');
        // تحويل بيانات سلم الرواتب إلى الشكل المطلوب
        salaryScale.grades.forEach(grade => {
            salaryTable[grade.grade] = {};
            grade.salaries.forEach((salary, index) => {
                salaryTable[grade.grade][index + 1] = salary * 1000; // تحويل إلى آلاف الدنانير
            });
        });
    } else {
        console.log('لا يوجد سلم رواتب، استخدام البيانات الافتراضية');
        // إذا لم يكن هناك سلم رواتب، استخدم بيانات افتراضية
        salaryTable = {
            1: { // الدرجة الأولى
                1: 910000, 2: 930000, 3: 950000, 4: 970000, 5: 990000, 6: 1010000, 7: 1030000, 8: 1050000, 9: 1070000, 10: 1090000, 11: 1110000
            },
            2: { // الدرجة الثانية
                1: 773000, 2: 790000, 3: 807000, 4: 824000, 5: 841000, 6: 858000, 7: 875000, 8: 892000, 9: 909000, 10: 926000, 11: 943000
            },
            3: { // الدرجة الثالثة
                1: 600000, 2: 610000, 3: 620000, 4: 630000, 5: 640000, 6: 650000, 7: 660000, 8: 670000, 9: 680000, 10: 690000, 11: 700000
            },
            4: { // الدرجة الرابعة
                1: 509000, 2: 517000, 3: 525000, 4: 533000, 5: 541000, 6: 549000, 7: 557000, 8: 565000, 9: 573000, 10: 581000, 11: 589000
            },
            5: { // الدرجة الخامسة
                1: 429000, 2: 435000, 3: 441000, 4: 447000, 5: 453000, 6: 459000, 7: 465000, 8: 471000, 9: 477000, 10: 483000, 11: 489000
            },
            6: { // الدرجة السادسة
                1: 362000, 2: 368000, 3: 374000, 4: 380000, 5: 386000, 6: 392000, 7: 398000, 8: 404000, 9: 410000, 10: 416000, 11: 422000
            },
            7: { // الدرجة السابعة
                1: 296000, 2: 302000, 3: 308000, 4: 314000, 5: 320000, 6: 326000, 7: 332000, 8: 338000, 9: 344000, 10: 350000, 11: 356000
            },
            8: { // الدرجة الثامنة
                1: 260000, 2: 263000, 3: 266000, 4: 269000, 5: 272000, 6: 275000, 7: 278000, 8: 281000, 9: 284000, 10: 287000, 11: 290000
            },
            9: { // الدرجة التاسعة
                1: 210000, 2: 213000, 3: 216000, 4: 219000, 5: 222000, 6: 225000, 7: 228000, 8: 231000, 9: 234000, 10: 237000, 11: 240000
            },
            10: { // الدرجة العاشرة
                1: 170000, 2: 173000, 3: 176000, 4: 179000, 5: 182000, 6: 185000, 7: 188000, 8: 191000, 9: 194000, 10: 197000, 11: 200000
            }
        };
    }

    // تخزين البيانات في متغيرات عامة للوصول إليها لاحقاً
    window.appData = {
        workLocations: workLocations,
        jobTitles: jobTitles,
        educationLevels: educationLevels,
        salaryTable: salaryTable,
        lastUpdate: new Date().toISOString() // إضافة وقت آخر تحديث
    };
    console.log('تم تخزين البيانات في window.appData:', window.appData);

    // ملء القوائم المنسدلة
    console.log('ملء القوائم المنسدلة...');
    populateSelectOptions('workLocation', workLocations);
    populateSelectOptions('education', educationLevels);

    // تحديث العناوين الوظيفية بناءً على الوصف الوظيفي المحدد
    console.log('تحديث العناوين الوظيفية...');
    const jobDescriptionSelect = document.getElementById('jobDescription');
    if (jobDescriptionSelect && jobDescriptionSelect.value) {
        console.log('الوصف الوظيفي المحدد:', jobDescriptionSelect.value);
        updateJobTitles();
    } else {
        console.log('لم يتم تحديد وصف وظيفي، سيتم تحديثه عند الاختيار');
    }

    // إرجاع البيانات المحدثة
    return window.appData;
}

// ملء القوائم المنسدلة بالخيارات
function populateSelectOptions(selectId, options) {
    const selectElement = document.getElementById(selectId);
    if (!selectElement) {
        console.error(`عنصر القائمة المنسدلة غير موجود: ${selectId}`);
        return;
    }

    console.log(`ملء القائمة المنسدلة ${selectId} بـ ${options.length} خيارات`);

    // الاحتفاظ بالخيار الأول (الافتراضي)
    const defaultOption = selectElement.options[0];
    selectElement.innerHTML = '';
    selectElement.appendChild(defaultOption);

    // إضافة الخيارات
    options.forEach(option => {
        const optionElement = document.createElement('option');
        optionElement.value = option.id;
        optionElement.textContent = option.name;
        selectElement.appendChild(optionElement);
        console.log(`تمت إضافة خيار: ${option.name} (${option.id})`);
    });

    // تحديث Select2 إذا كان موجوداً
    if (typeof jQuery !== 'undefined' && typeof jQuery.fn.select2 !== 'undefined') {
        try {
            jQuery(`#${selectId}`).trigger('change');
        } catch (error) {
            console.error(`خطأ في تحديث Select2 للقائمة ${selectId}:`, error);
        }
    }
}

// تحديث العناوين الوظيفية بناءً على الوصف الوظيفي
function updateJobTitles() {
    console.log('تحديث العناوين الوظيفية...');

    const jobDescriptionSelect = document.getElementById('jobDescription');
    const jobTitleSelect = document.getElementById('jobTitle');

    if (!jobDescriptionSelect || !jobTitleSelect) {
        console.error('لا يمكن تحديث العناوين الوظيفية: العناصر غير متوفرة');
        return;
    }

    const selectedJobDescription = jobDescriptionSelect.value;
    console.log('الوصف الوظيفي المحدد:', selectedJobDescription);

    if (!selectedJobDescription) {
        console.warn('لم يتم تحديد وصف وظيفي');
        return;
    }

    // حفظ القيمة الحالية للعنوان الوظيفي
    const currentJobTitleValue = jobTitleSelect.value;
    console.log('القيمة الحالية للعنوان الوظيفي:', currentJobTitleValue);

    // استخدام وظيفة تحديث قائمة العناوين الوظيفية
    if (typeof updateJobTitlesList === 'function') {
        updateJobTitlesList(selectedJobDescription);

        // إذا كانت هناك قيمة محددة سابقاً، حاول إعادة تحديدها
        if (currentJobTitleValue) {
            setTimeout(() => {
                // التحقق من وجود القيمة في القائمة الجديدة
                let optionExists = false;
                for (let i = 0; i < jobTitleSelect.options.length; i++) {
                    if (jobTitleSelect.options[i].value == currentJobTitleValue) {
                        optionExists = true;
                        break;
                    }
                }

                if (optionExists) {
                    // إعادة تحديد القيمة
                    jobTitleSelect.value = currentJobTitleValue;
                    console.log('تم إعادة تحديد العنوان الوظيفي:', currentJobTitleValue);

                    // تحديث Select2 إذا كان موجوداً
                    if (typeof jQuery !== 'undefined' && typeof jQuery.fn.select2 !== 'undefined') {
                        jQuery('#jobTitle').val(currentJobTitleValue).trigger('change');
                    }
                } else {
                    console.log('القيمة السابقة للعنوان الوظيفي لم تعد موجودة في القائمة الجديدة');
                }
            }, 100);
        }
    } else {
        console.error('وظيفة updateJobTitlesList غير متوفرة');
    }

    // تحديث العنوان الوظيفي الجديد
    setTimeout(() => {
        if (typeof updateNewJobTitle === 'function') {
            // تحقق مما إذا كان حقل العنوان الوظيفي الجديد فارغًا قبل التحديث التلقائي
            const newJobTitleElement = document.getElementById('newJobTitle');
            if (newJobTitleElement && !newJobTitleElement.value.trim()) {
                updateNewJobTitle();
            }
        }
    }, 300);
}

// حساب تاريخ الإحالة على التقاعد
function calculateRetirementDate() {
    const birthDateInput = document.getElementById('birthDate');
    const retirementDateInput = document.getElementById('retirementDate');

    if (!birthDateInput || !retirementDateInput) return;

    const birthDate = new Date(birthDateInput.value);
    if (isNaN(birthDate.getTime())) return;

    // حساب تاريخ التقاعد (60 سنة من تاريخ الميلاد)
    const retirementDate = new Date(birthDate);
    retirementDate.setFullYear(birthDate.getFullYear() + 60);

    // تنسيق التاريخ بتنسيق YYYY-MM-DD
    const formattedDate = retirementDate.toISOString().split('T')[0];
    retirementDateInput.value = formattedDate;
}

// حساب تاريخ الاستحقاق الجديد بناءً على العلاوة
function calculateNextAllowanceDate() {
    const currentDueDateInput = document.getElementById('currentDueDate'); // تاريخ الاستحقاق الحالي
    const newDueDateInput = document.getElementById('newDueDate'); // تاريخ الاستحقاق الجديد
    const allowanceSeniorityInput = document.getElementById('allowanceSeniority'); // قدم العلاوة

    if (!currentDueDateInput || !newDueDateInput) return;

    const currentDueDate = new Date(currentDueDateInput.value);
    if (isNaN(currentDueDate.getTime())) return;

    // الحصول على قيمة قدم العلاوة (بالأشهر)
    let allowanceSeniority = 0;
    if (allowanceSeniorityInput && allowanceSeniorityInput.value) {
        allowanceSeniority = parseInt(allowanceSeniorityInput.value);
        if (isNaN(allowanceSeniority)) allowanceSeniority = 0;
    }

    // حساب تاريخ الاستحقاق الجديد (سنة واحدة من تاريخ الاستحقاق الحالي)
    const newDueDate = new Date(currentDueDate);
    newDueDate.setFullYear(currentDueDate.getFullYear() + 1);

    // تطبيق قدم العلاوة (تقديم التاريخ بعدد أشهر قدم العلاوة)
    if (allowanceSeniority > 0) {
        newDueDate.setMonth(newDueDate.getMonth() - allowanceSeniority);
        console.log(`تم تقديم تاريخ الاستحقاق الجديد بمقدار ${allowanceSeniority} شهر (قدم العلاوة)`);
    }

    // تنسيق التاريخ بتنسيق YYYY-MM-DD
    const formattedDate = newDueDate.toISOString().split('T')[0];
    newDueDateInput.value = formattedDate;

    console.log(`تم حساب تاريخ الاستحقاق الجديد بناءً على العلاوة: ${formattedDate} (قدم العلاوة: ${allowanceSeniority} شهر)`);

    // مقارنة مع تاريخ الترفيع القادم واختيار الأقرب
    compareAndSelectEarlierDate();
}

// حساب تاريخ الترفيع القادم
function calculateNextPromotionDate() {
    const lastPromotionDateInput = document.getElementById('lastPromotionDate');
    const nextPromotionDateInput = document.getElementById('nextPromotionDate');
    const newDueDateInput = document.getElementById('newDueDate');
    const currentDegreeInput = document.getElementById('currentDegree');
    const promotionSeniorityInput = document.getElementById('promotionSeniority');

    if (!lastPromotionDateInput || !nextPromotionDateInput || !currentDegreeInput || !newDueDateInput) return;

    const lastPromotionDate = new Date(lastPromotionDateInput.value);
    if (isNaN(lastPromotionDate.getTime())) return;

    const currentDegree = parseInt(currentDegreeInput.value);
    if (isNaN(currentDegree)) return;

    // الحصول على قيمة قدم الترفيع (بالأشهر)
    let promotionSeniority = 0;
    if (promotionSeniorityInput && promotionSeniorityInput.value) {
        promotionSeniority = parseInt(promotionSeniorityInput.value);
        if (isNaN(promotionSeniority)) promotionSeniority = 0;
    }

    // تحديد عدد السنوات للترفيع بناءً على الدرجة
    let yearsToAdd = 4; // افتراضي للدرجات من 6 إلى 10
    if (currentDegree >= 1 && currentDegree <= 5) {
        yearsToAdd = 5; // للدرجات من 1 إلى 5
    }

    // حساب تاريخ الترفيع القادم
    const nextPromotionDate = new Date(lastPromotionDate);
    nextPromotionDate.setFullYear(lastPromotionDate.getFullYear() + yearsToAdd);

    // تطبيق قدم الترفيع (تقديم التاريخ بعدد أشهر قدم الترفيع)
    if (promotionSeniority > 0) {
        nextPromotionDate.setMonth(nextPromotionDate.getMonth() - promotionSeniority);
        console.log(`تم تقديم تاريخ الترفيع القادم بمقدار ${promotionSeniority} شهر (قدم الترفيع)`);
    }

    // تنسيق التاريخ بتنسيق YYYY-MM-DD
    const formattedDate = nextPromotionDate.toISOString().split('T')[0];
    nextPromotionDateInput.value = formattedDate;

    console.log(`تم حساب تاريخ الترفيع القادم: ${formattedDate} (قدم الترفيع: ${promotionSeniority} شهر)`);

    // تحديث تاريخ الاستحقاق الجديد إذا كان تاريخ الترفيع القادم أقرب
    compareAndSelectEarlierDate();
}

// مقارنة تاريخ العلاوة وتاريخ الترفيع واختيار الأقرب
function compareAndSelectEarlierDate() {
    const newDueDateInput = document.getElementById('newDueDate');
    const nextPromotionDateInput = document.getElementById('nextPromotionDate');

    if (!newDueDateInput || !nextPromotionDateInput) return;

    // الحصول على تاريخ الاستحقاق الجديد المحسوب من العلاوة
    const allowanceBasedDate = new Date(newDueDateInput.value);

    // الحصول على تاريخ الترفيع القادم
    const nextPromotionDate = new Date(nextPromotionDateInput.value);

    // التحقق من صحة التواريخ
    if (isNaN(allowanceBasedDate.getTime()) || isNaN(nextPromotionDate.getTime())) return;

    // اختيار التاريخ الأقرب (الأصغر)
    if (nextPromotionDate < allowanceBasedDate) {
        // إذا كان تاريخ الترفيع القادم أقرب، استخدمه كتاريخ استحقاق جديد
        const formattedDate = nextPromotionDate.toISOString().split('T')[0];
        newDueDateInput.value = formattedDate;
        console.log(`تم تحديث تاريخ الاستحقاق الجديد ليكون تاريخ الترفيع القادم: ${formattedDate}`);
    } else {
        console.log(`تاريخ الاستحقاق الجديد المحسوب من العلاوة هو الأقرب: ${allowanceBasedDate.toISOString().split('T')[0]}`);
    }
}

// حساب الراتب بناءً على الدرجة والمرحلة
function calculateSalary() {
    const currentDegreeInput = document.getElementById('currentDegree');
    const currentStageInput = document.getElementById('currentStage');
    const currentSalaryInput = document.getElementById('currentSalary');

    if (!currentDegreeInput || !currentStageInput || !currentSalaryInput || !window.appData) return;

    const currentDegree = parseInt(currentDegreeInput.value);
    const currentStage = parseInt(currentStageInput.value);

    if (isNaN(currentDegree) || isNaN(currentStage)) return;

    // الحصول على الراتب من جدول الرواتب
    const salaryTable = window.appData.salaryTable;
    if (salaryTable[currentDegree] && salaryTable[currentDegree][currentStage]) {
        const salary = salaryTable[currentDegree][currentStage];
        currentSalaryInput.value = salary;
    } else {
        currentSalaryInput.value = 'غير متوفر';
    }
}

// تحديث الدرجة والمرحلة الجديدة
function updateNewDegreeAndStage() {
    const currentDegreeInput = document.getElementById('currentDegree');
    const currentStageInput = document.getElementById('currentStage');
    const newDegreeInput = document.getElementById('newDegree');
    const newStageInput = document.getElementById('newStage');
    const newSalaryInput = document.getElementById('newSalary');
    const newDueDateInput = document.getElementById('newDueDate');

    if (!currentDegreeInput || !currentStageInput || !newDegreeInput || !newStageInput || !newSalaryInput || !window.appData) return;

    const currentDegree = parseInt(currentDegreeInput.value);
    const currentStage = parseInt(currentStageInput.value);

    if (isNaN(currentDegree) || isNaN(currentStage)) return;

    let newDegree = currentDegree;
    let newStage = currentStage + 1;

    // التحقق من الحد الأقصى للمراحل في الدرجة الحالية
    const maxStages = {
        1: 11, 2: 11, 3: 11, 4: 11, 5: 11, 6: 11, 7: 11, 8: 11, 9: 11, 10: 11
    };

    // إذا وصلنا إلى الحد الأقصى للمراحل، ننتقل إلى الدرجة التالية
    if (newStage > maxStages[currentDegree]) {
        newDegree = currentDegree - 1;
        newStage = 1;
    }

    // التأكد من أن الدرجة ضمن النطاق المسموح
    if (newDegree < 1) {
        newDegree = 1;
        newStage = 1;
    }

    newDegreeInput.value = newDegree;
    newStageInput.value = newStage;

    // حساب الراتب الجديد
    const salaryTable = window.appData.salaryTable;
    if (salaryTable[newDegree] && salaryTable[newDegree][newStage]) {
        const salary = salaryTable[newDegree][newStage];
        newSalaryInput.value = salary;
    } else {
        newSalaryInput.value = 'غير متوفر';
    }

    // حساب تاريخ الاستحقاق الجديد
    calculateNextAllowanceDate(); // حساب تاريخ الاستحقاق الجديد بناءً على العلاوة
    calculateNextPromotionDate(); // حساب تاريخ الترفيع القادم
}

// حفظ بيانات الموظف
function saveEmployeeData() {
    // التحقق من صحة البيانات
    if (!validateForm()) {
        return;
    }

    // جمع بيانات الموظف من النموذج
    const employeeData = {
        id: document.getElementById('employeeId').value || Date.now(), // استخدام الوقت الحالي كمعرف إذا كان جديداً
        name: document.getElementById('fullName').value,
        gender: document.getElementById('gender').value, // الجنس
        jobDescription: document.getElementById('jobDescription').value,
        jobTitle: document.getElementById('jobTitle').value,
        currentJobTitle: document.getElementById('jobTitle').value, // نسخة للتوافق مع نظام الشكر
        workLocation: document.getElementById('workLocation').value,
        education: document.getElementById('education').value,
        educationLevel: document.getElementById('education').value, // نسخة للتوافق مع نظام الشكر
        specialization: document.getElementById('specialization').value, // التخصص
        birthDate: document.getElementById('birthDate').value,
        hireDate: document.getElementById('hireDate').value,
        currentDegree: parseInt(document.getElementById('currentDegree').value),
        currentStage: parseInt(document.getElementById('currentStage').value),
        allowanceSeniority: parseInt(document.getElementById('allowanceSeniority').value), // قدم العلاوة
        promotionSeniority: parseInt(document.getElementById('promotionSeniority').value), // قدم الترفيع
        currentSalary: document.getElementById('currentSalary').value.replace(/[^\d]/g, ''), // إزالة الأحرف غير الرقمية
        lastPromotionDate: document.getElementById('lastPromotionDate').value,
        nextPromotionDate: document.getElementById('nextPromotionDate').value,
        currentDueDate: document.getElementById('currentDueDate').value,
        newDueDate: document.getElementById('newDueDate').value,
        newJobTitle: document.getElementById('newJobTitle').value, // العنوان الوظيفي الجديد
        retirementDate: document.getElementById('retirementDate').value,
        status: 'active'
    };

    // الحصول على الموظفين الحاليين
    let employees = JSON.parse(localStorage.getItem('employees') || '[]');

    // التحقق مما إذا كان الموظف موجوداً بالفعل
    const existingEmployeeIndex = employees.findIndex(emp => emp.id == employeeData.id);

    if (existingEmployeeIndex !== -1) {
        // تحديث الموظف الموجود
        employees[existingEmployeeIndex] = employeeData;
    } else {
        // إضافة موظف جديد
        employees.push(employeeData);
    }

    // حفظ البيانات في التخزين المحلي
    localStorage.setItem('employees', JSON.stringify(employees));

    // تحديث الإحصائيات في الصفحة الرئيسية
    try {
        // إرسال حدث تغيير التخزين المحلي
        const storageEvent = new StorageEvent('storage', {
            key: 'employees',
            newValue: JSON.stringify(employees),
            oldValue: null,
            storageArea: localStorage
        });
        window.dispatchEvent(storageEvent);
    } catch (error) {
        console.error('خطأ في إرسال حدث تحديث الإحصائيات:', error);
    }

    // عرض رسالة نجاح
    alert('تم حفظ بيانات الموظف بنجاح!');

    // العودة إلى صفحة قائمة الموظفين
    window.location.href = 'employees-list.html';
}

// التحقق من صحة النموذج
function validateForm() {
    // التحقق من الحقول المطلوبة
    const requiredFields = [
        'fullName', 'gender', 'jobDescription', 'birthDate', 'hireDate',
        'currentDegree', 'currentStage', 'allowanceSeniority', 'promotionSeniority',
        'lastPromotionDate', 'currentDueDate'
    ];

    for (const fieldId of requiredFields) {
        const field = document.getElementById(fieldId);
        if (!field || !field.value.trim()) {
            alert(`يرجى ملء حقل ${field.labels[0].textContent.replace(':', '')}`);
            field.focus();
            return false;
        }
    }

    // التحقق من صحة قدم العلاوة
    const allowanceSeniority = parseInt(document.getElementById('allowanceSeniority').value);
    if (isNaN(allowanceSeniority) || allowanceSeniority < 0 || allowanceSeniority > 120) {
        alert('يرجى إدخال قيمة صحيحة لقدم العلاوة (من 0 إلى 120 شهر)');
        document.getElementById('allowanceSeniority').focus();
        return false;
    }

    // التحقق من صحة قدم الترفيع
    const promotionSeniority = parseInt(document.getElementById('promotionSeniority').value);
    if (isNaN(promotionSeniority) || promotionSeniority < 0 || promotionSeniority > 120) {
        alert('يرجى إدخال قيمة صحيحة لقدم الترفيع (من 0 إلى 120 شهر)');
        document.getElementById('promotionSeniority').focus();
        return false;
    }

    return true;
}

// ملء حقول النموذج ببيانات الموظف
function fillEmployeeForm(employee) {
    console.log("ملء حقول النموذج ببيانات الموظف:", employee);

    // تخزين العنوان الوظيفي الجديد للاستخدام لاحقاً
    const savedNewJobTitle = employee.newJobTitle;

    try {
        // ملء الحقول الأساسية
        document.getElementById('employeeId').value = employee.id;
        document.getElementById('fullName').value = employee.name;

        // ملء الجنس والتخصص
        if (employee.gender) {
            document.getElementById('gender').value = employee.gender;
        }
        if (employee.specialization) {
            document.getElementById('specialization').value = employee.specialization;
        }

        // تخزين قيم القوائم المنسدلة للاستخدام لاحقاً
        const jobDescriptionValue = employee.jobDescription;
        const jobTitleValue = employee.jobTitle;
        const workLocationValue = employee.workLocation;
        const educationValue = employee.education;

        console.log("القيم المخزنة:", {
            jobDescription: jobDescriptionValue,
            jobTitle: jobTitleValue,
            workLocation: workLocationValue,
            education: educationValue
        });

        // تعيين قيم القوائم المنسدلة الأساسية أولاً
        document.getElementById('jobDescription').value = jobDescriptionValue;
        document.getElementById('workLocation').value = workLocationValue;
        document.getElementById('education').value = educationValue;

        // تدمير جميع مثيلات Select2 أولاً
        if (typeof destroySelect2 === 'function') {
            destroySelect2();
        } else if (typeof jQuery !== 'undefined') {
            try {
                // تدمير مثيلات Select2 يدوياً
                jQuery('#jobDescription, #jobTitle, #education, #workLocation').each(function() {
                    if (jQuery(this).data('select2')) {
                        jQuery(this).select2('destroy');
                    }
                });
            } catch (error) {
                console.error('خطأ في تدمير Select2:', error);
            }
        }

        // تحديث العناوين الوظيفية بناءً على الوصف الوظيفي
        console.log('تحديث العناوين الوظيفية...');
        updateJobTitles();

        // انتظار لضمان تحديث قائمة العناوين الوظيفية
        setTimeout(() => {
            try {
                // تعيين العنوان الوظيفي
                const jobTitleSelect = document.getElementById('jobTitle');

                if (!jobTitleSelect) {
                    console.error('عنصر قائمة العناوين الوظيفية غير موجود');
                    return;
                }

                // التحقق من وجود الخيار في القائمة
                let optionExists = false;
                for (let i = 0; i < jobTitleSelect.options.length; i++) {
                    if (jobTitleSelect.options[i].value == jobTitleValue) {
                        optionExists = true;
                        break;
                    }
                }

                if (optionExists) {
                    jobTitleSelect.value = jobTitleValue;
                    console.log("تم تعيين العنوان الوظيفي:", jobTitleValue);
                } else if (jobTitleValue) {
                    console.warn(`العنوان الوظيفي ${jobTitleValue} غير موجود في القائمة. إضافته...`);

                    // الحصول على اسم العنوان الوظيفي
                    let jobTitleName = '';
                    const allJobTitles = JSON.parse(localStorage.getItem('jobTitles') || '[]');
                    const jobTitle = allJobTitles.find(title => title.id == jobTitleValue);
                    if (jobTitle) {
                        jobTitleName = jobTitle.name;
                    } else {
                        jobTitleName = 'عنوان وظيفي غير معروف';
                    }

                    // إضافة الخيار إلى القائمة
                    const option = document.createElement('option');
                    option.value = jobTitleValue;
                    option.textContent = jobTitleName;
                    jobTitleSelect.appendChild(option);

                    // تعيين القيمة
                    jobTitleSelect.value = jobTitleValue;
                    console.log("تم إضافة وتعيين العنوان الوظيفي:", jobTitleValue, jobTitleName);
                }

                // إعادة تهيئة Select2 بعد تعيين جميع القيم
                setTimeout(() => {
                    console.log('إعادة تهيئة Select2 بعد تعيين القيم...');

                    // إعادة تهيئة جميع القوائم المنسدلة
                    if (typeof initializeSelect2 === 'function') {
                        initializeSelect2();
                        console.log('تم استدعاء initializeSelect2');

                        // إضافة مستمعي الأحداث
                        if (typeof addSelect2EventListeners === 'function') {
                            addSelect2EventListeners();
                            console.log('تم استدعاء addSelect2EventListeners');
                        }

                        // تأخير إضافي لضمان اكتمال التهيئة
                        setTimeout(() => {
                            console.log('تحديث القيم المحددة بعد التهيئة...');

                            // تحديث القيم المحددة
                            if (typeof updateSelect2Value === 'function') {
                                if (jobDescriptionValue) updateSelect2Value('#jobDescription', jobDescriptionValue);
                                if (jobTitleValue) updateSelect2Value('#jobTitle', jobTitleValue);
                                if (workLocationValue) updateSelect2Value('#workLocation', workLocationValue);
                                if (educationValue) updateSelect2Value('#education', educationValue);
                                console.log('تم تحديث قيم Select2');
                            } else if (typeof jQuery !== 'undefined') {
                                // تحديث القيم يدوياً إذا لم تكن الوظيفة متوفرة
                                if (jobDescriptionValue) jQuery('#jobDescription').val(jobDescriptionValue).trigger('change');
                                if (jobTitleValue) jQuery('#jobTitle').val(jobTitleValue).trigger('change');
                                if (workLocationValue) jQuery('#workLocation').val(workLocationValue).trigger('change');
                                if (educationValue) jQuery('#education').val(educationValue).trigger('change');
                                console.log('تم تحديث قيم Select2 يدوياً');
                            }

                            // تحديث العنوان الوظيفي الجديد
                            setTimeout(() => {
                                const newJobTitleElement = document.getElementById('newJobTitle');

                                // إذا كان هناك عنوان وظيفي جديد محفوظ، استخدمه
                                if (savedNewJobTitle && newJobTitleElement) {
                                    newJobTitleElement.value = savedNewJobTitle;
                                    console.log('تم استعادة العنوان الوظيفي الجديد المحفوظ:', savedNewJobTitle);
                                } else if (typeof updateNewJobTitle === 'function') {
                                    // تحقق مما إذا كان حقل العنوان الوظيفي الجديد فارغًا قبل التحديث التلقائي
                                    if (newJobTitleElement && !newJobTitleElement.value.trim()) {
                                        const updated = updateNewJobTitle();
                                        console.log('تم استدعاء updateNewJobTitle', updated ? '(تم استخدام العنوان الوظيفي الجديد المحدد يدوياً)' : '(تم استخدام نفس العنوان الوظيفي)');
                                    } else {
                                        console.log('تم تخطي التحديث التلقائي للعنوان الوظيفي الجديد لأنه يحتوي على قيمة بالفعل');
                                    }
                                }
                            }, 200);
                        }, 300);
                    } else {
                        console.error('وظيفة initializeSelect2 غير متوفرة');
                    }
                }, 300);
            } catch (error) {
                console.error("خطأ في تعيين العنوان الوظيفي:", error);
            }
        }, 500);
        document.getElementById('birthDate').value = employee.birthDate;
        document.getElementById('hireDate').value = employee.hireDate;
        document.getElementById('currentDegree').value = employee.currentDegree;
        document.getElementById('currentStage').value = employee.currentStage;

        // ملء حقول القدم
        if (employee.allowanceSeniority !== undefined) {
            document.getElementById('allowanceSeniority').value = employee.allowanceSeniority;
        } else if (employee.seniority !== undefined) {
            // للتوافق مع البيانات القديمة
            document.getElementById('allowanceSeniority').value = employee.seniority;
        } else {
            document.getElementById('allowanceSeniority').value = '0';
        }

        if (employee.promotionSeniority !== undefined) {
            document.getElementById('promotionSeniority').value = employee.promotionSeniority;
        } else if (employee.seniority !== undefined) {
            // للتوافق مع البيانات القديمة
            document.getElementById('promotionSeniority').value = employee.seniority;
        } else {
            document.getElementById('promotionSeniority').value = '0';
        }

        // تعيين الراتب
        if (employee.currentSalary) {
            document.getElementById('currentSalary').value = employee.currentSalary;
        }

        // ملء حقول التواريخ
        if (employee.lastPromotionDate) {
            document.getElementById('lastPromotionDate').value = employee.lastPromotionDate;
        }

        if (employee.nextPromotionDate) {
            document.getElementById('nextPromotionDate').value = employee.nextPromotionDate;
        }

        if (employee.retirementDate) {
            document.getElementById('retirementDate').value = employee.retirementDate;
        }

        // ملء حقول تاريخ الاستحقاق
        if (employee.currentDueDate) {
            document.getElementById('currentDueDate').value = employee.currentDueDate;
        } else if (employee.lastPromotionDate) {
            // إذا لم يكن هناك تاريخ استحقاق حالي، استخدم تاريخ آخر ترفيع
            document.getElementById('currentDueDate').value = employee.lastPromotionDate;
        }

        if (employee.newDueDate) {
            document.getElementById('newDueDate').value = employee.newDueDate;
        } else {
            // إذا لم يكن هناك تاريخ استحقاق جديد، قم بحسابه
            calculateNextAllowanceDate(); // حساب تاريخ الاستحقاق الجديد بناءً على العلاوة
            calculateNextPromotionDate(); // حساب تاريخ الترفيع القادم
        }

        // تحديث الدرجة والمرحلة الجديدة
        updateNewDegreeAndStage();

        console.log("تم ملء النموذج بنجاح");
    } catch (error) {
        console.error("خطأ في ملء النموذج:", error);
        alert('حدث خطأ في ملء النموذج: ' + error.message);
    }
}

function loadEmployeeData(employeeId) {
    try {
        const employees = JSON.parse(localStorage.getItem('employees') || '[]');
        const employee = employees.find(emp => emp.id === employeeId);
        
        if (employee) {
            // تعبئة البيانات الأساسية
            document.getElementById('employeeId').value = employee.id || '';
            document.getElementById('fullName').value = employee.name || '';
            document.getElementById('currentSalary').value = employee.currentSalary || '';
            document.getElementById('retirementDate').value = employee.retirementDate || '';
            // ... existing code ...
        }
    } catch (error) {
        showError('حدث خطأ في تحميل بيانات الموظف: ' + error.message);
    }
}

function saveEmployee(event) {
    event.preventDefault();
    try {
        const employeeData = {
            id: document.getElementById('employeeId').value,
            fullName: document.getElementById('fullName').value,
            currentSalary: Number(document.getElementById('currentSalary').value),
            retirementDate: document.getElementById('retirementDate').value,
            // ... existing code ...
        };

        // التحقق من صحة البيانات
        if (!employeeData.currentSalary || employeeData.currentSalary <= 0) {
            throw new Error('الرجاء إدخال راتب صحيح');
        }

        if (!employeeData.retirementDate) {
            throw new Error('الرجاء إدخال تاريخ الإحالة على التقاعد');
        }

        // حفظ البيانات
        const employees = JSON.parse(localStorage.getItem('employees') || '[]');
        const index = employees.findIndex(emp => emp.id === employeeData.id);
        
        if (index !== -1) {
            employees[index] = { ...employees[index], ...employeeData };
        } else {
            employees.push(employeeData);
        }

        localStorage.setItem('employees', JSON.stringify(employees));
        showSuccess('تم حفظ بيانات الموظف بنجاح');
        
        // تحديث البيانات التعريفية
        extractProfileDataFromEmployees();
        
    } catch (error) {
        showError('حدث خطأ في حفظ البيانات: ' + error.message);
    }
}
